import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';



const customFetch = async (input: RequestInfo | URL, init?: RequestInit): Promise<Response> => {
  const isProduction = process.env.NODE_ENV === 'production';
  const apiUrl = process.env.REACT_APP_API_URL;
  const initOptions = init || {};

  let urlString: string;
  if (typeof input === 'string') {
    urlString = input;
  } else if (input instanceof URL) {
    urlString = input.href;
  } else if (input instanceof Request) {
    urlString = input.url;
  } else {
    urlString = String(input);
  }

  if (isProduction) {
    const enhancedInit: RequestInit = {
      ...initOptions,
      mode: 'cors',
      credentials: 'same-origin' as RequestCredentials,
    };
    
    return fetch(input, enhancedInit);
  } else {
    if (!apiUrl) {
      return fetch(input, initOptions);
    }

    if (typeof input === 'string') {
      const developmentUrl = input.replace(apiUrl, '/api');
      return fetch(developmentUrl, initOptions);
    } else if (input instanceof Request) {
      const originalUrl = input.url;
      const developmentUrl = originalUrl.replace(apiUrl, '/api');
      const newRequest = new Request(developmentUrl, input);
      return fetch(newRequest, initOptions);
    } else {
      const originalUrl = input instanceof URL ? input.href : String(input);
      const developmentUrl = originalUrl.replace(apiUrl, '/api');
      return fetch(developmentUrl, initOptions);
    }
  }
};

const productionFetch = async (url: string, options: RequestInit) => {
  const token = localStorage.getItem('Token');
  const headers = new Headers();
  headers.set('Content-Type', 'application/json');
  if (token) headers.set('Authorization', `Bearer ${token}`);
  
  const fetchOptions = {
    ...options,
    headers,
    mode: 'cors' as RequestMode,
    credentials: 'same-origin' as RequestCredentials
  };
  
  return fetch(url, fetchOptions);
};

const baseQueryWithoutCache = fetchBaseQuery({
  baseUrl: process.env.REACT_APP_API_URL,
  prepareHeaders: (headers) => {
    const token = localStorage.getItem('Token');
    if (token) headers.set('Authorization', `Bearer ${token}`);
    headers.set('Content-Type', 'application/json');
    return headers;
  },
  fetchFn: (input, init) => {
    if (process.env.NODE_ENV === 'production') {
      const url = typeof input === 'string' ? input : input instanceof URL ? input.href : input.url;
      return productionFetch(url, init || {});
    }
    return customFetch(input, init);
  }
});

export const api = createApi({
  reducerPath: 'api',
  baseQuery: baseQueryWithoutCache,
  tagTypes: ['Orders', 'CashApp', 'Analytics'],
  endpoints: (builder) => ({
    getUserOrders: builder.query({
      query: ({ userId, page, limit }) => `/orders/user/${userId}?page=${page}&limit=${limit}`,
      providesTags: ['Orders'],
      serializeQueryArgs: ({ endpointName }) => {
        return endpointName;
      },
      merge: (currentCache, newData, { arg }) => {
        if (currentCache && newData) {
          if (arg.page === 1) {
            return newData;
          }
          return {
            ...newData,
            data: [...currentCache.data, ...newData.data],
          };
        }
        return newData;
      },
      forceRefetch({ currentArg, previousArg }) {
        return (
          currentArg?.page !== previousArg?.page ||
          currentArg?.limit !== previousArg?.limit ||
          currentArg?.userId !== previousArg?.userId
        );
      },
    }),

    getCashAppList: builder.query({
      query: () => '/cash-app',
      providesTags: (result) => 
        result 
          ? [...result.map(({ id }: { id: string }) => ({ type: 'CashApp' as const, id })), { type: 'CashApp' as const, id: 'LIST' }]
          : [{ type: 'CashApp' as const, id: 'LIST' }],
    }),
    
    addCashApp: builder.mutation({
      query: (data) => ({
        url: '/cash-app/add',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: [{ type: 'CashApp', id: 'LIST' }],
      async onQueryStarted(data, { dispatch, queryFulfilled, getState }) {
        try {
          const { data: response } = await queryFulfilled;
        } catch {
        }
      },
    }),
    
    updateCashAppStatus: builder.mutation({
      query: ({ id, status }) => ({
        url: `/cash-app-status/${id}`,
        method: 'PUT',
        body: { status },
      }),
      invalidatesTags: (result: any, error: any, { id }: any) => [
        { type: 'CashApp', id },
        { type: 'CashApp', id: 'LIST' }
      ],
      async onQueryStarted({ id, status }, { dispatch, queryFulfilled, getState }) {
        const getCashAppListResult = api.endpoints.getCashAppList.select(undefined)(getState());
        
        if (getCashAppListResult?.data) {
          const optimisticPatch = dispatch(
            api.util.updateQueryData('getCashAppList', undefined, (draft) => {
              const item = draft.find((item: { id: string | number, status: string }) => item.id === id);
              if (item) {
                item.status = status;
              }
            })
          );
          
          try {
            await queryFulfilled;
          } catch {
            optimisticPatch.undo();
          }
        }
      },
    }),

    getAnalytics: builder.query({
      query: (params) => ({
        url: '/analytics',
        params,
      }),
      providesTags: [{ type: 'Analytics', id: 'LIST' }],
    }),

    getOrdersWithFilter: builder.query({
      query: ({ userId, page, limit, paymentMethod }: any) => {
        if (!userId) return '';
        
        const queryParams = new URLSearchParams({
          page: page.toString(),
          limit: limit.toString(),
          ...(paymentMethod && { paymentMethod }),
        }).toString();
        return `/orders/user/${userId}?${queryParams}`;
      },
      providesTags: ['Orders'],
      serializeQueryArgs: ({ endpointName, queryArgs }) => {
        return `${endpointName}-${queryArgs.userId}-${queryArgs.paymentMethod}`;
      },
      merge: (currentCache, newData, { arg }) => {
        if (currentCache && newData) {
          if (arg.page === 1) {
            return newData;
          }
          return {
            ...newData,
            data: [...currentCache.data, ...newData.data],
          };
        }
        return newData;
      },
      forceRefetch({ currentArg, previousArg }) {
        return (
          currentArg?.page !== previousArg?.page ||
          currentArg?.limit !== previousArg?.limit ||
          currentArg?.userId !== previousArg?.userId ||
          currentArg?.paymentMethod !== previousArg?.paymentMethod
        );
      },
    }),
  }),
});

export const {
  useGetUserOrdersQuery,
  useGetOrdersWithFilterQuery,
  useGetCashAppListQuery,
  useAddCashAppMutation,
  useUpdateCashAppStatusMutation,
  useGetAnalyticsQuery,
} = api;
