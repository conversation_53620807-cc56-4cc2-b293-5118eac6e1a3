import React, { useState } from 'react';
import { GameFilters } from '../types/reports.types';

interface GameTransactionsSectionProps {
  gameFilters: GameFilters;
  onFilterChange: (key: keyof GameFilters, value: any) => void;
  totalTransactions: number;
  totalPurchaseAmount: number;
  totalRedeemAmount: number;
}

const GameTransactionsSection: React.FC<GameTransactionsSectionProps> = ({
  gameFilters,
  onFilterChange,
  totalTransactions,
  totalPurchaseAmount,
  totalRedeemAmount
}) => {
  const [showGameFilters, setShowGameFilters] = useState<boolean>(false);
  const [showGameDatePicker, setShowGameDatePicker] = useState<boolean>(false);

  const handleApplyFilters = () => {
    setShowGameFilters(false);
    // Filters are automatically applied through onFilterChange
  };

  const handleClearFilters = () => {
    onFilterChange('search', '');
    onFilterChange('gameName', '');
    onFilterChange('userSearch', '');
    onFilterChange('status', '');
    onFilterChange('dateFilter', { startDate: null, endDate: null });
    onFilterChange('amountFilter', { min: '', max: '' });
    setShowGameFilters(false);
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-800 mb-2">Game Transaction Analytics</h3>
          <p className="text-sm text-gray-600">Track game purchases and redemptions with detailed filtering</p>
        </div>
        
        <button
          onClick={() => setShowGameFilters(!showGameFilters)}
          className="mt-4 md:mt-0 px-4 py-2 bg-[#4D7C0F] text-white rounded-lg hover:bg-opacity-90 transition-colors flex items-center gap-2"
        >
          <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z" />
          </svg>
          Filter
        </button>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-blue-600 font-medium">Total Transactions</p>
              <p className="text-2xl font-bold text-blue-800">{totalTransactions.toLocaleString()}</p>
            </div>
            <div className="p-3 bg-blue-100 rounded-full">
              <svg className="w-6 h-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-green-50 p-4 rounded-lg border border-green-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-green-600 font-medium">Total Purchases</p>
              <p className="text-2xl font-bold text-green-800">${totalPurchaseAmount.toLocaleString()}</p>
            </div>
            <div className="p-3 bg-green-100 rounded-full">
              <svg className="w-6 h-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-orange-50 p-4 rounded-lg border border-orange-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-orange-600 font-medium">Total Redeems</p>
              <p className="text-2xl font-bold text-orange-800">${totalRedeemAmount.toLocaleString()}</p>
            </div>
            <div className="p-3 bg-orange-100 rounded-full">
              <svg className="w-6 h-6 text-orange-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Filters Panel */}
      {showGameFilters && (
        <div className="bg-gray-50 rounded-lg p-4 mb-6 border border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            {/* Search Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Search</label>
              <input
                type="text"
                value={gameFilters.search}
                onChange={(e) => onFilterChange('search', e.target.value)}
                placeholder="Search transactions..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#4D7C0F] focus:border-transparent"
              />
            </div>

            {/* Game Name Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Game Name</label>
              <input
                type="text"
                value={gameFilters.gameName}
                onChange={(e) => onFilterChange('gameName', e.target.value)}
                placeholder="Filter by game..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#4D7C0F] focus:border-transparent"
              />
            </div>

            {/* User Search Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">User</label>
              <input
                type="text"
                value={gameFilters.userSearch}
                onChange={(e) => onFilterChange('userSearch', e.target.value)}
                placeholder="Search by user..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#4D7C0F] focus:border-transparent"
              />
            </div>

            {/* Status Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
              <select
                value={gameFilters.status}
                onChange={(e) => onFilterChange('status', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#4D7C0F] focus:border-transparent"
              >
                <option value="">All Statuses</option>
                <option value="pending">Pending</option>
                <option value="completed">Completed</option>
                <option value="failed">Failed</option>
              </select>
            </div>

            {/* Amount Range Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Amount Range</label>
              <div className="flex gap-2">
                <input
                  type="number"
                  value={gameFilters.amountFilter.min}
                  onChange={(e) => onFilterChange('amountFilter', { ...gameFilters.amountFilter, min: e.target.value })}
                  placeholder="Min"
                  className="w-1/2 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#4D7C0F] focus:border-transparent"
                />
                <input
                  type="number"
                  value={gameFilters.amountFilter.max}
                  onChange={(e) => onFilterChange('amountFilter', { ...gameFilters.amountFilter, max: e.target.value })}
                  placeholder="Max"
                  className="w-1/2 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#4D7C0F] focus:border-transparent"
                />
              </div>
            </div>

            {/* Date Range Filter */}
            <div className="relative">
              <label className="block text-sm font-medium text-gray-700 mb-1">Date Range</label>
              <button
                type="button"
                onClick={() => setShowGameDatePicker(!showGameDatePicker)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#4D7C0F] focus:border-transparent text-left bg-white hover:bg-gray-50 transition-colors flex items-center justify-between"
              >
                <span className="text-gray-700">
                  {gameFilters.dateFilter.startDate && gameFilters.dateFilter.endDate
                    ? `${gameFilters.dateFilter.startDate.toLocaleDateString()} - ${gameFilters.dateFilter.endDate.toLocaleDateString()}`
                    : 'Select date range'
                  }
                </span>
                <svg className="w-4 h-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </button>
              
              {/* Date Range Picker Modal */}
              {showGameDatePicker && (
                <div className="absolute top-full left-0 mt-1 z-50 bg-white shadow-lg rounded-lg border border-gray-200 p-4 min-w-[280px]">
                  <div className="mb-3">
                    <label className="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                    <input
                      type="date"
                      value={gameFilters.dateFilter.startDate ? gameFilters.dateFilter.startDate.toISOString().split('T')[0] : ''}
                      onChange={(e) => onFilterChange('dateFilter', {
                        ...gameFilters.dateFilter,
                        startDate: e.target.value ? new Date(e.target.value) : null
                      })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#4D7C0F] focus:border-transparent"
                    />
                  </div>
                  <div className="mb-3">
                    <label className="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                    <input
                      type="date"
                      value={gameFilters.dateFilter.endDate ? gameFilters.dateFilter.endDate.toISOString().split('T')[0] : ''}
                      onChange={(e) => onFilterChange('dateFilter', {
                        ...gameFilters.dateFilter,
                        endDate: e.target.value ? new Date(e.target.value) : null
                      })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#4D7C0F] focus:border-transparent"
                    />
                  </div>
                  <div className="flex gap-2">
                    <button
                      onClick={() => {
                        onFilterChange('dateFilter', { startDate: null, endDate: null });
                        setShowGameDatePicker(false);
                      }}
                      className="flex-1 px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors text-sm"
                    >
                      Clear
                    </button>
                    <button
                      onClick={() => setShowGameDatePicker(false)}
                      className="flex-1 px-3 py-2 bg-[#4D7C0F] text-white rounded-lg hover:bg-opacity-90 transition-colors text-sm"
                    >
                      Apply
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Filter Actions */}
          <div className="flex gap-2 justify-end">
            <button
              onClick={handleClearFilters}
              className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
            >
              Clear All
            </button>
            <button
              onClick={handleApplyFilters}
              className="px-4 py-2 bg-[#4D7C0F] text-white rounded-lg hover:bg-opacity-90 transition-colors"
            >
              Apply Filters
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default GameTransactionsSection;
