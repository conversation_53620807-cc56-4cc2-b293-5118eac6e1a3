const mongoose = require("mongoose");

const gameTransactionSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: "User",
  },
  gameId: {
    type: String,
    required: true,
  },
  gameName: {
    type: String,
    required: true,
  },
  transactionType: {
    type: String,
    enum: ["purchase", "redeem"],
    required: true,
  },
  amount: {
    type: Number,
    required: true,
  },
  status: {
    type: String,
    enum: ["pending", "completed", "failed"],
    default: "pending",
  },
  gameInfo: {
    type: mongoose.Schema.Types.Mixed,
    required: true,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
});

module.exports = gameTransactionSchema;
