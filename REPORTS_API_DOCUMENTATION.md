# Reports API Documentation

## Overview

The Reports API provides comprehensive analytics and reporting functionality for the LuckShack platform. It analyzes Order documents to distinguish between purchases (card/crypto payments) and redeems (push-to-card payments), providing detailed insights into business metrics.

## Key Features

- **Purchase Analytics**: Track card and crypto payments
- **Redeem Analytics**: Track push-to-card transactions
- **Wallet Balance Reports**: Monitor sweeps coin balances
- **Flexible Grouping**: Group data by date, game, or user
- **Date Range Filtering**: Filter reports by custom date ranges
- **Pagination Support**: Handle large datasets efficiently
- **Role-based Access Control**: Secure access for Admin/Manage roles only

## Authentication & Authorization

All endpoints require:
- Valid JWT token in Authorization header: `Bearer <token>`
- User role must be either `Admin` or `Manage`

## Base URL

```
/reports
```

## Endpoints

### 1. Dashboard Analytics

**GET** `/reports/dashboard`

Get comprehensive dashboard analytics with summary metrics.

#### Query Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| startDate | string | No | Start date (YYYY-MM-DD format) |
| endDate | string | No | End date (YYYY-MM-DD format) |

#### Response

```json
{
  "success": true,
  "data": {
    "summary": {
      "totalPurchases": 150,
      "totalPurchaseAmount": 15000.50,
      "totalRedeems": 75,
      "totalRedeemAmount": 7500.25,
      "totalSweepsBalance": {
        "totalSwipeCoins": 50000,
        "totalUnplayedSwipeCoins": 25000,
        "totalRedeemableSwipeCoins": 15000,
        "grandTotal": 90000
      }
    },
    "purchases": {
      "total": 150,
      "totalAmount": 15000.50,
      "items": [...],
      "groupBy": "date"
    },
    "redeems": {
      "total": 75,
      "totalAmount": 7500.25,
      "items": [...],
      "groupBy": "date"
    },
    "dateRange": {
      "startDate": "2024-01-01",
      "endDate": "2024-01-31"
    }
  },
  "timestamp": "2024-01-31T12:00:00.000Z"
}
```

### 2. Purchases Report

**GET** `/reports/purchases`

Get detailed purchases analytics (card/crypto payments).

#### Query Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| startDate | string | No | Start date (YYYY-MM-DD format) |
| endDate | string | No | End date (YYYY-MM-DD format) |
| groupBy | string | No | Group by: 'date', 'game', 'user' (default: 'date') |
| page | number | No | Page number (default: 1) |
| limit | number | No | Items per page (default: 50, max: 100) |

#### Response

```json
{
  "success": true,
  "data": {
    "total": 150,
    "totalAmount": 15000.50,
    "items": [
      {
        "_id": "2024-01-15",
        "count": 25,
        "totalAmount": 2500.00,
        "paymentMethods": ["card", "crypto"]
      }
    ],
    "groupBy": "date"
  },
  "pagination": {
    "page": 1,
    "limit": 50,
    "hasMore": true
  },
  "timestamp": "2024-01-31T12:00:00.000Z"
}
```

### 3. Redeems Report

**GET** `/reports/redeems`

Get detailed redeems analytics (push-to-card payments).

#### Query Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| startDate | string | No | Start date (YYYY-MM-DD format) |
| endDate | string | No | End date (YYYY-MM-DD format) |
| groupBy | string | No | Group by: 'date', 'game', 'user' (default: 'date') |
| page | number | No | Page number (default: 1) |
| limit | number | No | Items per page (default: 50, max: 100) |

#### Response

```json
{
  "success": true,
  "data": {
    "total": 75,
    "totalAmount": 7500.25,
    "items": [
      {
        "_id": "2024-01-15",
        "count": 10,
        "totalAmount": 1000.00,
        "games": ["game1", "game2"]
      }
    ],
    "groupBy": "date"
  },
  "pagination": {
    "page": 1,
    "limit": 50,
    "hasMore": false
  },
  "timestamp": "2024-01-31T12:00:00.000Z"
}
```

### 4. Wallet Balances Report

**GET** `/reports/wallet-balances`

Get comprehensive wallet balances report.

#### Response

```json
{
  "success": true,
  "data": {
    "totalSweepsBalance": {
      "totalSwipeCoins": 50000,
      "totalUnplayedSwipeCoins": 25000,
      "totalRedeemableSwipeCoins": 15000,
      "grandTotal": 90000
    },
    "detailedBalances": {
      "totalWallets": 1000,
      "totalGoldCoins": 100000,
      "totalSwipeCoins": 50000,
      "totalUnplayedSwipeCoins": 25000,
      "totalRedeemableSwipeCoins": 15000,
      "avgGoldCoins": 100,
      "avgSwipeCoins": 50
    }
  },
  "timestamp": "2024-01-31T12:00:00.000Z"
}
```

## Data Sources

### Order Documents

The Reports API analyzes Order documents with the following logic:

- **Purchases**: Orders with `paymentMethod` = 'card' or 'crypto'
- **Redeems**: Orders with `paymentMethod` = 'push-to-card'
- **Status Filter**: Only orders with status 'Completed', 'Success', or 'Approved'
- **Amount Field**: Uses the `price` field from Order documents

### Wallet Documents

For wallet balance reports, the API analyzes Wallet documents:

- **Sweeps Coins**: `balance.swipeCoins`, `balance.unplayedSwipeCoins`, `balance.redeemableSwipeCoins`
- **Gold Coins**: `balance.goldCoins`

## Error Handling

### Common Error Responses

#### 400 Bad Request
```json
{
  "success": false,
  "message": "Invalid startDate format. Use YYYY-MM-DD format.",
  "error": "INVALID_DATE_FORMAT"
}
```

#### 401 Unauthorized
```json
{
  "success": false,
  "message": "Authentication required",
  "error": "UNAUTHORIZED"
}
```

#### 403 Forbidden
```json
{
  "success": false,
  "message": "Access denied. Reports access requires Manage or Admin role.",
  "error": "INSUFFICIENT_PERMISSIONS"
}
```

#### 500 Internal Server Error
```json
{
  "success": false,
  "message": "Failed to fetch dashboard analytics",
  "error": "INTERNAL_SERVER_ERROR"
}
```

## Validation Rules

### Date Validation
- Dates must be in YYYY-MM-DD format
- Start date cannot be after end date
- Date range cannot exceed 365 days

### Pagination Validation
- Page number must be greater than 0
- Limit must be between 1 and 100

### GroupBy Validation
- Allowed values: 'date', 'game', 'user'

## Usage Examples

### Get Dashboard Analytics for Last 30 Days
```bash
curl -X GET "http://localhost:9000/reports/dashboard?startDate=2024-01-01&endDate=2024-01-31" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Get Purchases Grouped by Game
```bash
curl -X GET "http://localhost:9000/reports/purchases?groupBy=game&page=1&limit=20" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Get Redeems for Specific Date Range
```bash
curl -X GET "http://localhost:9000/reports/redeems?startDate=2024-01-15&endDate=2024-01-31&groupBy=date" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Implementation Notes

- All monetary amounts are returned as numbers (not strings)
- Dates are processed in UTC timezone for consistency
- Pagination uses skip/limit for efficient data retrieval
- Aggregation pipelines are optimized for performance
- Role-based access control is enforced at the middleware level
