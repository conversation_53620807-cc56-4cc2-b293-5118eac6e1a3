
const mongoose = require('mongoose');
const gameTransactionSchema = require('../Schema/gametransaction');

class GameTransactionModel {
  constructor() {
    this.model = mongoose.model('GameTransaction', gameTransactionSchema);
  }

  async createTransaction(data) {
    try {
      const transaction = new this.model(data);
      return await transaction.save();
    } catch (error) {
      throw error;
    }
  }

  async updateTransactionStatus(transactionId, status) {
    try {
      return await this.model.findByIdAndUpdate(
        transactionId,
        { status },
        { new: true }
      );
    } catch (error) {
      throw error;
    }
  }

  async getTransactionsByUserId(userId, limit = null, skip = 0) {
    try {
      const query = { userId };
      
      if (limit !== null) {
        return await this.model.find(query)
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limit)
          .populate({
            path: "gameId",
            select: "gameName playLink description gameUserId",
          });
      }
      
      // Fallback to original behavior if no pagination is specified
      return await this.model.find(query).sort({ createdAt: -1 });
    } catch (error) {
      throw error;
    }
  }

  async getTransactionById(transactionId) {
    try {
      return await this.model.findById(transactionId);
    } catch (error) {
      throw error;
    }
  }

  async find(query = {}) {
    try {
      return await this.model.find(query);
    } catch (error) {
      throw error;
    }
  }

  async countDocuments(query = {}) {
    try {
      return await this.model.countDocuments(query);
    } catch (error) {
      throw error;
    }
  }
}

module.exports = new GameTransactionModel();
