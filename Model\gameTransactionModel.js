const mongoose = require("mongoose");

export const gameTransactionSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: 'User'
  },
  gameId: {
    type: String,
    required: true
  },
  gameName: {
    type: String,
    required: true
  },
  transactionType: {
    type: String,
    enum: ['purchase', 'redeem'],
    required: true
  },
  amount: {
    type: Number,
    required: true
  },
  status: {
    type: String,
    enum: ['pending', 'completed', 'failed'],
    default: 'pending'
  },
  gameInfo: {
    type: mongoose.Schema.Types.Mixed,
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

class GameTransactionModel {
  constructor() {
    this.model = mongoose.model('GameTransaction', gameTransactionSchema);
  }

  async createTransaction(data) {
    try {
      const transaction = new this.model(data);
      return await transaction.save();
    } catch (error) {
      throw error;
    }
  }

  async updateTransactionStatus(transactionId, status) {
    try {
      return await this.model.findByIdAndUpdate(
        transactionId,
        { status },
        { new: true }
      );
    } catch (error) {
      throw error;
    }
  }

  async getTransactionsByUserId(userId, limit = null, skip = 0) {
    try {
      const query = { userId };
      
      if (limit !== null) {
        return await this.model.find(query)
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limit)
          .populate({
            path: "gameId",
            select: "gameName playLink description gameUserId",
          });
      }
      
      // Fallback to original behavior if no pagination is specified
      return await this.model.find(query).sort({ createdAt: -1 });
    } catch (error) {
      throw error;
    }
  }

  async getTransactionById(transactionId) {
    try {
      return await this.model.findById(transactionId);
    } catch (error) {
      throw error;
    }
  }

  async find(query = {}) {
    try {
      return await this.model.find(query);
    } catch (error) {
      throw error;
    }
  }

  async countDocuments(query = {}) {
    try {
      return await this.model.countDocuments(query);
    } catch (error) {
      throw error;
    }
  }
}

module.exports = new GameTransactionModel();
