import React, { useState } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '../../../redux/types';
import RedeemRequestTable from '../admin/reddemRequest';
import { Setting } from '../dashboard/adminDash';
import GameRequestTable from '../dashboard/adminGameRequest';
import PasswordReset from '../dashboard/passwordRequestRecived';
import TransactionTable from '../dashboard/transaction';
import PurchaseRequestTable from '../admin/purchaseRequest';
import Sidebar from './sidebar';
import DocumentTitle from '../../../components/DocumentTitle';
import MasterDashboard from '../admin/masterDashboard';
const CashierDashboard: React.FC = () => {
  const { user } = useSelector((state: RootState) => state.auth);
  const [isSidebarOpen, setIsSidebarOpen] = useState<boolean>(false);
  const [userData, setUserData] = useState<any>(user);

  const [activeTab, setActiveTab] = useState<string>(
    localStorage.getItem('persistentActiveTab') || 'Transactions'
  );
  const updateUserData = (newData: any) => {
    setUserData((prevState: any) => ({
      ...prevState,
      ...newData,
    }));
  };
  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
    localStorage.setItem('persistentActiveTab', tab);
  };

  const renderContent = (): React.ReactNode => {
    switch (activeTab) {
      case 'Dashboard':
        return <MasterDashboard />;
      case 'Transactions':
        return <TransactionTable />;
      case 'Game Requests':
        return <GameRequestTable />;
      case 'Redeem Requests':
        return <RedeemRequestTable />;
      case 'Password Resets':
        return <PasswordReset />;
      case 'Purchase Requests':
        return <PurchaseRequestTable />;
      case 'Settings':
        return <Setting userData={userData} updateUserData={updateUserData} />;
      default:
        return <TransactionTable />; // Default to transactions if tab not found
    }
  };

  return (
    <>
      <DocumentTitle title="LuckShack - Cashier Dashboard" />
      <div className="flex flex-col lg:flex-row min-h-[100%] bg-white p-2">
        <Sidebar
          activeTab={activeTab}
          setActiveTab={handleTabChange}
          isOpen={isSidebarOpen}
          setIsOpen={setIsSidebarOpen}
        />
        <div className="flex-1 p-4 lg:p-6 lg:mt-0">{renderContent()}</div>
      </div>
    </>
  );
};

export default CashierDashboard;
