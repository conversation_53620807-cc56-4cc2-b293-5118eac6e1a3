import React, { useState, useEffect, useRef, useCallback } from 'react';
import axios from 'axios';
import { SearchOutlined, CalendarOutlined } from '@ant-design/icons';
import { toast } from 'react-toastify';
import { fetchGameFields, formatDateTime } from 'utils/helperfunctions';
import LogViewerButton from '../../../components/LogViewerButton';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';



// Reusable Tooltip Component with best practices
const Tooltip: React.FC<{
  children: React.ReactNode;
  content: string;
  position?: 'top' | 'bottom' | 'left' | 'right';
  delay?: number;
}> = ({ children, content, position = 'top', delay = 200 }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout>();
  const tooltipId = useRef(`tooltip-${Math.random().toString(36).substring(2, 11)}`);

  const handleMouseEnter = () => {
    clearTimeout(timeoutRef.current);
    timeoutRef.current = setTimeout(() => {
      setIsVisible(true);
      setShowTooltip(true);
    }, delay);
  };

  const handleMouseLeave = () => {
    clearTimeout(timeoutRef.current);
    setIsVisible(false);
    setTimeout(() => setShowTooltip(false), 150);
  };

  const handleFocus = () => {
    setIsVisible(true);
    setShowTooltip(true);
  };

  const handleBlur = () => {
    setIsVisible(false);
    setTimeout(() => setShowTooltip(false), 150);
  };

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const getPositionClasses = () => {
    switch (position) {
      case 'bottom':
        return 'top-full left-1/2 transform -translate-x-1/2 mt-2';
      case 'left':
        return 'right-full top-1/2 transform -translate-y-1/2 mr-2';
      case 'right':
        return 'left-full top-1/2 transform -translate-y-1/2 ml-2';
      default: // top
        return 'bottom-full left-1/2 transform -translate-x-1/2 mb-2';
    }
  };

  const getArrowClasses = () => {
    switch (position) {
      case 'bottom':
        return 'bottom-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-gray-900';
      case 'left':
        return 'left-full top-1/2 transform -translate-y-1/2 w-0 h-0 border-t-4 border-b-4 border-l-4 border-transparent border-l-gray-900';
      case 'right':
        return 'right-full top-1/2 transform -translate-y-1/2 w-0 h-0 border-t-4 border-b-4 border-r-4 border-transparent border-r-gray-900';
      default: // top
        return 'top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900';
    }
  };

  return (
    <div className="relative inline-block">
      <div
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onFocus={handleFocus}
        onBlur={handleBlur}
        aria-describedby={isVisible ? tooltipId.current : undefined}
        tabIndex={0}
        className="focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 rounded"
      >
        {children}
      </div>

      {showTooltip && (
        <div
          id={tooltipId.current}
          role="tooltip"
          className={`absolute ${getPositionClasses()} px-3 py-2 bg-gray-900 text-white text-sm rounded-lg shadow-lg border border-gray-700 z-[100] transition-opacity duration-200 ${
            isVisible ? 'opacity-100' : 'opacity-0'
          }`}
          style={{
            whiteSpace: 'nowrap',
            pointerEvents: 'none'
          }}
        >
          {content}
          <div className={`absolute ${getArrowClasses()}`}></div>
        </div>
      )}
    </div>
  );
};

interface GameTransaction {
  _id: string;
  userId: string;
  gameId: string;
  gameName: string;
  transactionType: 'purchase' | 'redeem';
  amount: number;
  status: 'pending' | 'approved' | 'rejected' | 'completed' | 'failed';
  gameInfo: any;
  createdAt: string;
  user?: {
    username: string;
    email: string;
    firstName?: string;
    lastName?: string;
    name?: string; // For backward compatibility
  };
}

interface DateRange {
  startDate: Date | null;
  endDate: Date | null;
}

interface GameFieldsPopupProps {
  userId: string;
  gameId: string;
  onClose: () => void;
}

const GameFieldsPopup: React.FC<GameFieldsPopupProps> = ({ userId, gameId, onClose }) => {
  const [gameFields, setGameFields] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  const getGameFields = async () => {
    try {
      setLoading(true);
      // Fix: Pass userId and gameId as separate arguments, not as object
      const data = await fetchGameFields(userId, gameId);
      setGameFields(data);
    } catch (error) {
      console.error('Error fetching game fields:', error);
      toast.error('Failed to fetch game details');
    } finally {
      setLoading(false);
    }
  };

  // Fetch game fields when component mounts
  useEffect(() => {
    getGameFields();
  }, [userId, gameId]);

  // Listen for tab changes in the admin dashboard
  useEffect(() => {
    const handleTabChange = () => {
      getGameFields();
    };

    window.addEventListener('adminTabChanged', handleTabChange);

    return () => {
      window.removeEventListener('adminTabChanged', handleTabChange);
    };
  }, []);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-lg p-6 max-w-[90%] sm:max-w-lg w-full">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-xl font-bold text-lime-900">Game Details</h3>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            ✕
          </button>
        </div>

        {loading ? (
          <div className="flex justify-center items-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-lime-900"></div>
            <span className="ml-2 text-lime-900 font-medium">Loading details...</span>
          </div>
        ) : gameFields ? (
          <div className="max-h-[70vh] overflow-y-auto">
            <div className="space-y-4">
              {Object.entries(gameFields).map(([key, value]) => (
                <div key={key} className="border-b pb-2">
                  <p className="font-semibold text-gray-700">{key}</p>
                  <p className="text-gray-800">{typeof value === 'object' ? JSON.stringify(value) : String(value)}</p>
                </div>
              ))}
            </div>
          </div>
        ) : (
          <p className="text-center py-6 text-gray-600">No game details available</p>
        )}

        <div className="mt-6 flex justify-end">
          <button
            onClick={onClose}
            className="bg-lime-900 text-white px-4 py-2 rounded-lg hover:bg-lime-800"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

const PurchaseRequestTable: React.FC = () => {
  const [gameTransactions, setGameTransactions] = useState<GameTransaction[]>([]);
  const [gameTransactionsPage, setGameTransactionsPage] = useState(1);
  const [hasMoreGameTransactions, setHasMoreGameTransactions] = useState(true);
  const [isFetchingGameTransactions, setIsFetchingGameTransactions] = useState(false);
  const [isConfirmingTransaction, setIsConfirmingTransaction] = useState(false);
  const [showFilters, setShowFilters] = useState(false); // State to control filter visibility
  const [itemsPerPage] = useState(25); // Set pagination to 25 entries per page
  const [totalItems, setTotalItems] = useState(0); // Total number of transactions

  // Filter states
  const [searchTerm, setSearchTerm] = useState('');
  const [dateFilter, setDateFilter] = useState<DateRange>({ startDate: null, endDate: null });
  const [userFilter, setUserFilter] = useState('');
  const [emailFilter, setEmailFilter] = useState('');
  const [gameFilter, setGameFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('pending');

  const loaderRef = useRef<HTMLDivElement>(null);
  const [gameTransactionConfirmPopup, setGameTransactionConfirmPopup] = useState<null | {
    transactionId: string;
    currentStatus: GameTransaction['status'];
    newStatus: GameTransaction['status'];
    message: string;
  }>(null);
  const [gameFieldsPopup, setGameFieldsPopup] = useState<null | {
    userId: string;
    gameId: string;
  }>(null);

  const handleViewGameFields = (userId: string, gameId: string) => {
    setGameFieldsPopup({ userId, gameId });
  };

  const handleCloseGameFieldsPopup = () => {
    setGameFieldsPopup(null);
  };

  // Handle date range changes
  const handleDateRangeChange = (dates: [Date | null, Date | null]) => {
    const [start, end] = dates;
    setDateFilter({
      startDate: start,
      endDate: end
    });
  };

  const handleGameTransactionStatusChange = (
    transactionId: string,
    newStatus: GameTransaction['status']
  ) => {
    // Find the transaction
    const transaction = gameTransactions.find((t) => t._id === transactionId);

    if (!transaction || transaction.status === newStatus) return;

    setGameTransactionConfirmPopup({
      transactionId: transactionId,
      currentStatus: transaction.status,
      newStatus: newStatus,
      message: `Are you sure you want to ${newStatus === 'completed' ? 'APPROVE' : 'REJECT'} this transaction?`
    });
  };

  const handleGameTransactionConfirmChange = async () => {
    if (!gameTransactionConfirmPopup) return;

    try {
      setIsConfirmingTransaction(true);
      setIsFetchingGameTransactions(true);

      const response = await axios.put(
        `${process.env.REACT_APP_API_URL}/api/game-transactions/${gameTransactionConfirmPopup.transactionId}`,
        { status: gameTransactionConfirmPopup.newStatus },
        {
          headers: {
            Accept: 'application/json',
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            Authorization: `Bearer ${localStorage.getItem('Token')}`,
          },
        }
      );

      if (response.status === 200) {
        toast.success(`Transaction ${gameTransactionConfirmPopup.newStatus === 'completed' ? 'approved' : 'rejected'} successfully`);

        // Update the local state
        setGameTransactions(
          gameTransactions.map((transaction) =>
            transaction._id === gameTransactionConfirmPopup.transactionId
              ? { ...transaction, status: gameTransactionConfirmPopup.newStatus as GameTransaction['status'] }
              : transaction
          )
        );
      }
    } catch (error) {
      console.error('Error updating transaction status:', error);
      toast.error('Failed to update transaction status');
    } finally {
      setIsConfirmingTransaction(false);
      setIsFetchingGameTransactions(false);
      setGameTransactionConfirmPopup(null);
    }
  };

  const handleGameTransactionCancelChange = () => {
    setGameTransactionConfirmPopup(null);
  };

  const formatDate = (date: string) => {
    return formatDateTime(date);
  };

  const fetchGameTransactions = async (page: number) => {
    if (isFetchingGameTransactions) return;

    setIsFetchingGameTransactions(true);
    try {
      // Build query parameters with all filters
      const params = new URLSearchParams();
      params.append('page', page.toString());
      params.append('limit', itemsPerPage.toString()); // Use 25 items per page
      params.append('transactionType', 'purchase');

      // Add all filters if they exist - trim all string parameters
      if (searchTerm && searchTerm.trim()) params.append('search', searchTerm.trim());

      // Add date range filter if it exists
      if (dateFilter.startDate) {
        // Format date as YYYY-MM-DD to ensure consistent date handling
        const startDate = new Date(dateFilter.startDate);
        const year = startDate.getFullYear();
        const month = String(startDate.getMonth() + 1).padStart(2, '0');
        const day = String(startDate.getDate()).padStart(2, '0');
        const formattedStartDate = `${year}-${month}-${day}`;

        params.append('startDate', formattedStartDate);
        console.log('Purchase Request - Added start date (YYYY-MM-DD):', formattedStartDate);
      }

      if (dateFilter.endDate) {
        // Format date as YYYY-MM-DD to ensure consistent date handling
        const endDate = new Date(dateFilter.endDate);
        const year = endDate.getFullYear();
        const month = String(endDate.getMonth() + 1).padStart(2, '0');
        const day = String(endDate.getDate()).padStart(2, '0');
        const formattedEndDate = `${year}-${month}-${day}`;

        params.append('endDate', formattedEndDate);
        console.log('Purchase Request - Added end date (YYYY-MM-DD):', formattedEndDate);
      }

      if (userFilter && userFilter.trim()) params.append('userSearch', userFilter.trim());
      if (emailFilter && emailFilter.trim()) params.append('email', emailFilter.trim());
      if (gameFilter && gameFilter.trim()) params.append('game', gameFilter.trim());
      if (statusFilter && statusFilter.trim()) params.append('status', statusFilter.trim());

      const response = await axios.get(
        `${process.env.REACT_APP_API_URL}/api/game-transactions?${params.toString()}`,
        {
          headers: {
            Accept: 'application/json',
            'Content-Type': 'application/json',
            Authorization: `Bearer ${localStorage.getItem('Token')}`,
          },
        }
      );

      console.log('Game transactions response:', response.data);

      const newGameTransactions = response.data.data || [];
      const totalPages = response.data.pagination?.totalPages || 0;
      const totalItemsFromAPI = response.data.pagination?.totalItems || 0;

      // Update total items count
      setTotalItems(totalItemsFromAPI);

      // Always update the hasMoreGameTransactions flag
      if (newGameTransactions.length === 0 || page >= totalPages) {
        setHasMoreGameTransactions(false);
      } else {
        setHasMoreGameTransactions(true);
      }

      // Always update the transactions state, even if empty
      if (page === 1) {
        // For first page, replace the entire state
        setGameTransactions(newGameTransactions);
      } else {
        // For subsequent pages, append unique transactions
        setGameTransactions((prev) => {
          // Create a Set of existing IDs for efficient lookup
          const existingIds = new Set(prev.map(t => t._id));

          // Filter out any transactions that already exist in the state
          const uniqueNewTransactions: GameTransaction[] = newGameTransactions.filter(
            (transaction: GameTransaction) => !existingIds.has(transaction._id)
          );

          // Return the combined array with unique transactions
          return [...prev, ...uniqueNewTransactions];
        });
      }

      // Always update the page number if we received data or reached the end
      setGameTransactionsPage(page + 1);
    } catch (error: any) {
      console.error('Error fetching game transactions:', error.message);
      toast.error('Failed to load transactions');
      setHasMoreGameTransactions(false);
      if (page === 1) {
        setTotalItems(0);
      }
    } finally {
      setIsFetchingGameTransactions(false);
    }
  };

  const handleApplyFilters = () => {
    // Apply all filters at once when button is clicked
    console.log('Applying filters...', {
      searchTerm,
      dateFilter,
      userFilter,
      emailFilter,
      gameFilter,
      statusFilter
    });

    // Reset pagination and fetch with current filter values
    setGameTransactions([]);
    setGameTransactionsPage(1);
    setHasMoreGameTransactions(true);

    // Use setTimeout to ensure state updates are processed
    setTimeout(() => {
      fetchGameTransactions(1);
    }, 0);
  };

  const handleClearFilters = async () => {
    console.log('Clearing all filters');

    // Clear existing transactions immediately to avoid showing old data
    setGameTransactions([]);
    setIsFetchingGameTransactions(true);

    // Reset pagination state
    setGameTransactionsPage(1);
    setHasMoreGameTransactions(true);

    try {
      // Make direct API call with only the base parameters (no filters)
      const params = new URLSearchParams();
      params.append('transactionType', 'purchase'); // Only show purchase transactions
      params.append('page', '1');
      params.append('limit', '25'); // Use same limit as itemsPerPage

      const apiUrl = `${process.env.REACT_APP_API_URL}/api/game-transactions?${params.toString()}`;
      console.log('Clear filters API URL:', apiUrl);

      const response = await axios.get(
        apiUrl,
        {
          headers: {
            Accept: 'application/json',
            'Content-Type': 'application/json',
            Authorization: `Bearer ${localStorage.getItem('Token')}`,
          },
        }
      );

      console.log('Clear filters API Response:', response.data);

      const newGameTransactions = response.data.data || [];
      const totalPages = response.data.pagination?.totalPages || 0;
      const totalItemsFromAPI = response.data.pagination?.totalItems || 0;

      // Update total items count
      setTotalItems(totalItemsFromAPI);

      // Always update the hasMoreGameTransactions flag
      if (newGameTransactions.length === 0 || 1 >= totalPages) {
        setHasMoreGameTransactions(false);
      } else {
        setHasMoreGameTransactions(true);
      }

      // Always update the transactions state, even if empty
      setGameTransactions(newGameTransactions);

      // Always update the page number if we received data or reached the end
      setGameTransactionsPage(2);

      // Clear all filter states AFTER successful API call
      setSearchTerm('');
      setDateFilter({
        startDate: null,
        endDate: null
      });
      setUserFilter('');
      setEmailFilter('');
      setGameFilter('');
      setStatusFilter('');

      console.log('Filters cleared and data refreshed');
    } catch (error: any) {
      console.error('Error clearing filters and fetching transactions:', error);
      toast.error('Failed to clear filters and fetch transactions');
      setGameTransactions([]);
      setTotalItems(0);
      setHasMoreGameTransactions(false);
    } finally {
      setIsFetchingGameTransactions(false);
    }
  };

  useEffect(() => {
    // Only fetch game transactions on initial load
    if (gameTransactionsPage === 1 && gameTransactions.length === 0) {
      fetchGameTransactions(gameTransactionsPage);
    }
  }, []);

  // Listen for tab changes in the admin dashboard to refresh data
  useEffect(() => {
    const handleTabChange = (event: Event) => {
      const customEvent = event as CustomEvent;
      if (customEvent.detail?.tab === 'Purchase Requests') {
        // Reset and fetch fresh data when this tab is selected
        setGameTransactions([]);
        setGameTransactionsPage(1);
        setHasMoreGameTransactions(true);
        fetchGameTransactions(1);
      }
    };

    window.addEventListener('adminTabChanged', handleTabChange);

    return () => {
      window.removeEventListener('adminTabChanged', handleTabChange);
    };
  }, []);

  useEffect(() => {
    // Set up the intersection observer for infinite scrolling
    const options = {
      root: null, // Use the viewport as the root
      rootMargin: '100px', // Load more content when within 100px of the bottom
      threshold: 0.1, // Trigger when at least 10% of the element is visible
    };

    const observer = new IntersectionObserver((entries) => {
      const [entry] = entries;
      if (entry.isIntersecting && hasMoreGameTransactions && !isFetchingGameTransactions) {
        fetchGameTransactions(gameTransactionsPage);
      }
    }, options);

    // Attach the observer to the loader element
    if (loaderRef.current) {
      observer.observe(loaderRef.current);
    }

    // Clean up the observer on component unmount
    return () => {
      if (loaderRef.current) {
        observer.unobserve(loaderRef.current);
      }
      observer.disconnect();
    };
  }, [gameTransactionsPage, hasMoreGameTransactions, isFetchingGameTransactions]);

  // Status options for dropdown
  const statusOptions = [
    { label: 'Completed', value: 'completed' },
    { label: 'Rejected', value: 'rejected' }
  ];

  return (
    <div className="w-full bg-white rounded-lg p-4 sm:p-4 md:p-6 lg:fixed lg:right-0 lg:w-[calc(100%-250px)] lg:h-screen lg:overflow-y-auto">
      <div className="lg:flex lg:flex-col lg:h-full">
        {/* Header with Refresh Button */}
        <div className="flex justify-between items-center mb-4 sm:mb-6 mx-4">
          <h2 className="text-2xl font-bold text-lime-900">Purchase Requests</h2>
          <button
            onClick={() => {
              setGameTransactions([]);
              setGameTransactionsPage(1);
              setHasMoreGameTransactions(true);
              setIsFetchingGameTransactions(true);
              fetchGameTransactions(1);
            }}
            disabled={isFetchingGameTransactions}
            className={`px-4 py-2 rounded-lg flex items-center transition-colors ${
              isFetchingGameTransactions
                ? 'bg-gray-400 text-gray-200 cursor-not-allowed'
                : 'bg-lime-900 text-white hover:bg-lime-800'
            }`}
          >
            {isFetchingGameTransactions ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                Refreshing...
              </>
            ) : (
              <>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                Refresh
              </>
            )}
          </button>
        </div>
        {/* Filters Section with Toggle Button */}
        <div className="w-full bg-[#f5f5f0] rounded-lg mb-4 overflow-hidden border border-gray-200">
          <div className="flex justify-between items-center p-4" onClick={() => setShowFilters(!showFilters)}>
            <div className="flex items-center cursor-pointer">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-[#4D7C0F]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
              </svg>
              <span className="font-medium">Filter</span>
            </div>
            <button className="bg-[#4D7C0F] hover:bg-[#3f6a0a] text-white px-3 py-1 rounded transition-colors text-sm flex items-center">
              <span>Hide Filters</span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className={`h-4 w-4 ml-1 transition-transform duration-200 ${showFilters ? 'rotate-180' : ''}`}
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>
          </div>

          {/* Filter Content - Collapsible */}
          <div className={`p-4 ${showFilters ? 'block' : 'hidden'}`}>
            {/* First Row - 3 Filters */}
            <div className="flex flex-wrap mb-4">
              {/* General Search */}
              <div className="w-full md:w-1/3 px-2 mb-4 md:mb-0">
                <div className="relative">
                  <div className="flex items-center mb-1">
                    <label className="block text-gray-700 text-base font-medium">General Search</label>
                    <Tooltip content="Search by Transaction ID or Amount only" position="top" delay={300}>
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500 hover:text-gray-700 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </Tooltip>
                  </div>
                  <input
                    type="text"
                    placeholder="Search by ID, amount..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:border-gray-400"
                  />
                </div>
              </div>

              {/* Date Filter */}
              <div className="w-full md:w-1/3 px-2 mb-4 md:mb-0">
                <div className="relative">
                  <div className="flex items-center mb-1">
                    <label className="block text-gray-700 text-base font-medium">Date Range</label>
                    <Tooltip content="Filter by transaction request date range" position="top" delay={300}>
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500 hover:text-gray-700 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </Tooltip>
                  </div>
                  <div className="relative">
                    <DatePicker
                      selected={dateFilter.startDate}
                      onChange={handleDateRangeChange}
                      startDate={dateFilter.startDate}
                      endDate={dateFilter.endDate}
                      selectsRange
                      className="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:border-gray-400"
                      wrapperClassName="w-full"
                      placeholderText="Select date range"
                      dateFormat="yyyy-MM-dd"
                      isClearable
                      showMonthDropdown
                      showYearDropdown
                      dropdownMode="select"
                      popperPlacement="right"
                      popperClassName="date-picker-popper"
                    />
                  </div>
                </div>
              </div>

              {/* User Filter */}
              <div className="w-full md:w-1/3 px-2 mb-4 md:mb-0">
                <div className="relative">
                  <div className="flex items-center mb-1">
                    <label className="block text-gray-700 text-base font-medium">User</label>
                    <Tooltip content="Search by username, first name, last name, or full name" position="top" delay={300}>
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500 hover:text-gray-700 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </Tooltip>
                  </div>
                  <input
                    type="text"
                    placeholder="Search by username, first name, or last name"
                    value={userFilter}
                    onChange={(e) => setUserFilter(e.target.value)}
                    className="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:border-gray-400"
                  />
                </div>
              </div>
            </div>

            {/* Second Row - 3 Filters */}
            <div className="flex flex-wrap">
              {/* Email Filter */}
              <div className="w-full md:w-1/3 px-2 mb-4 md:mb-0">
                <div className="relative">
                  <div className="flex items-center mb-1">
                    <label className="block text-gray-700 text-base font-medium">Email</label>
                    <Tooltip content="Search by user email address (partial matches supported)" position="right" delay={300}>
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500 hover:text-gray-700 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </Tooltip>
                  </div>
                  <input
                    type="text"
                    placeholder="Filter by email"
                    value={emailFilter}
                    onChange={(e) => setEmailFilter(e.target.value)}
                    className="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:border-gray-400"
                  />
                </div>
              </div>

              {/* Game Filter */}
              <div className="w-full md:w-1/3 px-2 mb-4 md:mb-0">
                <div className="relative">
                  <div className="flex items-center mb-1">
                    <label className="block text-gray-700 text-base font-medium">Game</label>
                    <Tooltip content="Search by game name (partial matches supported)" position="top" delay={300}>
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500 hover:text-gray-700 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </Tooltip>
                  </div>
                  <input
                    type="text"
                    placeholder="Filter by game name"
                    value={gameFilter}
                    onChange={(e) => setGameFilter(e.target.value)}
                    className="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:border-gray-400"
                  />
                </div>
              </div>

              {/* Status Filter */}
              <div className="w-full md:w-1/3 px-2 mb-4 md:mb-0">
                <div className="relative">
                  <div className="flex items-center mb-1">
                    <label className="block text-gray-700 text-base font-medium">Status</label>
                    <Tooltip content="Filter by transaction status (Pending, Completed, Rejected)" position="top" delay={300}>
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500 hover:text-gray-700 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </Tooltip>
                  </div>
                  <select
                    className="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:border-gray-400 appearance-none"
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                  >
                    <option value="">All Statuses</option>
                    <option value="pending" selected>Pending</option>
                    <option value="completed">Completed</option>
                    <option value="rejected">Rejected</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Filter Actions */}
            <div className="flex justify-end mt-4 space-x-3">
              <button
                onClick={handleClearFilters}
                disabled={isFetchingGameTransactions}
                className={`flex items-center px-3 py-1.5 rounded transition-colors text-sm ${
                  isFetchingGameTransactions
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    : 'bg-gray-200 text-gray-800 hover:bg-gray-300'
                }`}
              >
                {isFetchingGameTransactions ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-400 mr-1"></div>
                    Clearing...
                  </>
                ) : (
                  <>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                    Clear All Filters
                  </>
                )}
              </button>
              <button
                onClick={handleApplyFilters}
                disabled={isFetchingGameTransactions}
                className={`flex items-center px-4 py-1.5 rounded transition-colors text-sm ${
                  isFetchingGameTransactions
                    ? 'bg-gray-400 text-gray-200 cursor-not-allowed'
                    : 'bg-[#4D7C0F] hover:bg-[#3f6a0a] text-white'
                }`}
              >
                {isFetchingGameTransactions ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-1"></div>
                    Applying...
                  </>
                ) : (
                  <>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                    Apply Filters
                  </>
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Table container */}
        <div className="w-full overflow-x-auto lg:flex-1 lg:overflow-y-auto">
          {/* Loading overlay for initial load */}
          {isFetchingGameTransactions && gameTransactions.length === 0 && (
            <div className="flex justify-center items-center py-12 bg-white rounded-lg border border-gray-200">
              <div className="flex flex-col items-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-lime-900 mb-4"></div>
                <span className="text-lime-900 font-medium text-lg">Loading purchase requests...</span>
              </div>
            </div>
          )}

          {/* Table - only show when not in initial loading state */}
          {(!isFetchingGameTransactions || gameTransactions.length > 0) && (
            <div className="min-w-[1400px] shadow-md rounded-lg border border-gray-200">
              <table className="w-full text-2xl table-fixed">
              <thead className="bg-lime-900">
                <tr>
                  <th className="px-4 py-4 text-left text-base sm:text-lg font-bold text-white whitespace-nowrap w-[18%]">
                    TRANSACTION ID
                  </th>
                  <th className="px-4 py-4 text-left text-base sm:text-lg font-bold text-white whitespace-nowrap w-[12%]">
                    USER
                  </th>
                  <th className="px-4 py-4 text-left text-base sm:text-lg font-bold text-white whitespace-nowrap w-[15%]">
                    EMAIL
                  </th>
                  <th className="px-4 py-4 text-left text-base sm:text-lg font-bold text-white whitespace-nowrap w-[12%]">
                    GAME
                  </th>
                  <th className="px-4 py-4 text-left text-base sm:text-lg font-bold text-white whitespace-nowrap w-[12%]">
                    REQUEST DATE
                  </th>
                  <th className="px-4 py-4 text-left text-base sm:text-lg font-bold text-white whitespace-nowrap w-[8%]">
                    AMOUNT
                  </th>
                  <th className="px-4 py-4 text-left text-base sm:text-lg font-bold text-white whitespace-nowrap w-[10%]">
                    STATUS
                  </th>
                  <th className="px-4 py-4 text-left text-base sm:text-lg font-bold text-white whitespace-nowrap w-[8%]">
                    ACTIONS
                  </th>
                  <th className="px-4 py-4 text-left text-base sm:text-lg font-bold text-white whitespace-nowrap w-[8%]">
                    DETAILS
                  </th>
                  <th className="px-4 py-4 text-left text-base sm:text-lg font-bold text-white whitespace-nowrap w-[7%]">
                    LOGS
                  </th>
                </tr>
              </thead>
              <tbody>
                {gameTransactions.map((request, index) => (
                  <tr key={request._id} className={`hover:bg-gray-50 ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}>
                    <td className="px-4 py-4 text-gray-800 font-medium w-[18%] truncate" title={request._id}>
                      {request._id}
                    </td>
                    <td className="px-4 py-4 text-lime-900 font-medium w-[12%] truncate" title={
                      request.user?.username ||
                      (request.user?.firstName || request.user?.lastName ?
                        `${request.user?.firstName || ''} ${request.user?.lastName || ''}`.trim() :
                        'Unknown User')
                    }>
                      {request.user?.username ||
                       (request.user?.firstName || request.user?.lastName ?
                        `${request.user?.firstName || ''} ${request.user?.lastName || ''}`.trim() :
                        'Unknown User')}
                    </td>
                    <td className="px-4 py-4 text-gray-700 w-[15%] truncate" title={request.user?.email || 'No email'}>
                      {request.user?.email || 'No email'}
                    </td>
                    <td className="px-4 py-4 text-lime-900 font-medium w-[12%] truncate" title={request.gameName}>
                      {request.gameName}
                    </td>
                    <td className="px-4 py-4 text-gray-700 w-[12%] truncate">
                      {formatDate(request.createdAt)}
                    </td>
                    <td className="px-4 py-4 font-semibold text-gray-800 w-[8%] truncate">
                      ${request.amount}
                    </td>
                    <td className="px-4 py-4 w-[10%]">
                      {request.status === 'pending' ? (
                        <select
                          value={request.status}
                          onChange={(e) => handleGameTransactionStatusChange(
                            request._id,
                            e.target.value as GameTransaction['status']
                          )}
                          className="w-full px-2 py-1 rounded-md text-xs font-medium border-2 bg-yellow-100 text-yellow-800 border-yellow-300 focus:outline-none focus:ring-2 focus:ring-lime-500 focus:border-transparent"
                        >
                          <option value="pending" disabled>PENDING</option>
                          {statusOptions.map((option) => (
                            <option key={option.value} value={option.value}>
                              {option.label.toUpperCase()}
                            </option>
                          ))}
                        </select>
                      ) : (
                        <span
                          className={`px-2 py-1 text-xs font-semibold rounded-full ${
                            request.status === 'completed'
                              ? 'bg-green-100 text-green-800'
                              : request.status === 'rejected'
                              ? 'bg-red-100 text-red-800'
                              : request.status === 'failed'
                              ? 'bg-gray-100 text-gray-800'
                              : 'bg-blue-100 text-blue-800'
                          }`}
                        >
                          {request.status.toUpperCase()}
                        </span>
                      )}
                    </td>

                    <td className="px-4 py-4 w-[8%]">
                      <button
                        onClick={() => handleViewGameFields(request.userId, request.gameId)}
                        className="bg-lime-900 text-white px-2 py-1 rounded hover:bg-lime-800 text-xs"
                      >
                        Actions
                      </button>
                    </td>
                    <td className="px-4 py-4 w-[8%]">
                      <button
                        onClick={() => handleViewGameFields(request.userId, request.gameId)}
                        className="bg-gray-600 text-white px-2 py-1 rounded hover:bg-gray-700 text-xs"
                      >
                        Details
                      </button>
                    </td>
                    <td className="px-4 py-4 w-[7%]">
                      <LogViewerButton
                        entityType="transaction_purchase"
                        entityId={request._id}
                        buttonSize="sm"
                        tooltip="View Activity Logs"
                      />
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>

            {/* Transaction Count Display */}
            <div className="p-4 bg-gray-50 border-t border-gray-200">
              <div className="text-sm text-gray-700 text-center">
                Showing <span className="font-medium">{gameTransactions.length}</span> of{' '}
                <span className="font-medium">{totalItems}</span> results
                {hasMoreGameTransactions && (
                  <span className="block mt-1 text-gray-500">
                    Scroll down to load more...
                  </span>
                )}
              </div>
            </div>

            {gameTransactions.length === 0 && !isFetchingGameTransactions && (
              <div className="text-center py-8">
                <p className="text-xl text-gray-500">No purchase requests found</p>
              </div>
            )}
            {/* Infinite scroll loading indicator */}
            <div ref={loaderRef} className="h-10 flex justify-center items-center py-4">
              {isFetchingGameTransactions && (
                <div className="flex justify-center items-center w-full py-6">
                  <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-lime-900"></div>
                  <span className="ml-3 text-lime-900 font-medium">Loading data...</span>
                </div>
              )}
            </div>
            {!hasMoreGameTransactions && gameTransactions.length > 0 && (
              <p className="text-center p-4 text-gray-500">No more records to display.</p>
            )}

            </div>
          )}
        </div>

        {/* Status Confirmation popup */}
        {gameTransactionConfirmPopup && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center p-4 z-50">
            <div className="bg-white rounded-lg shadow-lg p-4 sm:p-6 max-w-[90%] sm:max-w-md">
              <p className="text-base sm:text-lg font-medium mb-4">
                {gameTransactionConfirmPopup.message}
              </p>
              <div className="flex justify-end space-x-2 sm:space-x-4">
                <button
                  onClick={handleGameTransactionConfirmChange}
                  disabled={isConfirmingTransaction}
                  className={`bg-lime-900 text-white px-3 sm:px-4 py-1 sm:py-2 rounded-lg hover:bg-lime-800 text-sm sm:text-base flex items-center justify-center min-w-[80px] ${isConfirmingTransaction ? 'opacity-75 cursor-not-allowed' : ''}`}
                >
                  {isConfirmingTransaction ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Processing...
                    </>
                  ) : (
                    'Confirm'
                  )}
                </button>
                <button
                  onClick={handleGameTransactionCancelChange}
                  disabled={isConfirmingTransaction}
                  className={`bg-gray-600 text-white px-3 sm:px-4 py-1 sm:py-2 rounded-lg hover:bg-gray-700 text-sm sm:text-base ${isConfirmingTransaction ? 'opacity-75 cursor-not-allowed' : ''}`}
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Game Fields Popup */}
        {gameFieldsPopup && (
          <GameFieldsPopup
            userId={gameFieldsPopup.userId}
            gameId={gameFieldsPopup.gameId}
            onClose={handleCloseGameFieldsPopup}
          />
        )}
      </div>
    </div>
  );
};

export default PurchaseRequestTable;