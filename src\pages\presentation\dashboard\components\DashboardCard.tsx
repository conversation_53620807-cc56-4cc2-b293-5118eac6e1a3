import React from 'react';

interface DashboardCardProps {
  label: string;
  value: string | number;
  icon: React.ReactNode;
  bgColor: string;
  loading?: boolean;
}

const DashboardCard: React.FC<DashboardCardProps> = ({ 
  label, 
  value, 
  icon, 
  bgColor, 
  loading = false 
}) => (
  <div className="dashboard-card bg-white rounded-xl p-6 border-l-4 border-[#495e26] shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
    <div className="flex items-center justify-between">
      <div className="flex flex-col space-y-2">
        <div className="text-sm font-medium text-gray-600 uppercase tracking-wide">
          {label}
        </div>
        {loading ? (
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded-md w-20"></div>
          </div>
        ) : (
          <span className="text-2xl font-bold text-gray-800 leading-none">
            {value}
          </span>
        )}
      </div>
      <div className={`p-3 rounded-xl ${bgColor} shadow-md transition-transform duration-200 hover:scale-110`}>
        {loading ? (
          <div className="animate-pulse">
            <div className="w-6 h-6 bg-white/30 rounded"></div>
          </div>
        ) : (
          <div className="w-6 h-6 flex items-center justify-center text-white">
            {icon}
          </div>
        )}
      </div>
    </div>
  </div>
);

export default DashboardCard;