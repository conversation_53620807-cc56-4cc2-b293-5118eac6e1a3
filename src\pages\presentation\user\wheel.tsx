import React, { useState, useRef, useEffect } from 'react';
import axios from 'axios';
import { toast } from 'react-toastify';

// Type definitions
interface Segment {
  label: string;
  color: string;
  value: number;
}

interface SpinWheelProps {
  initialSegments?: Segment[];
}

const SpinWheel: React.FC<SpinWheelProps> = ({ initialSegments }): JSX.Element => {
  const [isSpinning, setIsSpinning] = useState<boolean>(false);
  const [result, setResult] = useState<string>('');
  const [userBalance, setUserBalance] = useState({
    goldCoins: 0
  });
  const [isFreeSpinMode, setIsFreeSpinMode] = useState<boolean>(false);

  // Use refs for critical elements
  const wheelRef = useRef<HTMLDivElement>(null);
  const spinTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const resultTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isProcessingRef = useRef<boolean>(false);

  const segments: Segment[] = initialSegments || [
    { label: 'NO WIN', color: '#FFFFFF', value: 0 },
    { label: 'NO WIN', color: '#495e26', value: 0 },
    { label: '5 GOLD', color: '#FFFFFF', value: 5 },
    { label: 'NO WIN', color: '#495e26', value: 0 },
    { label: 'SPIN AGAIN', color: '#FFFFFF', value: 0 }, // Free spin
    { label: 'NO WIN', color: '#495e26', value: 0 },
    { label: 'NO WIN', color: '#FFFFFF', value: 0 },
    { label: '10 GOLD', color: '#495e26', value: 10 },
    { label: 'SPIN AGAIN', color: '#FFFFFF', value: 0 }, // Free spin
    { label: '5 GOLD', color: '#495e26', value: 5 },
  ];

  // Clean up timeouts when component unmounts
  useEffect(() => {
    return () => {
      if (spinTimeoutRef.current) {
        clearTimeout(spinTimeoutRef.current);
        spinTimeoutRef.current = null;
      }
      if (resultTimeoutRef.current) {
        clearTimeout(resultTimeoutRef.current);
        resultTimeoutRef.current = null;
      }
    };
  }, []);

  // Ensure clean state reset
  const resetSpinState = () => {
    isProcessingRef.current = false;
    setIsSpinning(false);
  };

  const updateBalance = async (winAmount: number) => {
    try {
      const userId = localStorage.getItem('userId');
      if (!userId) {
        toast.error('User not found');
        resetSpinState();
        return;
      }

      // Only deduct spin cost if not in free spin mode
      const spinCost = isFreeSpinMode ? 0 : 5;

      const response = await axios.post(
        `${process.env.REACT_APP_API_URL}/wallet/spin-result/${userId}`,
        {
          spinCost: spinCost,
          winAmount: winAmount
        },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('Token')}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.data.data) {
        setUserBalance({
          goldCoins: response.data.data.balance.goldCoins
        });

        if (response.data.data.message) {
          if (winAmount > 0) {
            toast.success(response.data.data.message);
          } else {
            toast.info(response.data.data.message);
          }
        }
      }
    } catch (error: any) {
      console.error('Error updating balance:', error);
      fetchBalance();
      resetSpinState(); // Ensure state is reset on error
    } finally {
      // Always reset state after API call completes
      resetSpinState();
    }
  };

  const fetchBalance = async () => {
    try {
      const userId = localStorage.getItem('userId');
      if (!userId) {
        toast.error('User ID not found');
        return;
      }

      const response = await axios.get(
        `${process.env.REACT_APP_API_URL}/wallet/check/${userId}`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('Token')}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.data.data && response.data.data.balance) {
        setUserBalance({
          goldCoins: response.data.data.balance.goldCoins || 0
        });
      } else {
        toast.error('Failed to get balance data');
      }
    } catch (error: any) {
      toast.error('Failed to fetch balance');
    }
  };

  useEffect(() => {
    fetchBalance();
  }, []);

  const handleSpin = () => {

    // Prevent multiple spins or clicks
    if (isSpinning || isProcessingRef.current) {
      return;
    }

    if (!wheelRef.current) {
      return;
    }

    // Check if user has enough balance (only if not in free spin mode)
    if (!isFreeSpinMode && userBalance.goldCoins < 5) {
      toast.error('Not enough gold coins! You need 5 coins to spin.');
      return;
    }

    // Set processing state immediately
    isProcessingRef.current = true;
    setIsSpinning(true);
    setResult(''); // Clear any previous result

    // Clean up any previous timeouts to prevent interference
    if (spinTimeoutRef.current) {
      clearTimeout(spinTimeoutRef.current);
      spinTimeoutRef.current = null;
    }
    if (resultTimeoutRef.current) {
      clearTimeout(resultTimeoutRef.current);
      resultTimeoutRef.current = null;
    }

    // Only deduct balance if not in free spin mode
    if (!isFreeSpinMode) {
      setUserBalance(prev => ({
        ...prev,
        goldCoins: prev.goldCoins - 5
      }));
    }

    // Save and reset free spin mode
    const wasFreeSpinMode = isFreeSpinMode;
    if (isFreeSpinMode) {
      setIsFreeSpinMode(false);
    }

    // Fixed number of spins for consistent speed
    const spins = 5;
    const extraDegrees = Math.floor(Math.random() * 360);
    const totalDegrees = spins * 360 + extraDegrees;

    const winningIndex = Math.floor((360 - (extraDegrees % 360)) / (360 / segments.length));
    const winning = segments[winningIndex];

    // Reset wheel position with a proper reflow
    if (wheelRef.current) {
      wheelRef.current.style.transition = 'none';
      wheelRef.current.style.transform = `rotate(0deg)`;
      void wheelRef.current.offsetHeight; // Force reflow

      // Set animation with consistent parameters
      wheelRef.current.style.transition = 'transform 5s cubic-bezier(0.2, 0, 0.2, 1)';
      wheelRef.current.style.transform = `rotate(${totalDegrees}deg)`;
    }

    // Store spin result data before timeout
    const isFreeSpinResult = winning.label === 'SPIN AGAIN';
    const winValue = winning.value;

    // Create result text and prepare action, wait for animation to complete
    resultTimeoutRef.current = setTimeout(() => {
      try {
        // Delay the result popup very slightly to ensure animation is fully complete
        let resultText = '';

        // Set result text based on winning segment
        if (isFreeSpinResult) {
          resultText = 'Spin again to win!';
          setIsFreeSpinMode(true);
        } else if (winning.label === 'NO WIN') {
          resultText = 'Better luck next time!';
        } else {
          resultText = `You won ${winValue} coins!`;
        }

        // Display result popup
        setResult(resultText);

        // Handle notifications
        if (isFreeSpinResult) {
          toast.info('Spin again to win!');
        } else if (winValue > 0) {
          toast.success(resultText);
        } else {
          toast.info(resultText);
        }

        // Update balance via API - must be called before resetting state
        updateBalance(winValue);

        // If it's a free spin, queue up another spin after a delay
        if (isFreeSpinResult) {
          // Reset state first to allow auto-spin to proceed
          setIsSpinning(false);

          spinTimeoutRef.current = setTimeout(() => {
            // Double-check we're not still processing before auto-spinning
            if (!isProcessingRef.current) {
              // Reset processing flag just to be safe
              isProcessingRef.current = false;
              // Trigger the auto-spin with a small delay to ensure clean state
              handleSpin();
            } else {
              console.warn("Auto-spin skipped due to ongoing processing");
              // Reset state to recover from stuck condition
              resetSpinState();
            }
          }, 3000); // Longer delay for reliability
        } else {
          // If not a free spin, just reset state
          resetSpinState();
        }
      } catch (error) {
        console.error("Error in spin result handling:", error);
        // Make sure state is reset even on error
        resetSpinState();
      }
    }, 5200); // Slightly longer than animation to ensure it completes
  };

  const getSegmentPath = (index: number, total: number, radius: number): string => {
    const angle = (2 * Math.PI) / total;
    const startAngle = index * angle - Math.PI / 2;
    const endAngle = (index + 1) * angle - Math.PI / 2;

    const x1 = 275 + radius * Math.cos(startAngle);
    const y1 = 275 + radius * Math.sin(startAngle);
    const x2 = 275 + radius * Math.cos(endAngle);
    const y2 = 275 + radius * Math.sin(endAngle);

    return `M275,275 L${x1},${y1} A${radius},${radius} 0 0,1 ${x2},${y2} Z`;
  };

  const generateSpiral = (): string => {
    let path = '';
    const turns = 3;
    const points = 180;
    const maxRadius = 28;

    for (let i = 0; i <= points; i++) {
      const angle = (i / points) * Math.PI * 2 * turns;
      const radius = (i / points) * maxRadius;
      const x = radius * Math.cos(angle);
      const y = radius * Math.sin(angle);

      if (i === 0) {
        path += `M${x},${y}`;
      } else {
        path += `L${x},${y}`;
      }
    }
    return path;
  };

  // Handle closing result popup - ensure state is clean
  const handleCloseResult = () => {
    setResult('');
    // Double-check all states are properly reset
    if (isProcessingRef.current) {
      resetSpinState();
    }
  };

  return (
    <div className="w-full bg-gradient-to-br rounded-xl p-2 sm:p-4 md:p-6 min-h-[650px] flex flex-col items-center relative overflow-hidden">
    {/* Result Popup - Fixed positioning and center alignment on mobile */}
    {result && (
      <div className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50">
        <div className="bg-white border-4 border-[#495e26] rounded-xl shadow-2xl p-6 max-w-sm w-full text-center mx-4 sm:mx-auto">
          <h3 className="text-2xl font-bold text-[#495e26] mb-2">Result</h3>
          <p className="text-lg text-gray-700">{result}</p>
          <button
            onClick={handleCloseResult}
            className="mt-4 px-4 py-2 bg-[#495e26] text-white rounded hover:bg-[#3D5C3F] transition"
          >
            Close
          </button>
        </div>
      </div>
    )}

      <div className="rounded-xl overflow-hidden w-full max-w-4xl mx-auto">
        <div className="p-2 border-b border-[#495e26] flex flex-col sm:flex-row justify-between items-center">
          <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-center bg-clip-text text-transparent bg-gradient-to-r from-[#FFD700] to-[#FFA500] mb-2 sm:mb-0">
            Gold Coin Wheel
          </h2>
          <div className="flex items-center bg-gradient-to-r from-[#FFD700] to-[#FFA500] p-[2px] rounded-lg">
            <div className="flex items-center gap-2 bg-white px-4 py-2 rounded-lg">
              <img src="https://res.cloudinary.com/dyiso4ohk/image/upload/v1742926203/Clover__1_-removebg-preview_xh6tsw.png" alt="Gold Coin" className="w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12" />
              <div className="flex flex-col">
                <span className="text-xs text-gray-600">Balance</span>
                <span className="text-lg sm:text-xl font-bold text-[#495e26]">{userBalance.goldCoins}</span>
              </div>
            </div>
          </div>
        </div>

        <div className="p-2 sm:p-4 flex flex-col items-center gap-2">
          <div className="relative w-full max-w-[300px] h-[300px] sm:max-w-[400px] sm:h-[400px] md:max-w-[500px] md:h-[500px] lg:max-w-[600px] lg:h-[600px] mt-4">
            <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-2 z-10 filter drop-shadow-lg pointer-events-none">
              <svg width="44" height="44" viewBox="0 0 44 44" className="overflow-visible">
                <path d="M22 44 L44 0 L0 0 Z" fill="#FFD700" filter="url(#glow)" />
                <defs>
                  <filter id="glow">
                    <feGaussianBlur stdDeviation="3" result="coloredBlur" />
                    <feMerge>
                      <feMergeNode in="coloredBlur" />
                      <feMergeNode in="SourceGraphic" />
                    </feMerge>
                  </filter>
                </defs>
              </svg>
            </div>

            <div
              ref={wheelRef}
              className="absolute w-full h-full"
              style={{ transformOrigin: 'center center' }}
            >
              <svg width="100%" height="100%" viewBox="0 0 550 550">
                <defs>
                  <radialGradient id="wheelGradient">
                    <stop offset="0%" stopColor="rgba(255,215,0,0.2)" />
                    <stop offset="100%" stopColor="rgba(255,215,0,0)" />
                  </radialGradient>
                  <linearGradient id="goldenRing" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" stopColor="#FFD700" />
                    <stop offset="50%" stopColor="#FFA500" />
                    <stop offset="100%" stopColor="#FFD700" />
                  </linearGradient>
                </defs>

                <circle
                  cx="275"
                  cy="275"
                  r="273"
                  fill="none"
                  stroke="url(#goldenRing)"
                  strokeWidth="8"
                  className="filter drop-shadow-xl"
                />
                <circle
                  cx="275"
                  cy="275"
                  r="265"
                  fill="none"
                  stroke="#FFD700"
                  strokeWidth="3"
                  opacity="0.5"
                />
                <circle
                  cx="275"
                  cy="275"
                  r="260"
                  fill="none"
                  stroke="#FFA500"
                  strokeWidth="2"
                  opacity="0.3"
                />

                {segments.map((segment, index) => {
                  const angle = (360 / segments.length) * index + 360 / segments.length / 2;
                  return (
                    <g key={index}>
                      <path
                        d={getSegmentPath(index, segments.length, 275)}
                        fill={segment.color}
                        stroke="rgba(255,215,0,0.3)"
                        strokeWidth="2"
                        className="filter drop-shadow-sm"
                      />
                      <path
                        d={getSegmentPath(index, segments.length, 275)}
                        fill="url(#wheelGradient)"
                        opacity="0.1"
                      />
                      <g transform={`rotate(${angle} 275 275)`}>
                        <text
                          x="275"
                          y="95"
                          fill={segment.color === '#FFFFFF' ? '#495e26' : '#FFFFFF'}
                          fontFamily="Georgia, serif"
                          fontSize="18"
                          fontWeight="bold"
                          textAnchor="middle"
                          dominantBaseline="middle"
                          transform={`rotate(${90} 275 95)`}>
                          {segment.label}
                        </text>
                      </g>
                    </g>
                  );
                })}

                <g transform="translate(275 275)">
                  <circle r="42" fill="#495e26" className="filter drop-shadow-lg" />
                  <circle r="40" fill="none" stroke="#FFD700" strokeWidth="3" />
                  {[0, 90, 180, 270].map((rotation, index) => (
                    <g key={index} transform={`rotate(${rotation})`}>
                      <path
                        d={generateSpiral()}
                        fill="none"
                        stroke="#FFD700"
                        strokeWidth="3"
                        strokeLinecap="round"
                        className="filter drop-shadow-md"
                        opacity={0.8 - index * 0.2}
                      />
                    </g>
                  ))}
                </g>
              </svg>
            </div>
          </div>

          <div className="relative">
            <button
              onClick={handleSpin}
              disabled={isSpinning || (!isFreeSpinMode && userBalance.goldCoins < 5)}
              className={`mt-4 px-8 py-3 rounded-full text-xl font-bold transition-all duration-300 relative z-10 w-full max-w-[200px] flex items-center justify-center ${
                isSpinning || (!isFreeSpinMode && userBalance.goldCoins < 5)
                  ? 'bg-gray-400 cursor-not-allowed'
                  : 'bg-[#495e26] hover:bg-[#3D5C3F] text-white cursor-pointer'
              }`}
              style={{ pointerEvents: 'auto' }}
            >
              <span className="pointer-events-none">
                {isSpinning
                  ? 'Spinning...'
                  : userBalance.goldCoins < 5 && !isFreeSpinMode
                  ? 'Not enough coins'
                  : "Spin"}
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SpinWheel;