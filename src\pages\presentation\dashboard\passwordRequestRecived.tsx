import React, { useState, useEffect, useCallback, useRef } from 'react';
import axios from 'axios';
import { toast } from 'react-toastify';
import LogViewerButton from '../../../components/LogViewerButton';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';

// Reusable Tooltip Component
const Tooltip: React.FC<{
  children: React.ReactNode;
  content: string;
  position?: 'top' | 'bottom' | 'left' | 'right';
  delay?: number;
}> = ({ children, content, position = 'top', delay = 200 }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout>();
  const tooltipId = useRef(`tooltip-${Math.random().toString(36).substring(2, 11)}`);

  const handleMouseEnter = () => {
    clearTimeout(timeoutRef.current);
    timeoutRef.current = setTimeout(() => {
      setIsVisible(true);
      setShowTooltip(true);
    }, delay);
  };

  const handleMouseLeave = () => {
    clearTimeout(timeoutRef.current);
    setIsVisible(false);
    setTimeout(() => setShowTooltip(false), 150);
  };

  const handleFocus = () => {
    setIsVisible(true);
    setShowTooltip(true);
  };

  const handleBlur = () => {
    setIsVisible(false);
    setTimeout(() => setShowTooltip(false), 150);
  };

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const getPositionClasses = () => {
    switch (position) {
      case 'bottom':
        return 'top-full left-1/2 transform -translate-x-1/2 mt-2';
      case 'left':
        return 'right-full top-1/2 transform -translate-y-1/2 mr-2';
      case 'right':
        return 'left-full top-1/2 transform -translate-y-1/2 ml-2';
      default: // top
        return 'bottom-full left-1/2 transform -translate-x-1/2 mb-2';
    }
  };

  const getArrowClasses = () => {
    switch (position) {
      case 'bottom':
        return 'bottom-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-gray-900';
      case 'left':
        return 'left-full top-1/2 transform -translate-y-1/2 w-0 h-0 border-t-4 border-b-4 border-l-4 border-transparent border-l-gray-900';
      case 'right':
        return 'right-full top-1/2 transform -translate-y-1/2 w-0 h-0 border-t-4 border-b-4 border-r-4 border-transparent border-r-gray-900';
      default: // top
        return 'top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900';
    }
  };

  return (
    <div className="relative inline-block">
      <div
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onFocus={handleFocus}
        onBlur={handleBlur}
        aria-describedby={isVisible ? tooltipId.current : undefined}
        tabIndex={0}
        className="focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 rounded"
      >
        {children}
      </div>

      {showTooltip && (
        <div
          id={tooltipId.current}
          role="tooltip"
          className={`absolute ${getPositionClasses()} px-3 py-2 bg-gray-900 text-white text-sm rounded-lg shadow-lg border border-gray-700 z-[100] transition-opacity duration-200 ${
            isVisible ? 'opacity-100' : 'opacity-0'
          }`}
          style={{
            whiteSpace: 'nowrap',
            pointerEvents: 'none'
          }}
        >
          {content}
          <div className={`absolute ${getArrowClasses()}`}></div>
        </div>
      )}
    </div>
  );
};

interface PasswordResetData {
  gameUserId: string;
  password: string;
  _id: string;
}

interface PasswordInputs {
  [key: string]: string;
}

interface DateRange {
  startDate: Date | null;
  endDate: Date | null;
}

const PasswordReset: React.FC = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [passwords, setPasswords] = useState<PasswordInputs>({});
  const [gameRequests, setGameRequests] = useState<any[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [selectedSystem, setSelectedSystem] = useState<any>(null);
  const [isDetailsOpen, setIsDetailsOpen] = useState(false);
  const [approvedRequests, setApprovedRequests] = useState<Set<string>>(new Set());
  const [showPasswords, setShowPasswords] = useState<{
    [key: string]: boolean;
  }>({});
  const [loading, setLoading] = useState(true);
  const [hasMore, setHasMore] = useState(true);
  const [approvingRequests, setApprovingRequests] = useState<Set<string>>(new Set());

  // Filter states
  const [searchTerm, setSearchTerm] = useState('');
  const [dateFilter, setDateFilter] = useState<DateRange>({
    startDate: null,
    endDate: null
  });
  const [userFilter, setUserFilter] = useState('');
  const [emailFilter, setEmailFilter] = useState('');
  const [gameFilter, setGameFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [showFilters, setShowFilters] = useState(false);

  // Initialize all passwords to be visible by default
  useEffect(() => {
    const initialPasswordVisibility: { [key: string]: boolean } = {};
    gameRequests.forEach(request => {
      initialPasswordVisibility[request._id] = true;
    });
    setShowPasswords(initialPasswordVisibility);
  }, [gameRequests]);

  const togglePasswordVisibility = (id: string): void => {
    setShowPasswords((prev) => ({
      ...prev,
      [id]: !prev[id],
    }));
  };

  const formatDateTime = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const handlePasswordChange = (id: string, value: string) => {
    setPasswords((prev) => ({
      ...prev,
      [id]: value,
    }));
  };

  // Create a function to build the API params object for filtering
  const buildApiParams = useCallback(() => {
    const params = new URLSearchParams();

    // Always include these base params
    params.append('requestType', 'passwordReset');

    // Add search term if it exists - trim to remove extra spaces
    const trimmedSearchTerm = searchTerm.trim();
    if (trimmedSearchTerm) {
      params.append('search', trimmedSearchTerm);
      console.log('Added search term:', trimmedSearchTerm);
    }

    // Add date range filter if it exists
    if (dateFilter.startDate || dateFilter.endDate) {
      if (dateFilter.startDate) {
        // Format date as YYYY-MM-DD to ensure consistent date handling
        const startDate = new Date(dateFilter.startDate);
        const year = startDate.getFullYear();
        const month = String(startDate.getMonth() + 1).padStart(2, '0');
        const day = String(startDate.getDate()).padStart(2, '0');
        const formattedStartDate = `${year}-${month}-${day}`;

        params.append('startDate', formattedStartDate);
        console.log('Added start date (YYYY-MM-DD):', formattedStartDate);
      }
      if (dateFilter.endDate) {
        // Format date as YYYY-MM-DD to ensure consistent date handling
        const endDate = new Date(dateFilter.endDate);
        const year = endDate.getFullYear();
        const month = String(endDate.getMonth() + 1).padStart(2, '0');
        const day = String(endDate.getDate()).padStart(2, '0');
        const formattedEndDate = `${year}-${month}-${day}`;

        params.append('endDate', formattedEndDate);
        console.log('Added end date (YYYY-MM-DD):', formattedEndDate);
      }
    }

    // Add user filter if it exists - trim to remove extra spaces
    const trimmedUserFilter = userFilter.trim();
    if (trimmedUserFilter) {
      // Send the user filter as userName to match backend expectations
      params.append('userName', trimmedUserFilter);
      console.log('Added user search filter:', trimmedUserFilter);
    }

    // Add email filter if it exists - trim and convert to lowercase
    const trimmedEmailFilter = emailFilter.trim().toLowerCase();
    if (trimmedEmailFilter) {
      params.append('userEmail', trimmedEmailFilter);
      console.log('Added email filter:', trimmedEmailFilter);
    }

    // Add game filter if it exists - trim to remove extra spaces
    const trimmedGameFilter = gameFilter.trim();
    if (trimmedGameFilter) {
      params.append('gameName', trimmedGameFilter);
      console.log('Added game filter:', trimmedGameFilter);
    }

    // Add status filter if it exists
    if (statusFilter) {
      params.append('status', statusFilter);
      console.log('Added status filter:', statusFilter);
    }

    return params;
  }, [searchTerm, dateFilter, userFilter, emailFilter, gameFilter, statusFilter]);

  const fetchGameRequests = async (page: number = 1) => {
    setLoading(true);
    try {
      // Build the URL with query parameters using the filter function
      const params = buildApiParams();
      params.append('page', page.toString());
      params.append('limit', '10');

      const apiUrl = `${process.env.REACT_APP_API_URL}/game-requests?${params.toString()}`;
      console.log('API URL:', apiUrl);

      const response = await axios.get(
        apiUrl,
        {
          headers: {
            Accept: 'application/json',
            'Content-Type': 'application/json',
            Authorization: `Bearer ${localStorage.getItem('Token')}`,
          },
        }
      );

      const { data, pagination } = response.data;

      if (!data || !pagination) {
        throw new Error('Invalid API response structure.');
      }

      // Check if there are more pages available based on pagination info
      const totalPages = pagination.totalPages || 1;
      const hasMorePages = page < totalPages;

      console.log('Pagination info:', {
        currentPage: page,
        totalPages,
        totalRecords: pagination.totalRecords || 0,
        hasMorePages
      });

      const userRequests = await Promise.all(
        data.map(async (request: any) => {
          if (request.userId && !request.user) {
            try {
              const userResponse = await axios.get(
                `${process.env.REACT_APP_API_URL}/user/${request.userId}`,
                {
                  headers: {
                    Accept: 'application/json',
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${localStorage.getItem('Token')}`,
                  },
                }
              );
              const userData = userResponse.data.data;
              return {
                ...request,
                email: userData?.email || 'N/A',
                userName: `${userData?.firstName || 'N/A'} ${userData?.lastName || 'N/A'}`,
              };
            } catch (userError) {
              console.error(`Error fetching user data for userId ${request.userId}:`, userError);
              return {
                ...request,
                email: 'N/A',
                userName: 'N/A',
              };
            }
          }

          return {
            ...request,
            email: request.user?.email || 'N/A',
            userName: `${request.user?.firstName || 'N/A'} ${request.user?.lastName || 'N/A'}`,
          };
        })
      );

      const newRequests = page === 1 ? userRequests : [...gameRequests, ...userRequests];

      // Sort requests by creation date only (newest first)
      const sortedRequests = [...newRequests].sort((a, b) => {
        return new Date(b.createdAt || b.date).getTime() - new Date(a.createdAt || a.date).getTime();
      });

      setGameRequests(sortedRequests);

      // Set hasMore based on pagination information instead of just checking if the current batch has items
      setHasMore(hasMorePages);

      const approvedSet = new Set(
        newRequests.filter((request) => request.passwordReset === 'Requested').map((req) => req._id)
      );

      setApprovedRequests(approvedSet);
    } catch (error: any) {
      console.error('Error fetching game requests:', error.message);
      setError('Failed to fetch game requests. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Create a ref for the loading indicator element
  const loadingRef = React.useRef<HTMLDivElement>(null);

  // Use Intersection Observer for better infinite scrolling
  useEffect(() => {
    const options = {
      root: null, // Use the viewport as the root
      rootMargin: '0px',
      threshold: 0.1, // Trigger when at least 10% of the target is visible
    };

    const observer = new IntersectionObserver((entries) => {
      const [entry] = entries;
      if (entry.isIntersecting && !loading && hasMore) {
        console.log('Loading more data - intersection detected');
        setCurrentPage((prevPage) => prevPage + 1);
      }
    }, options);

    const currentLoadingRef = loadingRef.current;
    if (currentLoadingRef) {
      observer.observe(currentLoadingRef);
    }

    return () => {
      if (currentLoadingRef) {
        observer.unobserve(currentLoadingRef);
      }
    };
  }, [loading, hasMore]);

  useEffect(() => {
    // Only fetch on currentPage change if it's not the initial load
    if (currentPage > 1) {
      fetchGameRequests(currentPage);
    }
  }, [currentPage]);

  // Initial data load
  useEffect(() => {
    fetchGameRequests(1);
  }, []);

  const handleApprove = async (rowData: PasswordResetData) => {
    const password = passwords[rowData._id] || '';
    if (!password) {
      alert('Password is required.');
      return;
    }
    try {
      setApprovingRequests(prev => new Set(prev).add(rowData._id));
      await axios.put(
        `${process.env.REACT_APP_API_URL}/game-request/reset-password/${rowData._id}`,
        {
          gamePassword: password,
          passwordReset: 'Requested',
        },
        {
          headers: {
            Accept: 'application/json',
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            Authorization: `Bearer ${localStorage.getItem('Token')}`,
          },
        }
      );
      alert('Game request approved successfully.');
      setApprovedRequests((prev) => new Set(prev).add(rowData._id));

      // Refresh with current page to maintain filters
      fetchGameRequests(1);
    } catch (error) {
      console.error('Error updating game request:', error);
      alert('Failed to approve the game request.');
    } finally {
      setApprovingRequests(prev => {
        const newSet = new Set(prev);
        newSet.delete(rowData._id);
        return newSet;
      });
    }
  };

  // Filter functions
  const handleSearch = (e?: React.FormEvent) => {
    if (e) e.preventDefault();
    console.log('Applying filters and fetching data');
    console.log('Filters:', { searchTerm, dateFilter, userFilter, emailFilter, gameFilter, statusFilter });

    // Clear existing requests immediately to avoid showing old data
    setGameRequests([]);
    setLoading(true);

    // Reset pagination and fetch first page with current filters
    setCurrentPage(1);
    setHasMore(true);
    fetchGameRequests(1);
  };

  // Handle date filter changes
  const handleDateRangeChange = (dates: [Date | null, Date | null]) => {
    const [start, end] = dates;
    console.log('Date range changed:', { start, end });
    setDateFilter({
      startDate: start,
      endDate: end
    });
  };

  const clearAllFilters = async () => {
    console.log('Clearing all filters');

    // Clear existing requests immediately to avoid showing old data
    setGameRequests([]);
    setLoading(true);

    // Reset pagination state
    setCurrentPage(1);
    setHasMore(true);

    try {
      // Make direct API call with only the base parameters (no filters)
      const params = new URLSearchParams();
      params.append('requestType', 'passwordReset');
      params.append('page', '1');
      params.append('limit', '10');

      const apiUrl = `${process.env.REACT_APP_API_URL}/game-requests?${params.toString()}`;
      console.log('Clear filters API URL:', apiUrl);

      const response = await axios.get(
        apiUrl,
        {
          headers: {
            Accept: 'application/json',
            'Content-Type': 'application/json',
            Authorization: `Bearer ${localStorage.getItem('Token')}`,
          },
        }
      );

      console.log('Clear filters API Response:', response.data);

      const { data, pagination } = response.data;

      if (!data || !pagination) {
        throw new Error('Invalid API response structure.');
      }

      // Check if there are more pages available based on pagination info
      const totalPages = pagination.totalPages || 1;
      const hasMorePages = 1 < totalPages;

      const userRequests = await Promise.all(
        data.map(async (request: any) => {
          if (request.userId && !request.user) {
            try {
              const userResponse = await axios.get(
                `${process.env.REACT_APP_API_URL}/user/${request.userId}`,
                {
                  headers: {
                    Accept: 'application/json',
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${localStorage.getItem('Token')}`,
                  },
                }
              );
              const userData = userResponse.data.data;
              return {
                ...request,
                email: userData?.email || 'N/A',
                userName: `${userData?.firstName || 'N/A'} ${userData?.lastName || 'N/A'}`,
              };
            } catch (userError) {
              console.error(`Error fetching user data for userId ${request.userId}:`, userError);
              return {
                ...request,
                email: 'N/A',
                userName: 'N/A',
              };
            }
          }

          return {
            ...request,
            email: request.user?.email || 'N/A',
            userName: `${request.user?.firstName || 'N/A'} ${request.user?.lastName || 'N/A'}`,
          };
        })
      );

      // Sort requests by creation date only (newest first)
      const sortedRequests = [...userRequests].sort((a, b) => {
        return new Date(b.createdAt || b.date).getTime() - new Date(a.createdAt || a.date).getTime();
      });

      setGameRequests(sortedRequests);
      setHasMore(hasMorePages);

      const approvedSet = new Set(
        userRequests.filter((request) => request.passwordReset === 'Requested').map((req) => req._id)
      );

      setApprovedRequests(approvedSet);

      // Clear all filter states AFTER successful API call
      setSearchTerm('');
      setDateFilter({
        startDate: null,
        endDate: null
      });
      setUserFilter('');
      setEmailFilter('');
      setGameFilter('');
      setStatusFilter('');

    } catch (error: any) {
      console.error('Error clearing filters and fetching requests:', error.message);
      setError('Failed to clear filters and fetch requests. Please try again later.');
    } finally {
      setLoading(false);
    }
  };


  return (
    <div className="w-full bg-white rounded-lg p-2 sm:p-4 md:p-6 lg:ml-[250px] lg:max-w-[calc(100%-250px)] min-h-[100%] flex flex-col">
      {/* Header Section with Refresh Button */}
      <div className="p-2 border-b flex justify-between items-center">
        <h2 className="text-xl sm:text-2xl font-bold">PASSWORD RESETS</h2>
        <button
          onClick={() => {
            setGameRequests([]);
            setCurrentPage(1);
            setHasMore(true);
            fetchGameRequests(1);
            toast.info("Refreshing data...");
          }}
          className="bg-lime-900 text-white px-3 py-2 rounded-lg hover:bg-lime-800 flex items-center text-sm sm:text-base"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 sm:h-5 sm:w-5 mr-1 sm:mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          Refresh
        </button>
      </div>

      {/* Filters Section with Toggle Button */}
      <div className="w-full bg-[#f5f5f0] rounded-lg mb-4 overflow-hidden border border-gray-200">
        <div className="flex justify-between items-center p-4" onClick={() => setShowFilters(!showFilters)}>
          <div className="flex items-center cursor-pointer">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-[#4D7C0F]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
            </svg>
            <span className="font-medium">Filter</span>
          </div>
          <button className="bg-[#4D7C0F] hover:bg-[#3f6a0a] text-white px-3 py-1 rounded transition-colors text-sm flex items-center">
            <span>{showFilters ? 'Hide Filters' : 'Show Filters'}</span>
            <svg xmlns="http://www.w3.org/2000/svg" className={`h-4 w-4 ml-1 transition-transform duration-200 ${showFilters ? 'rotate-180' : ''}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </button>
        </div>

        {/* Collapsible Filter Content */}
        <div className={`p-4 ${showFilters ? 'block' : 'hidden'}`}>
          {/* First Row - 3 Filters */}
          <div className="flex flex-wrap mb-4">
            {/* General Search */}
            <div className="w-full md:w-1/3 px-2 mb-4 md:mb-0">
              <div className="relative">
                <div className="flex items-center mb-1">
                  <label className="block text-gray-700 text-base font-medium">General Search</label>
                  <Tooltip content="Search by Request ID or Game Name" position="top" delay={300}>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500 hover:text-gray-700 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </Tooltip>
                </div>
                <input
                  type="text"
                  placeholder="Search by Request ID, Game Name..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:border-gray-400"
                />
              </div>
            </div>

            {/* Date Range Filter */}
            <div className="w-full md:w-1/3 px-2 mb-4 md:mb-0">
              <div className="relative">
                <div className="flex items-center mb-1">
                  <label className="block text-gray-700 text-base font-medium">Date Range</label>
                  <Tooltip content="Filter by password reset request date range" position="top" delay={300}>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500 hover:text-gray-700 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </Tooltip>
                </div>
                <div className="relative">
                  <DatePicker
                    selected={dateFilter.startDate}
                    onChange={handleDateRangeChange}
                    startDate={dateFilter.startDate}
                    endDate={dateFilter.endDate}
                    selectsRange
                    className="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:border-gray-400"
                    wrapperClassName="w-full"
                    placeholderText="Select date range"
                    dateFormat="yyyy-MM-dd"
                    isClearable
                    showMonthDropdown
                    showYearDropdown
                    dropdownMode="select"
                    popperPlacement="right"
                    popperClassName="date-picker-popper"
                  />
                </div>
              </div>
            </div>

            {/* User Filter */}
            <div className="w-full md:w-1/3 px-2 mb-4 md:mb-0">
              <div className="relative">
                <div className="flex items-center mb-1">
                  <label className="block text-gray-700 text-base font-medium">User</label>
                  <Tooltip content="Search by username, first name, last name, or full name" position="top" delay={300}>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500 hover:text-gray-700 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </Tooltip>
                </div>
                <input
                  type="text"
                  placeholder="Search by username, first name, or last name"
                  value={userFilter}
                  onChange={(e) => setUserFilter(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:border-gray-400"
                />
              </div>
            </div>
          </div>

          {/* Second Row - 3 Filters */}
          <div className="flex flex-wrap">
            {/* Email Filter */}
            <div className="w-full md:w-1/3 px-2 mb-4 md:mb-0">
              <div className="relative">
                <div className="flex items-center mb-1">
                  <label className="block text-gray-700 text-base font-medium">Email</label>
                  <Tooltip content="Search by user email address (partial matches supported)" position="right" delay={300}>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500 hover:text-gray-700 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </Tooltip>
                </div>
                <input
                  type="text"
                  placeholder="Filter by email"
                  value={emailFilter}
                  onChange={(e) => setEmailFilter(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:border-gray-400"
                />
              </div>
            </div>

            {/* Game Filter */}
            <div className="w-full md:w-1/3 px-2 mb-4 md:mb-0">
              <div className="relative">
                <div className="flex items-center mb-1">
                  <label className="block text-gray-700 text-base font-medium">Game</label>
                  <Tooltip content="Search by game name (partial matches supported)" position="top" delay={300}>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500 hover:text-gray-700 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </Tooltip>
                </div>
                <input
                  type="text"
                  placeholder="Filter by game name"
                  value={gameFilter}
                  onChange={(e) => setGameFilter(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:border-gray-400"
                />
              </div>
            </div>

            {/* Status Filter */}
            <div className="w-full md:w-1/3 px-2 mb-4 md:mb-0">
              <div className="relative">
                <div className="flex items-center mb-1">
                  <label className="block text-gray-700 text-base font-medium">Status</label>
                  <Tooltip content="Filter by request status (Pending, Approved, Rejected)" position="top" delay={300}>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500 hover:text-gray-700 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </Tooltip>
                </div>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:border-gray-400 appearance-none"
                >
                  <option value="">All Statuses</option>
                  <option value="Pending">Pending</option>
                  <option value="Approved">Approved</option>
                  <option value="Rejected">Rejected</option>
                </select>
              </div>
            </div>
          </div>

          {/* Filter Action Buttons */}
          <div className="flex justify-end mt-4 space-x-3">
            <button
              onClick={clearAllFilters}
              disabled={loading}
              className={`flex items-center px-3 py-1.5 rounded transition-colors text-sm ${
                loading
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-gray-200 text-gray-800 hover:bg-gray-300'
              }`}
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-400 mr-1"></div>
                  Clearing...
                </>
              ) : (
                <>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                  Clear All Filters
                </>
              )}
            </button>
            <button
              onClick={handleSearch}
              disabled={loading}
              className={`flex items-center px-4 py-1.5 rounded transition-colors text-sm ${
                loading
                  ? 'bg-gray-400 text-gray-200 cursor-not-allowed'
                  : 'bg-[#4D7C0F] hover:bg-[#3f6a0a] text-white'
              }`}
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-1"></div>
                  Applying...
                </>
              ) : (
                <>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                  Apply Filters
                </>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Table Section */}
      <div className="flex-1 overflow-x-auto">
        <div className="min-w-[1000px] shadow-md rounded-lg border border-gray-200">
          <table className="w-full text-2xl">
            <thead className="bg-lime-900 sticky top-0">
              <tr>
                <th className="px-2 py-3 text-left text-sm sm:text-base font-bold text-white whitespace-nowrap w-[4.5%]">
                  REQ ID
                </th>
                <th className="px-3 py-3 text-left text-sm sm:text-base font-bold text-white whitespace-nowrap w-[10%]">
                  GAME
                </th>
                <th className="px-3 py-3 text-left text-sm sm:text-base font-bold text-white whitespace-nowrap w-[10%]">
                  USER
                </th>
                <th className="px-3 py-3 text-left text-sm sm:text-base font-bold text-white whitespace-nowrap w-[15%]">
                  EMAIL
                </th>
                <th className="px-3 py-3 text-left text-sm sm:text-base font-bold text-white whitespace-nowrap w-[12%]">
                  DATE & TIME
                </th>
                <th className="px-3 py-3 text-left text-sm sm:text-base font-bold text-white whitespace-nowrap w-[10%]">
                  PASSWORD
                </th>
                <th className="px-2 py-3 text-left text-sm sm:text-base font-bold text-white whitespace-nowrap w-[8%]">
                  ACTION
                </th>
                <th className="px-2 py-3 text-left text-sm sm:text-base font-bold text-white whitespace-nowrap w-[8%]">
                  DETAILS
                </th>
                <th className="px-2 py-3 text-left text-sm sm:text-base font-bold text-white whitespace-nowrap w-[6%]">
                  LOGS
                </th>
              </tr>
            </thead>
            <tbody>
              {gameRequests.map((row, index) => (
                <tr key={index} className={`hover:bg-gray-50 ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}>
                  <td className="px-3 py-3 text-gray-700 truncate max-w-[100px]">
                    {row.requestId}
                  </td>
                  <td className="px-3 py-3 text-gray-700 truncate max-w-[100px]">
                    {row.gameName}
                  </td>
                  <td className="px-3 py-3 text-gray-700 truncate max-w-[100px]">
                    {row.userName}
                  </td>
                  <td className="px-3 py-3 text-lime-900 font-medium truncate max-w-[150px]">
                    {row.email}
                  </td>
                  <td className="px-3 py-3 text-gray-700">
                    {formatDateTime(row.date)}
                  </td>
                  <td className="px-3 py-3">
                    <div className="relative flex items-center max-w-[100px]">
                      <input
                        type={showPasswords[row._id] ? 'text' : 'password'}
                        value={
                          approvedRequests.has(row._id)
                            ? row.gamePassword || 'Requested'
                            : passwords[row._id] || passwords[row.gamePassword] || ''  // Set default to gamePassword
                        }
                        onChange={(e) => handlePasswordChange(row._id, e.target.value)}
                        disabled={row.passwordReset === 'Requested'}  // Disable if passwordReset is 'Requested'
                        className="w-full pr-10 px-2 py-1 border rounded text-sm
          bg-gray-100 focus:outline-none focus:ring-2 focus:ring-lime-900"
                        placeholder="Enter password"
                      />
                      <button
                        type="button"
                        onClick={() => togglePasswordVisibility(row._id)}
                        className="absolute right-2 text-gray-500 hover:text-gray-700 focus:outline-none"
                      >
                      </button>
                    </div>
                  </td>
                  <td className="px-3 py-3">
                    <button
                      onClick={() => handleApprove(row)}
                      disabled={!passwords[row._id] || approvedRequests.has(row._id) || approvingRequests.has(row._id)}
                      className={`px-3 py-2 text-white rounded transition-colors text-sm
                      ${approvedRequests.has(row._id)
                          ? 'bg-gray-500 hover:bg-gray-600'
                          : 'bg-lime-900 hover:bg-lime-800'
                        } disabled:opacity-50 disabled:cursor-not-allowed min-w-[80px] flex items-center justify-center`}
                    >
                      {approvingRequests.has(row._id) ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          <span>Processing...</span>
                        </>
                      ) : approvedRequests.has(row._id) ? 'APPROVED' : 'APPROVE'}
                    </button>
                  </td>
                  <td className="px-3 py-3">
                    <button
                      onClick={() => {
                        setSelectedSystem(row);
                        setIsDetailsOpen(true);
                      }}
                      className="px-3 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded transition-colors text-sm"
                    >
                      Details
                    </button>
                  </td>
                  <td className="px-2 py-3">
                    <LogViewerButton
                      entityType="password_reset"
                      entityId={row._id}
                      buttonSize="sm"
                      tooltip="View Activity Logs"
                    />
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {/* Empty state message */}
          {gameRequests.length === 0 && !loading && (
            <div className="text-center py-8">
              <p className="text-xl text-gray-500">No password requests found</p>
            </div>
          )}

          {/* Loading indicator */}
          <div className="flex justify-center items-center w-full" ref={loadingRef}>
            {loading ? (
              <div className="flex justify-center items-center w-full py-6">
                <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-lime-900"></div>
                <span className="ml-3 text-lime-900 font-medium">Loading data...</span>
              </div>
            ) : hasMore ? (
              <div className="py-4 text-gray-500">Scroll for more...</div>
            ) : gameRequests.length > 0 ? (
              <div className="py-4 text-gray-500">No more requests to load</div>
            ) : null}
          </div>
          {isDetailsOpen && selectedSystem && selectedSystem.gameFields && (
            <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
              <div className="bg-white p-4 rounded-lg shadow-lg w-11/12 max-w-md">
                <h3 className="text-lg font-bold mb-4">Request Details</h3>

                {/* Request and Approval Time Section */}
                <div className="mb-4 p-3 bg-gray-50 rounded">
                  <div className="flex justify-between mb-2">
                    <span className="font-semibold">Requested Time:</span>
                    <span className="text-right">{formatDateTime(selectedSystem.createdAt || selectedSystem.date)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-semibold">Approval Time:</span>
                    <span className="text-right">
                      {selectedSystem.updatedAt && selectedSystem.passwordReset === 'Requested'
                        ? formatDateTime(selectedSystem.updatedAt)
                        : 'Not yet approved'}
                    </span>
                  </div>
                </div>

                <ul className="space-y-2">
                  {/* Display all properties from selectedSystem.gameFields */}
                  {Object.entries(selectedSystem.gameFields).map(([key, value]) => (
                    <li key={key} className="flex justify-between">
                      <span className="font-semibold">{key}:</span>
                      <span className="text-right">
                        {value !== null && typeof value !== 'object'
                          ? String(value)
                          : value === null
                            ? 'null'
                            : typeof value === 'object' && !Array.isArray(value)
                              ? 'Object'
                              : Array.isArray(value)
                                ? 'Array'
                                : 'N/A'}
                      </span>
                    </li>
                  ))}
                </ul>
                <div className="mt-4 text-right">
                  <button
                    onClick={() => setIsDetailsOpen(false)}
                    className="px-4 py-2 bg-lime-900 text-white rounded hover:bg-lime-600"
                  >
                    Close
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PasswordReset;