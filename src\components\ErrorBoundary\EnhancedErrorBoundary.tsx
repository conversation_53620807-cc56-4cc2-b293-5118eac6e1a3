import React, { Component, ErrorInfo, ReactNode } from 'react';
import { FiAlertTriangle, FiRefreshCw } from 'react-icons/fi';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  componentName?: string;
  showDetails?: boolean;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

/**
 * Enhanced Error Boundary component that provides a better fallback UI
 * and handles errors differently based on the environment.
 */
class EnhancedErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // Log the error to an error reporting service
    this.logErrorToService(error, errorInfo);
    
    this.setState({
      error,
      errorInfo
    });
  }

  logErrorToService(error: Error, errorInfo: ErrorInfo): void {
    // In a real app, you would send this to your error tracking service
    // like Sentry, LogRocket, etc.
    console.error('Error caught by EnhancedErrorBoundary:', {
      error: error.toString(),
      componentStack: errorInfo.componentStack,
      componentName: this.props.componentName || 'Unknown'
    });

    // You could also log to your backend API
    // Example:
    // fetch('/api/log-error', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify({
    //     error: error.toString(),
    //     stack: error.stack,
    //     componentStack: errorInfo.componentStack,
    //     componentName: this.props.componentName || 'Unknown',
    //     url: window.location.href,
    //     userAgent: navigator.userAgent,
    //     timestamp: new Date().toISOString()
    //   })
    // }).catch(console.error);
  }

  handleRetry = (): void => {
    this.setState({ hasError: false, error: null, errorInfo: null });
  };

  handleReload = (): void => {
    window.location.reload();
  };

  render(): ReactNode {
    const { hasError, error } = this.state;
    const { children, fallback, showDetails } = this.props;
    
    if (hasError) {
      // If a custom fallback is provided, use it
      if (fallback) {
        return fallback;
      }

      // Check if we're in development or production
      const isDev = process.env.NODE_ENV === 'development';
      
      return (
        <div className="flex flex-col items-center justify-center p-6 rounded-lg bg-white shadow-md m-4 text-center">
          <FiAlertTriangle className="text-red-500 w-16 h-16 mb-4" />
          
          <h2 className="text-xl font-bold text-gray-800 mb-2">
            {isDev ? 'Something went wrong' : 'Oops! Something went wrong'}
          </h2>
          
          <p className="text-gray-600 mb-4">
            {isDev 
              ? 'An error occurred in this component.' 
              : 'We encountered an unexpected issue. Our team has been notified.'}
          </p>
          
          {/* Show error details only in development or if explicitly allowed */}
          {(isDev || showDetails) && error && (
            <div className="bg-gray-100 p-4 rounded mb-4 text-left w-full overflow-auto max-h-48">
              <p className="font-mono text-red-600 text-sm">{error.toString()}</p>
              {error.stack && (
                <pre className="mt-2 text-xs text-gray-700 whitespace-pre-wrap">
                  {error.stack}
                </pre>
              )}
            </div>
          )}
          
          <div className="flex space-x-4 mt-2">
            <button
              onClick={this.handleRetry}
              className="flex items-center px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
            >
              <FiRefreshCw className="mr-2" /> Try Again
            </button>
            
            <button
              onClick={this.handleReload}
              className="px-4 py-2 border border-gray-300 rounded hover:bg-gray-100 transition-colors"
            >
              Reload Page
            </button>
          </div>
        </div>
      );
    }

    return children;
  }
}

export default EnhancedErrorBoundary;
