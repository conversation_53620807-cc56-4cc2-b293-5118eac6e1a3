import { useState, useEffect } from 'react';
import axios from 'axios';
import { 
  DateRange, 
  GameFilters, 
  GameTransaction, 
  SystemTotals, 
  ReportData,
  GameTransactionsResponse,
  AnalyticsResponse,
  DashboardResponse
} from '../types/reports.types';

export const useReportsData = () => {
  // State management
  const [activeTab, setActiveTab] = useState<string>('daily');
  const [dateRange, setDateRange] = useState<DateRange[]>([
    {
      startDate: new Date(new Date().setDate(new Date().getDate() - 30)),
      endDate: new Date(),
      key: 'selection',
    },
  ]);
  
  const [gameFilters, setGameFilters] = useState<GameFilters>({
    search: '',
    gameName: '',
    userSearch: '',
    status: '',
    dateFilter: {
      startDate: null,
      endDate: null,
    },
    amountFilter: {
      min: '',
      max: '',
    },
  });

  const [gameTransactions, setGameTransactions] = useState<GameTransaction[]>([]);
  const [gameTransactionsPagination, setGameTransactionsPagination] = useState({
    currentPage: 1,
    totalItems: 0,
    hasMore: false,
  });
  
  const [systemTotals, setSystemTotals] = useState<SystemTotals>({
    totalSweepsCoins: 0,
    totalGoldCoins: 0,
    totalGamePurchases: 0,
    totalCashRedeems: 0,
  });
  
  const [reportData, setReportData] = useState<ReportData[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [gameTransactionsLoading, setGameTransactionsLoading] = useState<boolean>(false);

  // API functions
  const fetchReportData = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('Token') || '';

      // Build query parameters for analytics endpoint
      const params = new URLSearchParams();
      params.append('pageNumber', '1');
      params.append('limit', '50');
      
      // Add date range filter if available
      if (dateRange?.[0]?.startDate && dateRange?.[0]?.endDate) {
        params.append('startDate', dateRange[0].startDate.toISOString().split('T')[0]);
        params.append('endDate', dateRange[0].endDate.toISOString().split('T')[0]);
      }

      const url = `${process.env.REACT_APP_API_URL}/analytics?${params.toString()}`;

      const response = await axios.get<AnalyticsResponse>(url, {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      });

      if (response?.data?.data) {
        setReportData(response.data.data);
        console.log('Analytics data received:', response.data.data);
      } else {
        console.log('No analytics data received:', response.data);
        setReportData([]);
      }
    } catch (error) {
      console.error('Error fetching analytics data:', error);
      setReportData([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchSystemTotals = async () => {
    try {
      const token = localStorage.getItem('Token') || '';

      const url = `${process.env.REACT_APP_API_URL}/reports/dashboard`;

      const response = await axios.get<DashboardResponse>(url, {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      });

      if (response?.data?.success && response?.data?.data) {
        // Extract system totals from dashboard response
        const dashboardData = response.data.data;
        setSystemTotals({
          totalSweepsCoins: dashboardData.totalSweepsCoins || 0,
          totalGoldCoins: dashboardData.totalGoldCoins || 0,
          totalGamePurchases: dashboardData.totalPurchases || 0,
          totalCashRedeems: dashboardData.totalRedeems || 0
        });
        console.log('System totals received:', dashboardData);
      } else {
        console.log('No system totals data received:', response.data);
      }
    } catch (error) {
      console.error('Error fetching system totals:', error);
    }
  };

  const fetchGameTransactions = async (page: number = 1) => {
    try {
      setGameTransactionsLoading(true);
      const token = localStorage.getItem('Token') || '';

      // Build query parameters
      const params = new URLSearchParams();
      params.append('page', page.toString());
      params.append('limit', '25');

      // Add filters
      if (gameFilters.search) params.append('search', gameFilters.search);
      if (gameFilters.gameName) params.append('gameName', gameFilters.gameName);
      if (gameFilters.userSearch) params.append('userSearch', gameFilters.userSearch);
      if (gameFilters.status) params.append('status', gameFilters.status);
      if (gameFilters.dateFilter.startDate) {
        params.append('startDate', gameFilters.dateFilter.startDate.toISOString().split('T')[0]);
      }
      if (gameFilters.dateFilter.endDate) {
        params.append('endDate', gameFilters.dateFilter.endDate.toISOString().split('T')[0]);
      }
      if (gameFilters.amountFilter.min) params.append('minAmount', gameFilters.amountFilter.min);
      if (gameFilters.amountFilter.max) params.append('maxAmount', gameFilters.amountFilter.max);

      const apiUrl = `${process.env.REACT_APP_API_URL}/api/game-transactions?${params.toString()}`;

      const response = await axios.get<GameTransactionsResponse>(apiUrl, {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      });

      if (response?.data?.success && response?.data?.data) {
        if (page === 1) {
          setGameTransactions(response.data.data);
        } else {
          setGameTransactions(prev => [...prev, ...response.data.data]);
        }

        setGameTransactionsPagination({
          currentPage: response.data.pagination?.currentPage || page,
          totalItems: response.data.pagination?.totalItems || 0,
          hasMore: response.data.pagination?.hasMore || false,
        });
        console.log('Game transactions received:', response.data.data);
      } else {
        console.log('No game transactions data received:', response.data);
        setGameTransactions([]);
      }
    } catch (error) {
      console.error('Error fetching game transactions:', error);
      setGameTransactions([]);
    } finally {
      setGameTransactionsLoading(false);
    }
  };

  // Effects
  useEffect(() => {
    fetchReportData();
    fetchSystemTotals();
    fetchGameTransactions(1);
  }, [activeTab, dateRange]);

  useEffect(() => {
    fetchGameTransactions(1);
  }, [gameFilters]);

  // Handler functions
  const handleGameFilterChange = (key: keyof GameFilters, value: any) => {
    setGameFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleDateRangeChange = (ranges: any) => {
    setDateRange([ranges.selection]);
  };

  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
  };

  const handlePageChange = (page: number) => {
    fetchGameTransactions(page);
  };

  return {
    // State
    activeTab,
    dateRange,
    gameFilters,
    gameTransactions,
    gameTransactionsPagination,
    systemTotals,
    reportData,
    loading,
    gameTransactionsLoading,
    
    // Actions
    handleGameFilterChange,
    handleDateRangeChange,
    handleTabChange,
    handlePageChange,
    fetchReportData,
    fetchSystemTotals,
    fetchGameTransactions,
  };
};
