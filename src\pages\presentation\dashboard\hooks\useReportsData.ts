// ===================================================================
// REPORTS DATA HOOK - RTK QUERY IMPLEMENTATION
// ===================================================================

import { useState, useMemo } from 'react';
import {
  useGetSystemTotalsQuery,
  useGetAnalyticsDataQuery,
  useGetGameTransactionsQuery
} from '../../../../redux/api/reportsApi';
import {
  DateRange,
  GameFilters
} from '../types/reports.types';

export const useReportsData = () => {
  const [activeTab, setActiveTab] = useState<'daily' | 'weekly' | 'monthly'>('daily');
  const [dateRange, setDateRange] = useState<DateRange>({
    startDate: new Date(new Date().setDate(new Date().getDate() - 30)),
    endDate: new Date(),
    key: 'selection',
  });

  const [gameFilters, setGameFilters] = useState<GameFilters>({
    search: '',
    gameName: '',
    userSearch: '',
    status: '',
    dateFilter: {
      startDate: null,
      endDate: null,
    },
    amountFilter: {
      min: '',
      max: '',
    },
  });

  const [currentPage, setCurrentPage] = useState(1);

  const {
    data: systemTotals,
    isLoading: systemTotalsLoading,
    error: systemTotalsError
  } = useGetSystemTotalsQuery();

  // Analytics data query with proper parameters
  const analyticsQueryParams = useMemo(() => ({
    groupBy: activeTab,
    startDate: dateRange.startDate.toISOString().split('T')[0],
    endDate: dateRange.endDate.toISOString().split('T')[0],
    pageNumber: 1,
    limit: 50
  }), [activeTab, dateRange]);

  const {
    data: reportData,
    isLoading: reportDataLoading,
    error: reportDataError
  } = useGetAnalyticsDataQuery(analyticsQueryParams);

  // Game transactions query with all filters
  const gameTransactionsQueryParams = useMemo(() => ({
    page: currentPage,
    limit: 25,
    search: gameFilters.search || undefined,
    gameName: gameFilters.gameName || undefined,
    userSearch: gameFilters.userSearch || undefined,
    status: gameFilters.status || undefined,
    startDate: gameFilters.dateFilter.startDate?.toISOString().split('T')[0],
    endDate: gameFilters.dateFilter.endDate?.toISOString().split('T')[0],
    minAmount: gameFilters.amountFilter.min || undefined,
    maxAmount: gameFilters.amountFilter.max || undefined,
  }), [gameFilters, currentPage]);

  const {
    data: gameTransactionsData,
    isLoading: gameTransactionsLoading,
    error: gameTransactionsError
  } = useGetGameTransactionsQuery(gameTransactionsQueryParams);

  const gameTransactions = gameTransactionsData?.data || [];
  const gameTransactionsPagination = gameTransactionsData?.pagination || {
    currentPage: 1,
    totalItems: 0,
    hasMore: false,
  };

  // Combine loading states
  const loading = systemTotalsLoading || reportDataLoading;

  // Default values for data
  const safeSystemTotals = systemTotals || {
    totalSweepsCoins: 0,
    totalGoldCoins: 0,
    totalGamePurchases: 0,
    totalCashRedeems: 0,
  };

  const safeReportData = reportData || [];

  // ===================================================================
  // HANDLER FUNCTIONS
  // ===================================================================

  const handleGameFilterChange = (key: keyof GameFilters, value: any) => {
    setGameFilters(prev => ({
      ...prev,
      [key]: value
    }));
    // Reset to first page when filters change
    setCurrentPage(1);
  };

  const handleDateRangeChange = (ranges: any) => {
    setDateRange(ranges.selection);
  };

  const handleTabChange = (tab: 'daily' | 'weekly' | 'monthly') => {
    setActiveTab(tab);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // ===================================================================
  // RETURN HOOK INTERFACE
  // ===================================================================

  return {
    // State
    activeTab,
    dateRange,
    gameFilters,
    gameTransactions,
    gameTransactionsPagination,
    systemTotals: safeSystemTotals,
    reportData: safeReportData,
    loading,
    gameTransactionsLoading,

    // Actions
    handleGameFilterChange,
    handleDateRangeChange,
    handleTabChange,
    handlePageChange,

    // Legacy compatibility (for components that might still expect these)
    fetchReportData: () => Promise.resolve(),
    fetchSystemTotals: () => Promise.resolve(),
    fetchGameTransactions: () => Promise.resolve(),
  } as const;
};
