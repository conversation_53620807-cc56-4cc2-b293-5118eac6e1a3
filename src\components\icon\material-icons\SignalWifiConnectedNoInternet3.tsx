import React, { SVGProps } from 'react';

const SvgSignalWifiConnectedNoInternet3 = (props: SVGProps<SVGSVGElement>) => {
  return (
    <svg viewBox="0 0 24 24" fill="currentColor" className="svg-icon" {...props}>
      <path d="M0 0h24v24H0V0z" fill="none" />
      <path
        d="M21.18 11.8L24 8.98A16.88 16.88 0 0012 4C7.31 4 3.07 5.9 0 8.98l2.82 2.82C5.17 9.45 8.41 8 12 8s6.83 1.45 9.18 3.8z"
        fillOpacity={0.3}
      />
      <path d="M21.18 11.8C18.83 9.45 15.59 8 12 8s-6.83 1.45-9.18 3.8L12 21v-9h8.99l.19-.2zM19.59 14l-2.09 2.09L15.41 14 14 15.41l2.09 2.09L14 19.59 15.41 21l2.09-2.08L19.59 21 21 19.59l-2.08-2.09L21 15.41 19.59 14z" />
    </svg>
  );
};

export default SvgSignalWifiConnectedNoInternet3;
