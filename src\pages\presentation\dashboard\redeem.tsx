import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { RootState } from '../../../redux/store';
import { FaUniversity, FaGamepad, FaCreditCard, FaTrash, FaStar, FaRegStar } from 'react-icons/fa';
import { toast } from 'react-toastify';
import { useRedeemHistory } from '../../../hooks/useRedeemHistory';
import WarningBanner from './components/Alert';

interface WalletData {
  id: string;
  swipeCoins: number;
}

interface Transaction {
  id: string;
  type: 'deposit' | 'redeem' | 'purchase' | 'refund' | 'bonus';
  amount: number;
  date: string;
  coinType: string;
  paymentMethod?: string;
  status?: string;
  description?: string;
  cardDetails?: {
    cardId: string;
    lastFour: string;
  };
}

interface SavedCard {
  _id: string;
  cardholderName: string;
  lastFour: string;
  expirationMonth: string;
  expirationYear: string;
  cardType: string;
  isDefault: boolean;
  createdAt: string;
}

// API configuration
const API_BASE_URL = process.env.REACT_APP_API_URL;

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth interceptor
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('Token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

const RedeemSection = () => {
  const { user, loading: isloading }: any = useSelector((state: RootState) => state.auth);
  const navigate = useNavigate();
  const [hasWallet, setHasWallet] = useState(false);
  const [amount, setAmount] = useState('');
  const [loading, setLoading] = useState(false);
  const [notification, setNotification] = useState({
    show: false,
    message: '',
    type: '',
  });
  const [walletData, setWalletData] = useState<WalletData>({
    id: '1',
    swipeCoins: 0,
  });
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [paymentMethod, setPaymentMethod] = useState<'bank' | 'game-time' | 'push-to-card' | ''>('');

  // Use the redeemHistory hook to track redemption history for validation
  const { transactions: redeemTransactions, loading: redeemHistoryLoading } = useRedeemHistory({
    userId: user?._id || '',
    pageSize: 100, // Fetch a larger page size to ensure we get all today's transactions
  });
  const [cardDetails, setCardDetails] = useState({
    cardNumber: '',
    expirationMonth: '',
    expirationYear: '',
    cardholderName: '',
    cvv: ''
  });
  const [showCardForm, setShowCardForm] = useState(false);
  const [paymentSuccess, setPaymentSuccess] = useState(false);
  const [paymentDetails, setPaymentDetails] = useState<{
    id: string;
    amount: number;
    status: string;
    lastFour: string;
  } | null>(null);
  const [saveCard, setSaveCard] = useState(false);
  const [savedCards, setSavedCards] = useState<SavedCard[]>([]);
  const [useExistingCard, setUseExistingCard] = useState(false);
  const [selectedCardId, setSelectedCardId] = useState<string>('');

  // Helper function to check daily redemption total using redeemTransactions from useRedeemHistory
  const getDailyRedemptionTotal = () => {
    const today = new Date().toLocaleDateString();

    // Filter today's redemption transactions from the redeemHistory hook
    // This uses the /orders endpoint with paymentMethod=push-to-card filter
    const todaysRedemptions = redeemTransactions.filter((t: any) => {
      // Convert the date to a string for comparison
      const transactionDate = new Date(t.date).toLocaleDateString();
      return transactionDate === today && t.status !== 'cancelled' && t.status !== 'failed';
    });

    // Calculate total amount redeemed today
    return todaysRedemptions.reduce((total: number, t: any) => total + t.amount, 0);
  };

  const email = localStorage.getItem('email');

  // Filter transactions to only show swipe coins
  const filteredTransactions = transactions.filter(t => t.coinType === 'swipeCoin');

  // Group transactions by date for better organization
  const groupedTransactions = filteredTransactions.reduce((groups, transaction) => {
    const date = new Date(transaction.date).toLocaleDateString();
    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(transaction);
    return groups;
  }, {} as Record<string, Transaction[]>);

  // Sort dates in descending order (newest first)
  const sortedDates = Object.keys(groupedTransactions).sort((a, b) => {
    return new Date(b).getTime() - new Date(a).getTime();
  });

  // Payment method options for redeem
  const redeemPaymentMethods = [
    // { id: 'bank', name: 'Bank Transfer', icon: <FaUniversity className="mr-2" /> },
    // { id: 'game-time', name: 'Game Time Wallet', icon: <FaGamepad className="mr-2" /> },
    { id: 'push-to-card', name: 'Instant to Card', icon: <FaCreditCard className="mr-2" /> },
  ];

  const checkKYCStatus = async () => {
    try {
      const userId = localStorage.getItem('userId');
      const response = await axios.get(`${API_BASE_URL}/api/kyc/verify`, {
        params: {
          user_id: userId,
          email: email,
        },
        headers: {
          Authorization: `Bearer ${localStorage.getItem('Token')}`,
        },
      });

      return response.data.is_verified;
    } catch (error) {
      console.error('Error checking KYC status:', error);
      return false;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const amountNum = Number(amount);

    // Validate amount is provided and positive
    if (!amount || amountNum <= 0) {
      showNotification('Please enter a valid amount', 'error');
      return;
    }

    // Validate minimum redemption amount ($50)
    if (amountNum < 50) {
      showNotification('Minimum redemption amount is $50', 'error');
      return;
    }

    // Validate maximum redemption amount ($500)
    if (amountNum > 500) {
      showNotification('Maximum redemption amount is $500', 'error');
      return;
    }

    // Check daily redemption limit using the redeemTransactions data
    if (!redeemHistoryLoading) {
      const dailyTotal = getDailyRedemptionTotal();
      if (dailyTotal + amountNum > 500) {
        showNotification(`Daily redemption limit is $500. You have already redeemed $${dailyTotal.toFixed(2)} today.`, 'error');
        return;
      }
    }

    if (!paymentMethod) {
      showNotification('Please select a payment method', 'error');
      return;
    }

    // Check if KYC is required
    const isKYCVerified = await checkKYCStatus();

    if (!isKYCVerified) {
      localStorage.setItem('pendingWalletRecharge', JSON.stringify({
        amount,
        walletType: 'swipeCoins',
        paymentMethod: paymentMethod
      }));
      navigate('/kyc-verify');
      return;
    }

    await handleRedeemRequest();
  };

  const handleRedeemRequest = async () => {
    setLoading(true);
    try {
      // Check if user has enough balance
      if (Number(amount) > walletData.swipeCoins) {
        showNotification('Insufficient balance', 'error');
        return;
      }

      // If push to card is selected, handle card payment
      if (paymentMethod === 'push-to-card') {
        await handlePushToCardPayment();
        return;
      }

      const response = await api.post('/wallet/redeem', {
        amount: Number(amount),
        paymentMethod: paymentMethod,
        coinType: 'swipeCoin'
      });

      if (response.data.success) {
        await fetchWalletData();
        await fetchTransactions();
        showNotification(`Successfully requested redemption of ${amount} coins`, 'success');
        setAmount('');
        setPaymentMethod('');
      } else {
        throw new Error(response.data.message || 'Redemption request failed');
      }
    } catch (error) {
      console.error('Error processing redemption request:', error);
      showNotification('Failed to process redemption request', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handlePushToCardPayment = async () => {
    try {
      let cardData;

      if (useExistingCard && selectedCardId) {
        // Find the selected card
        const selectedCard = savedCards.find(card => card._id === selectedCardId);
        if (!selectedCard) {
          showNotification('Please select a card', 'error');
          return;
        }

        // We only have partial card details for saved cards
        cardData = {
          cardholderName: selectedCard.cardholderName,
          // These fields will be ignored by the backend since we're using a saved card
          cardNumber: `xxxxxxxxxxxx${selectedCard.lastFour}`,
          expirationMonth: selectedCard.expirationMonth,
          expirationYear: selectedCard.expirationYear
        };
      } else {
        // Validate card details for new card
        if (!cardDetails.cardNumber || !cardDetails.expirationMonth ||
            !cardDetails.expirationYear || !cardDetails.cardholderName || !cardDetails.cvv) {
          showNotification('Please fill in all card details including CVV code', 'error');
          return;
        }

        // Basic card validation
        if (!/^\d{15,16}$/.test(cardDetails.cardNumber.replace(/\s/g, ''))) {
          showNotification('Please enter a valid card number', 'error');
          return;
        }

        // CVV validation (3-4 digits)
        if (!/^\d{3,4}$/.test(cardDetails.cvv)) {
          showNotification('Please enter a valid CVV code (3-4 digits)', 'error');
          return;
        }

        cardData = {
          cardNumber: cardDetails.cardNumber.replace(/\s/g, ''),
          expirationMonth: cardDetails.expirationMonth,
          expirationYear: cardDetails.expirationYear,
          cardholderName: cardDetails.cardholderName,
          cvv: cardDetails.cvv
        };
      }

      // Call backend to process push to card payment
      const response = await api.post('/redeem/virtual-card', {
        userId: user?._id,
        amount: Number(amount),
        email: email || user?.email,
        cardDetails: cardData,
        saveCard: !useExistingCard && saveCard
      });

      if (response.data.success) {
        // Store payment details
        setPaymentDetails(response.data.paymentDetails);

        // Show success message
        setPaymentSuccess(true);

        // Update wallet and transactions
        await fetchWalletData();
        await fetchTransactions();

        // If we saved a new card, refresh the saved cards list
        if (!useExistingCard && saveCard) {
          await fetchSavedCards();
        }

        showNotification(`Successfully sent ${amount} coins to your card`, 'success');
        setAmount('');
        setPaymentMethod('');
        setShowCardForm(false);
        setUseExistingCard(false);
        setSaveCard(false);
      } else {
        throw new Error(response.data.message || 'Card payment failed');
      }
    } catch (error: any) {
      console.log(error)
      toast.error(error.response.data.error); 
      console.error('Error processing card payment:', error);
      showNotification('Failed to process card payment', 'error');
    }
  };

  const fetchSavedCards = async () => {
    try {
      const response = await api.get(`/cards/${user?._id}`);
      if (response.data.success && response.data.savedCards) {
        setSavedCards(response.data.savedCards);

        // If there are saved cards, check if there's a default one
        const defaultCard = response.data.savedCards.find((card: SavedCard) => card.isDefault);
        if (defaultCard) {
          setSelectedCardId(defaultCard._id);
        } else if (response.data.savedCards.length > 0) {
          setSelectedCardId(response.data.savedCards[0]._id);
        }
      }
    } catch (error) {
      console.error('Error fetching saved cards:', error);
    }
  };

  const handleDeleteCard = async (cardId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      setLoading(true);
      const response = await api.delete(`/cards/${user?._id}/${cardId}`);
      if (response.data.success) {
        showNotification('Card deleted successfully', 'success');
        setSavedCards(response.data.savedCards);
        if (selectedCardId === cardId) {
          setSelectedCardId('');
        }
      }
    } catch (error) {
      console.error('Error deleting card:', error);
      showNotification('Failed to delete card', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleSetDefaultCard = async (cardId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      setLoading(true);
      const response = await api.put(`/cards/${user?._id}/${cardId}/default`);
      if (response.data.success) {
        showNotification('Default card updated', 'success');
        setSavedCards(response.data.savedCards);
      }
    } catch (error) {
      console.error('Error setting default card:', error);
      showNotification('Failed to update default card', 'error');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (user?._id && paymentMethod === 'push-to-card') {
      fetchSavedCards();
    }
  }, [user?._id, paymentMethod]);

  const showNotification = (message: string, type: string) => {
    setNotification({ show: true, message, type });
    setTimeout(() => {
      setNotification({ show: false, message: '', type: '' });
    }, 3000);
  };

  const fetchWalletData = async () => {
    try {
      const response = await api.get(`/wallet/balance/${user?._id}`);
      console.log(response.data.data);
      setWalletData(response.data.data);
    } catch (error) {
      console.error('Error fetching wallet data:', error);
      showNotification('Failed to fetch wallet data', 'error');
    }
  };

  const fetchTransactions = async () => {
    try {
      const response = await api.get(`/wallet/transactions/${user?._id}`);
      console.log(response.data);
      setTransactions(response.data.data);
    } catch (error) {
      console.error('Error fetching transactions:', error);
      showNotification('Failed to fetch transactions', 'error');
    }
  };

  const renderPaymentMethodSelector = () => {
    return (
      <div className="mt-4">
        <label className="block text-sm font-medium mb-2">Select Payment Method</label>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
          {redeemPaymentMethods.map((method) => (
            <button
              key={method.id}
              type="button"
              className={`flex items-center justify-center px-4 py-2 border ${paymentMethod === method.id
                ? 'border-lime-500 bg-lime-50 text-lime-700'
                : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                } rounded-md focus:outline-none`}
              onClick={() => {
                setPaymentMethod(method.id as any);
                if (method.id === 'push-to-card') {
                  setShowCardForm(true);
                } else {
                  setShowCardForm(false);
                }
              }}
              disabled={loading}
            >
              {method.icon}
              {method.name}
            </button>
          ))}
        </div>
      </div>
    );
  };

  const renderCardForm = () => {
    if (!showCardForm) return null;

    return (
      <div className="mt-4 p-4 border border-gray-200 rounded-md">
        {savedCards.length > 0 && (
          <div className="mb-4">
            <div className="flex items-center mb-2">
              <input
                type="checkbox"
                id="useExistingCard"
                checked={useExistingCard}
                onChange={() => setUseExistingCard(!useExistingCard)}
                className="mr-2"
              />
              <label htmlFor="useExistingCard" className="text-sm font-medium">
                Use existing card
              </label>
            </div>

            {useExistingCard && (
              <div className="mt-2 space-y-2">
                {savedCards.map((card) => (
                  <div
                    key={card._id}
                    onClick={() => setSelectedCardId(card._id)}
                    className={`p-3 border rounded-md cursor-pointer flex justify-between items-center ${
                      selectedCardId === card._id ? 'border-lime-500 bg-lime-50' : 'border-gray-200'
                    }`}
                  >
                    <div className="flex items-center">
                      <div className="mr-3">
                        {card.cardType === 'Visa' && <span className="font-bold text-blue-600">VISA</span>}
                        {card.cardType === 'MasterCard' && <span className="font-bold text-red-600">MC</span>}
                        {card.cardType === 'AmEx' && <span className="font-bold text-blue-800">AMEX</span>}
                        {card.cardType === 'Discover' && <span className="font-bold text-orange-600">DISC</span>}
                        {!['Visa', 'MasterCard', 'AmEx', 'Discover'].includes(card.cardType) &&
                          <span className="font-bold text-gray-600">CARD</span>}
                      </div>
                      <div>
                        <div className="font-medium">{card.cardholderName}</div>
                        <div className="text-sm text-gray-600">•••• {card.lastFour}</div>
                        <div className="text-xs text-gray-500">Expires: {card.expirationMonth}/{card.expirationYear.slice(-2)}</div>
                      </div>
                    </div>
                    <div className="flex items-center">
                      {card.isDefault ? (
                        <FaStar className="text-yellow-500 mr-2" title="Default card" />
                      ) : (
                        <FaRegStar
                          className="text-gray-400 mr-2 hover:text-yellow-500"
                          title="Set as default"
                          onClick={(e) => handleSetDefaultCard(card._id, e)}
                        />
                      )}
                      <FaTrash
                        className="text-red-500 hover:text-red-700"
                        title="Delete card"
                        onClick={(e) => handleDeleteCard(card._id, e)}
                      />
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {(!useExistingCard || savedCards.length === 0) && (
          <>
            <h3 className="text-lg font-medium mb-4">Enter Card Details</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">Cardholder Name</label>
                <input
                  type="text"
                  value={cardDetails.cardholderName}
                  onChange={(e) => setCardDetails({...cardDetails, cardholderName: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-lime-500"
                  placeholder="Name on card"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Card Number</label>
                <input
                  type="text"
                  value={cardDetails.cardNumber}
                  onChange={(e) => {
                    // Format card number with spaces every 4 digits
                    const input = e.target.value.replace(/\D/g, '');
                    const formatted = input.replace(/(\d{4})(?=\d)/g, '$1 ');
                    setCardDetails({...cardDetails, cardNumber: formatted});
                  }}
                  maxLength={19} // 16 digits + 3 spaces
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-lime-500"
                  placeholder="1234 5678 9012 3456"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Expiration Month</label>
                  <select
                    value={cardDetails.expirationMonth}
                    onChange={(e) => setCardDetails({...cardDetails, expirationMonth: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-lime-500"
                  >
                    <option value="">Month</option>
                    {Array.from({ length: 12 }, (_, i) => {
                      const month = (i + 1).toString().padStart(2, '0');
                      return <option key={month} value={month}>{month}</option>;
                    })}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Expiration Year</label>
                  <select
                    value={cardDetails.expirationYear}
                    onChange={(e) => setCardDetails({...cardDetails, expirationYear: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-lime-500"
                  >
                    <option value="">Year</option>
                    {Array.from({ length: 10 }, (_, i) => {
                      const year = (new Date().getFullYear() + i).toString();
                      return <option key={year} value={year}>{year}</option>;
                    })}
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">CVV Code</label>
                <input
                  type="password"
                  maxLength={4}
                  value={cardDetails.cvv}
                  onChange={(e) => {
                    // Only allow numbers
                    const input = e.target.value.replace(/\D/g, '');
                    setCardDetails({...cardDetails, cvv: input});
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-lime-500"
                  placeholder="123"
                />
                <p className="text-xs text-gray-500 mt-1">3 or 4 digit security code on the back of your card</p>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="saveCard"
                  checked={saveCard}
                  onChange={() => setSaveCard(!saveCard)}
                  className="mr-2"
                />
                <label htmlFor="saveCard" className="text-sm">
                  Save card for future redemptions
                </label>
              </div>
            </div>
          </>
        )}
      </div>
    );
  };

  const renderSuccessModal = () => {
    if (!paymentSuccess || !paymentDetails) return null;

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 max-w-md w-full">
          <h3 className="text-xl font-bold mb-4 text-[#495e26]">Payment Successful!</h3>

          <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
            <div className="flex items-center mb-2">
              <FaCreditCard className="text-green-600 mr-2" />
              <span className="font-medium">Card Payment Complete</span>
            </div>

            <p className="text-gray-700">
              We've successfully sent <span className="font-semibold">${paymentDetails.amount.toFixed(2)}</span> to
              your card ending in <span className="font-semibold">{paymentDetails.lastFour}</span>.
            </p>

            <p className="text-gray-700 mt-2">
              Payment ID: <span className="font-mono text-sm">{paymentDetails.id}</span>
            </p>

            <p className="text-gray-700 mt-2">
              Status: <span className="inline-block px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-semibold">
                {paymentDetails.status}
              </span>
            </p>
          </div>

          <div className="mb-4 text-sm text-gray-600">
            <p>The funds should be available on your card shortly.</p>
            <p className="mt-2">Thank you for using our service!</p>
          </div>

          <div className="flex justify-end">
            <button
              onClick={() => setPaymentSuccess(false)}
              className="bg-[#495e26] text-white px-4 py-2 rounded hover:bg-[#3b4d1f]"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    );
  };

  useEffect(() => {
    // Check for any pending recharge after KYC verification
    const pendingRecharge = localStorage.getItem('pendingWalletRecharge');

    if (pendingRecharge) {
      const rechargeData = JSON.parse(pendingRecharge);
      setAmount(rechargeData.amount);
      if (rechargeData.paymentMethod) {
        setPaymentMethod(rechargeData.paymentMethod);
      }
      localStorage.removeItem('pendingWalletRecharge');
    }
  }, []);

  useEffect(() => {
    if (!user?._id) return;

    setLoading(true);

    // Immediately invoked async function
    (async () => {
      try {
        let walletExists = false;

        // Check if wallet exists
        try {
          await api.get(`/wallet/check/${user._id}`);
          walletExists = true;
          console.log('Existing wallet found');
        } catch (checkError: any) {
          if (checkError.response?.status === 404 &&
            checkError.response?.data?.error === "Wallet not found") {
            console.log('Wallet not found, will create one');
          } else {
            console.error('Unexpected error checking wallet:', checkError);
            toast.error('Error checking wallet status');
            setLoading(false);
            return;
          }
        }

        // Create wallet only if it doesn't exist
        if (!walletExists) {
          try {
            console.log('Creating new wallet for user:', user._id);
            const createResponse = await api.post(`/wallet/create/${user._id}`);
            console.log('Wallet created successfully:', createResponse.data);
            toast.success('Wallet created successfully');
          } catch (createError) {
            console.error('Error creating wallet:', createError);
            toast.error('Failed to create wallet. Please try again later.');
            setLoading(false);
            return;
          }
        }

        // Set wallet status and fetch data
        setHasWallet(true);

        try {
          await fetchWalletData();
          await fetchTransactions();
        } catch (fetchError) {
          console.error('Error fetching wallet data:', fetchError);
          toast.error('Failed to load wallet data');
        }

      } catch (error) {
        console.error('Error initializing wallet:', error);
        toast.error('Failed to initialize wallet');
      } finally {
        setLoading(false);
      }
    })();
  }, [user?._id]);

  if (isloading || loading) {
    return (
      <div className="w-full bg-white rounded-lg p-2 sm:p-4 md:p-6 lg:ml-[250px] lg:max-w-[calc(100%-250px)] min-h-[500px] flex items-center justify-center">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#495e26] mb-4" />
          <p className="text-gray-600">Fetching your details...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full bg-white rounded-lg p-2 sm:p-4 md:p-6 lg:ml-[250px] lg:max-w-[calc(100%-250px)] min-h-[500px] flex flex-col h-full">
      <h1 className="text-3xl font-bold text-[#495e26] mb-8">Redeem Coins</h1>
      <WarningBanner/>
      {notification.show && (
        <div
          className={`fixed top-4 right-4 p-4 rounded-lg shadow-lg ${notification.type === 'error' ? 'bg-red-500' : 'bg-green-500'
            } text-white z-50 transition-opacity duration-300`}
        >
          {notification.message}
        </div>
      )}

      {renderSuccessModal()}

      <div className="w-full lg:max-w-xl lg:mx-auto bg-white rounded-lg shadow-md p-4 lg:p-8">
        <div className="mb-6 flex items-center space-x-4 text-2xl font-semibold text-[#495e26]">
          <img src="https://res.cloudinary.com/dyiso4ohk/image/upload/v1744049409/image_1_gqc6nq.png" alt="Coin Icon" className="w-8 h-8" />
          <span>Sweeps Coins: {walletData.swipeCoins || "0"}</span>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="space-y-4">
            <input
              type="number"
              step="1"
              min="1"
              onKeyDown={(e) => {
                // Prevent decimal point and other non-numeric input
                if (e.key === '.' || e.key === 'e' || e.key === '-' || e.key === '+') {
                  e.preventDefault();
                }
              }}
              onInput={(e) => {
                const input = e.target as HTMLInputElement;
                input.value = input.value.replace(/[^0-9]/g, '');
                setAmount(input.value);
              }}
              value={amount}
              placeholder="Enter amount"
              className="w-full px-3 py-2 border border-lime-900 rounded-md focus:outline-none focus:ring-1 focus:ring-lime-500"
              disabled={loading}
            />

            {renderPaymentMethodSelector()}

            {renderCardForm()}

            <button
              type="submit"
              className="w-full bg-[#495e26] text-white py-4 px-6 rounded-lg hover:bg-[#3b4d1f] transition-colors text-lg font-medium mt-4"
              disabled={loading || !paymentMethod || (paymentMethod === 'push-to-card' &&
                (!useExistingCard && (!cardDetails.cardNumber || !cardDetails.expirationMonth || !cardDetails.expirationYear || !cardDetails.cardholderName || !cardDetails.cvv)) ||
                (useExistingCard && !selectedCardId))}
            >
              {loading ? 'Processing redemption...' : 'Redeem Sweeps Coins'}
            </button>
          </div>
        </form>

        {/* Transaction history section commented out */}
      </div>
    </div>
  );
};

export default RedeemSection;
