const mongoose = require("mongoose");
const GameTransactionModel = require("../Model/gameTransactionModel");
const WalletModel = require("../Model/userWalletModal");
const GameRequestModel = require("../Model/gameRequestModel");
const Wallet = require("../Schema/userWalletSchema");
const { User } = require("../Model/usersModel");

class GameTransactionController {
  async handleGamePurchase(req, res) {
    try {
      const { userId, gameId, gameName, amount, gameInfo } = req.body;

      if (!userId || !gameId || !gameName || !amount || !gameInfo) {
        return res.status(400).json({
          error:
            "Missing required fields: userId, gameId, gameName, amount, gameInfo",
        });
      }

      // Convert userId to ObjectId
      let userObjectId;
      try {
        userObjectId = new mongoose.Types.ObjectId(userId);
      } catch (error) {
        return res.status(400).json({
          error: "Invalid userId format",
        });
      }

      // Check wallet balance first
      const wallet = await WalletModel.findOne({ userId: userObjectId });
      if (!wallet || wallet.balance.swipeCoins < amount) {
        return res.status(400).json({
          error: "Insufficient Sweeps Coins balance",
        });
      }

      // Create transaction record first
      const transaction = await GameTransactionModel.createTransaction({
        userId: userObjectId,
        gameId: gameId.toString(),
        gameName,
        transactionType: "purchase",
        amount,
        gameInfo,
        status: "pending",
      });

      try {
        // Deduct from wallet
        await WalletModel.removeSwipeCoins(userObjectId, amount);

        // Keep transaction status as pending (removed the updateTransactionStatus call)
        const updatedTransaction = transaction;

        // Update game balance using the new updateBalance method
        const updatedGame = await GameRequestModel.updateBalance(
          gameId,
          amount,
          "add"
        );

        return res.status(200).json({
          data: {
            ...updatedTransaction.toObject(),
            gameBalance: updatedGame.sweepstakesBalance,
          },
          error: "",
        });
      } catch (error) {
        // If wallet deduction fails, mark transaction as failed
        await GameTransactionModel.updateTransactionStatus(
          transaction._id,
          "failed"
        );
        throw error;
      }
    } catch (error) {
      console.error("Error in game purchase:", error);
      return res.status(500).json({
        error: error.message,
      });
    }
  }

  async handleGameRedeem(req, res) {
    try {
      const { userId, gameId, gameName, amount, gameInfo } = req.body;

      if (!userId || !gameId || !gameName || !amount || !gameInfo) {
        return res.status(400).json({
          error:
            "Missing required fields: userId, gameId, gameName, amount, gameInfo",
        });
      }

      // Convert userId to ObjectId
      let userObjectId;
      try {
        userObjectId = new mongoose.Types.ObjectId(userId);
      } catch (error) {
        return res.status(400).json({
          error: "Invalid userId format",
        });
      }

      // Check game balance first
      const game = await GameRequestModel.findById(gameId);
      if (!game) {
        return res.status(400).json({
          error: "Game not found",
        });
      }

      // Create transaction record first
      const transaction = await GameTransactionModel.createTransaction({
        userId: userObjectId,
        gameId: gameId.toString(),
        gameName,
        transactionType: "redeem",
        amount,
        gameInfo,
        status: "pending",
      });

      try {
        // Update game balance first to ensure sufficient funds
        const updatedGame = await GameRequestModel.updateBalance(
          gameId,
          amount,
          "subtract"
        );

        // No longer adding to wallet balance for redemption
        // await WalletModel.addSwipeCoins(userObjectId, amount);

        // Keep transaction status as pending (removed the updateTransactionStatus call)
        const updatedTransaction = transaction;

        return res.status(200).json({
          data: {
            ...updatedTransaction.toObject(),
            gameBalance: updatedGame.sweepstakesBalance,
          },
          error: "",
        });
      } catch (error) {
        // If any operation fails, mark transaction as failed
        await GameTransactionModel.updateTransactionStatus(
          transaction._id,
          "failed"
        );
        throw error;
      }
    } catch (error) {
      console.error("Error in game redeem:", error);
      return res.status(500).json({
        error: error.message,
      });
    }
  }

  async getTransactions(req, res) {
    try {
      const { userId } = req.params;
      const { page = 1, limit = 10 } = req.query;

      if (!userId) {
        return res.status(400).json({
          error: "UserId is required",
        });
      }

      // Convert userId to ObjectId
      let userObjectId;
      try {
        userObjectId = new mongoose.Types.ObjectId(userId);
      } catch (error) {
        return res.status(400).json({
          error: "Invalid userId format",
        });
      }

      // Calculate skip value for pagination
      const skip = (parseInt(page) - 1) * parseInt(limit);

      // Get total count for pagination
      const totalCount = await GameTransactionModel.countDocuments({ userId: userObjectId });

      // Get transactions with pagination
      const transactions = await GameTransactionModel.getTransactionsByUserId(
        userObjectId,
        parseInt(limit),
        skip
      );

      return res.status(200).json({
        data: transactions,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(totalCount / parseInt(limit)),
          totalItems: totalCount,
          hasNextPage: skip + transactions.length < totalCount,
          hasPrevPage: parseInt(page) > 1
        },
        error: "",
      });
    } catch (error) {
      console.error("Error fetching transactions:", error);
      return res.status(500).json({
        error: error.message,
      });
    }
  }

  async getTransactionHistory(req, res) {
    try {
      const { userId, type, page = 1, limit = 10 } = req.query;

      if (!userId) {
        return res.status(400).json({
          error: "UserId is required",
        });
      }

      // Convert userId to ObjectId
      let userObjectId;
      try {
        userObjectId = new mongoose.Types.ObjectId(userId);
      } catch (error) {
        return res.status(400).json({
          error: "Invalid userId format",
        });
      }

      // Build query based on transaction type
      const query = { userId: userObjectId };
      if (type && ["purchase", "redeem"].includes(type)) {
        query.transactionType = type;
      }

      // Calculate skip value for pagination
      const skip = (parseInt(page) - 1) * parseInt(limit);

      // Get total count for pagination
      const totalCount = await GameTransactionModel.countDocuments(query);

      // Get transactions with pagination
      const transactions = await GameTransactionModel.find(query)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(parseInt(limit))
        .populate({
          path: "gameId",
          select: "gameName playLink description gameUserId",
        });

      return res.status(200).json({
        data: {
          transactions,
          pagination: {
            currentPage: parseInt(page),
            totalPages: Math.ceil(totalCount / parseInt(limit)),
            totalItems: totalCount,
            hasMore: skip + transactions.length < totalCount,
          },
        },
        error: "",
      });
    } catch (error) {
      console.error("Error fetching transaction history:", error);
      return res.status(500).json({
        error: error.message,
      });
    }
  }

  async getTransactionStats(req, res) {
    try {
      const { userId } = req.params;

      if (!userId) {
        return res.status(400).json({
          error: "UserId is required",
        });
      }

      // Convert userId to ObjectId
      let userObjectId;
      try {
        userObjectId = new mongoose.Types.ObjectId(userId);
      } catch (error) {
        return res.status(400).json({
          error: "Invalid userId format",
        });
      }

      // Get stats for both purchase and redeem
      const [purchaseStats, redeemStats] = await Promise.all([
        GameTransactionModel.aggregate([
          {
            $match: {
              userId: userObjectId,
              transactionType: "purchase",
              status: "completed",
            },
          },
          {
            $group: {
              _id: null,
              totalAmount: { $sum: "$amount" },
              count: { $sum: 1 },
              lastTransaction: { $max: "$createdAt" },
            },
          },
        ]),
        GameTransactionModel.aggregate([
          {
            $match: {
              userId: userObjectId,
              transactionType: "redeem",
              status: "completed",
            },
          },
          {
            $group: {
              _id: null,
              totalAmount: { $sum: "$amount" },
              count: { $sum: 1 },
              lastTransaction: { $max: "$createdAt" },
            },
          },
        ]),
      ]);

      return res.status(200).json({
        data: {
          purchase: purchaseStats[0] || { totalAmount: 0, count: 0 },
          redeem: redeemStats[0] || { totalAmount: 0, count: 0 },
        },
        error: "",
      });
    } catch (error) {
      console.error("Error fetching transaction stats:", error);
      return res.status(500).json({
        error: error.message,
      });
    }
  }

  async getAllTransactions(req, res) {
    try {
      console.log('Received query parameters:', req.query);
      // Extract all possible query parameters and trim string values
      let {
        page = 1,
        limit = 10,
        status,
        transactionType,
        date,
        startDate,
        endDate,
        userName,
        userSearch, // Add userSearch parameter
        userEmail,
        gameName,
        searchTerm,
        // Add new filter parameters from frontend
        username,
        email,
        game,
        search
      } = req.query;

      // Map new filter parameters to existing ones for backward compatibility
      if (username && !userName) userName = username;
      if (userSearch && !userName) userName = userSearch; // Map userSearch to userName for processing
      if (email && !userEmail) userEmail = email;
      if (game && !gameName) gameName = game;
      if (search && !searchTerm) searchTerm = search;

      // Trim string inputs to handle copy-pasted values with extra spaces
      if (userName) userName = userName.trim();
      if (userEmail) userEmail = userEmail.trim().toLowerCase(); // Convert email to lowercase for case-insensitive search
      if (gameName) gameName = gameName.trim();
      if (searchTerm) searchTerm = searchTerm.trim();
      if (status) status = status.trim();
      if (transactionType) transactionType = transactionType.trim();

      // Build query based on status and transaction type if provided
      const query = {};
      if (
        status &&
        ["pending", "completed", "failed", "approved", "rejected"].includes(
          status
        )
      ) {
        query.status = status;
        console.log('Added status filter:', status);
      }

      // Add transaction type filter if provided
      if (transactionType && ["purchase", "redeem"].includes(transactionType)) {
        query.transactionType = transactionType;
        console.log('Added transaction type filter:', transactionType);
      }

      // Handle date range filtering using MongoDB's native Date handling
      if (startDate || endDate || date) {
        console.log('Original date inputs:', { startDate, endDate, date });

        // Create a dedicated createdAt filter object
        const createdAtFilter = {};

        if (startDate || endDate) {
          if (startDate) {
            // Format: YYYY-MM-DD
            const formattedStartDate = startDate.trim();
            console.log('Start date for filtering:', formattedStartDate);

            // Create a start date condition - beginning of day in local timezone
            // This ensures we're using the correct day boundary for the user's timezone
            const startDateObj = new Date(formattedStartDate);
            // Set time to start of day (00:00:00.000) in local time
            startDateObj.setHours(0, 0, 0, 0);
            createdAtFilter.$gte = startDateObj;

            console.log('Added start date filter:', startDateObj.toISOString());
          }

          if (endDate) {
            // Format: YYYY-MM-DD
            const formattedEndDate = endDate.trim();
            console.log('End date for filtering:', formattedEndDate);

            // Create an end date condition - end of day in local timezone
            // This ensures we're using the correct day boundary for the user's timezone
            const endDateObj = new Date(formattedEndDate);
            // Set time to end of day (23:59:59.999) in local time
            endDateObj.setHours(23, 59, 59, 999);
            createdAtFilter.$lte = endDateObj;

            console.log('Added end date filter:', endDateObj.toISOString());
          }
        }
        // Handle single date filter if provided (for backward compatibility)
        else if (date) {
          const formattedDate = date.trim();
          console.log('Single date for filtering:', formattedDate);

          // For a single date, we want all records from that day
          const startOfDay = new Date(`${formattedDate}T00:00:00.000Z`);
          const endOfDay = new Date(`${formattedDate}T23:59:59.999Z`);

          createdAtFilter.$gte = startOfDay;
          createdAtFilter.$lte = endOfDay;

          console.log('Single date filter applied:', startOfDay.toISOString(), 'to', endOfDay.toISOString());
        }

        // Add the createdAt filter to the main query
        query.createdAt = createdAtFilter;

        console.log('Final date filter query:', JSON.stringify(query.createdAt, null, 2));
      }

      // Add game name filter if provided
      if (gameName) {
        query.gameName = { $regex: gameName, $options: 'i' };
        console.log('Added game name filter:', gameName);
      }

      // Handle search term if provided
      if (searchTerm) {
        // Check if searchTerm is a valid ObjectId
        const isValidObjectId = /^[0-9a-fA-F]{24}$/.test(searchTerm);

        // Create an OR query for searching by ID or amount
        const searchQuery = [];

        if (isValidObjectId) {
          searchQuery.push({ _id: searchTerm });
        }

        // Check if searchTerm is a number for amount search
        const amount = parseFloat(searchTerm);
        if (!isNaN(amount)) {
          searchQuery.push({ amount });
        }

        if (searchQuery.length > 0) {
          query.$or = searchQuery;
          console.log('Added search term filter:', searchTerm);
        }
      }

      // For user name and email filters, we need to find the user IDs first
      if (userName || userEmail) {
        console.log('Processing user filters');

        try {
          // Use aggregation to efficiently find matching users
          const aggregationPipeline = [];

          // Build the match stage based on the filters
          const matchStage = { $match: {} };

          if (userName) {
            // Enhanced user search: match on username, firstName, lastName, or full name combination
            const searchConditions = [
              { username: { $regex: userName, $options: 'i' } },
              { firstName: { $regex: userName, $options: 'i' } },
              { lastName: { $regex: userName, $options: 'i' } }
            ];

            // If the search term contains a space, also search for "firstName lastName" combination
            if (userName.includes(' ')) {
              const nameParts = userName.trim().split(/\s+/);
              if (nameParts.length >= 2) {
                const firstName = nameParts[0];
                const lastName = nameParts.slice(1).join(' '); // Handle multiple last names
                searchConditions.push({
                  $and: [
                    { firstName: { $regex: firstName, $options: 'i' } },
                    { lastName: { $regex: lastName, $options: 'i' } }
                  ]
                });
              }
            }

            matchStage.$match.$or = searchConditions;
            console.log('Added enhanced user search filter:', userName);
          }

          if (userEmail) {
            // Email is already converted to lowercase
            if (matchStage.$match.$or) {
              // If we already have an $or for userName, we need to use $and to combine with email
              matchStage.$match.$and = [
                { $or: matchStage.$match.$or },
                { email: { $regex: userEmail, $options: 'i' } }
              ];
              // Remove the original $or since it's now in the $and
              delete matchStage.$match.$or;
            } else {
              // Simple email match if no userName filter
              matchStage.$match.email = { $regex: userEmail, $options: 'i' };
            }
            console.log('Added user email filter (lowercase):', userEmail);
          }

          aggregationPipeline.push(matchStage);

          // Project only the _id field to minimize data transfer
          aggregationPipeline.push({ $project: { _id: 1 } });

          console.log('User aggregation pipeline:', JSON.stringify(aggregationPipeline, null, 2));
          const users = await User.aggregate(aggregationPipeline);
          console.log('Found users:', users.length);

          if (users.length > 0) {
            const userIds = users.map(user => user._id);
            query.userId = { $in: userIds };
            console.log('Added user IDs filter with', userIds.length, 'users');
          } else {
            console.log('No users match the filter criteria, returning empty result');
            // No users match the filter criteria, return empty result
            return res.status(200).json({
              data: [],
              pagination: {
                currentPage: parseInt(page),
                totalPages: 0,
                totalItems: 0,
                hasMore: false,
              },
              error: "",
            });
          }
        } catch (error) {
          console.error('Error processing user filters:', error);
          // Continue with other filters even if user filter fails
        }
      }

      // Calculate skip value for pagination
      const skip = (parseInt(page) - 1) * parseInt(limit);
      console.log('Pagination:', { page, limit, skip });

      // Log the final MongoDB query
      console.log('Final MongoDB query:', JSON.stringify(query, null, 2));

      // Get total count for pagination
      const totalCount = await GameTransactionModel.model.countDocuments(query);
      console.log('Total matching documents:', totalCount);

      // Fetch transactions with populated user details
      const transactions = await GameTransactionModel.model
        .find(query)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(parseInt(limit))
        .populate({
          path: "userId",
          select: "firstName lastName email username", // Add username to the selected fields
          model: User
        });

      console.log('Retrieved transactions:', transactions.length);

      // Transform the response to include user details in a more frontend-friendly format
      const transformedTransactions = transactions.map(transaction => {
        const transObj = transaction.toObject();
        if (transObj.userId) {
          // Create a user object with all required fields for the frontend
          transObj.user = {
            username: transObj.userId.username || `${transObj.userId.firstName || ''} ${transObj.userId.lastName || ''}`.trim() || 'User',
            email: transObj.userId.email || 'No email provided',
            firstName: transObj.userId.firstName || '',
            lastName: transObj.userId.lastName || ''
          };
        } else {
          // Provide default user data if userId is not populated
          transObj.user = {
            username: 'Unknown User',
            email: 'No email available',
            firstName: '',
            lastName: ''
          };
        }
        return transObj;
      });

      const response = {
        data: transformedTransactions,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(totalCount / parseInt(limit)),
          totalItems: totalCount,
          hasMore: skip + transactions.length < totalCount,
        },
        error: "",
      };

      console.log('Sending response with pagination:', response.pagination);
      return res.status(200).json(response);
    } catch (error) {
      console.error("Error fetching all transactions:", error);
      return res.status(500).json({
        error: error.message,
      });
    }
  }

  async updateTransactionStatus(req, res) {
    try {
      const { id } = req.params;
      let { status, type } = req.body;
      console.log(type);
      if (!id) {
        return res.status(400).json({
          error: "Transaction ID is required",
        });
      }
      if (status) {
        // Convert first letter to lowercase for consistency with our schema
        status = status.charAt(0).toLowerCase() + status.slice(1);
      }
      const validStatuses = [
        "pending",
        "completed",
        "failed",
        "approved",
        "rejected",
      ];

      if (!status || !validStatuses.includes(status)) {
        return res.status(400).json({
          error:
            "Valid status is required (pending, completed, failed, approved, rejected)",
        });
      }
      const transaction = await GameTransactionModel.model.findById(id);

      if (!transaction) {
        return res.status(404).json({
          error: "Transaction not found",
        });
      }

      // Store the original transaction state for activity logging
      const originalTransaction = { ...transaction.toObject() };

      // If status is rejected, handle based on transaction type
      if (status === "rejected") {
        // For redeem transactions, just update the status without wallet operations
        if (transaction.transactionType === "redeem") {
          const updatedTransaction =
            await GameTransactionModel.model.findByIdAndUpdate(id, { status }, { new: true });

          // Log the rejection activity if user info is available
          if (req.user) {
            const { logActivity } = require('../utils/logger');
            await logActivity({
              user: req.user,
              action: `${transaction.transactionType} request rejected`,
              entityType: `transaction_${transaction.transactionType}`,
              entityId: id,
              previousState: {
                status: originalTransaction.status
              },
              newState: {
                status: status
              },
              notes: `${transaction.transactionType} request of ${transaction.amount} Sweeps Coins rejected by staff`,
              ipAddress: req.ip
            });
          }

          return res.status(200).json({
            data: updatedTransaction,
            error: "",
          });
        }
        // For purchase transactions, revert the amount back to the user's wallet
        else if (transaction.transactionType === "purchase") {
          try {
            // First, find the user's wallet
            const wallet = await Wallet.findOne({
              userId: transaction.userId,
            });

            if (!wallet) {
              return res.status(404).json({
                error: "Wallet not found for this user",
              });
            }

            console.log(
              "Before refund - swipeCoins:",
              wallet.balance.swipeCoins
            );
            console.log("Refund amount:", transaction.amount);

            // Add the coins back to the user's wallet
            await WalletModel.addSwipeCoins(transaction.userId, transaction.amount);

            // Update the transaction status
            const updatedTransaction =
              await GameTransactionModel.model.findByIdAndUpdate(id, { status }, { new: true });

            // Get the updated wallet for logging
            const updatedWallet = await Wallet.findOne({
              userId: transaction.userId,
            });

            console.log(
              "After refund - swipeCoins:",
              updatedWallet.balance.swipeCoins
            );

            // Log the transaction in the wallet
            await WalletModel.addTransaction(
              transaction.userId,
              "credit",
              "swipeCoin",
              transaction.amount,
              `Refund for rejected purchase transaction #${id}`,
              "Refund",
              "Processed"
            );

            // Log the rejection activity if user info is available
            if (req.user) {
              const { logActivity } = require('../utils/logger');
              await logActivity({
                user: req.user,
                action: `${transaction.transactionType} request rejected with refund`,
                entityType: `transaction_${transaction.transactionType}`,
                entityId: id,
                previousState: {
                  status: originalTransaction.status
                },
                newState: {
                  status: status
                },
                notes: `${transaction.transactionType} request of ${transaction.amount} Sweeps Coins rejected by staff. Amount refunded to user wallet.`,
                ipAddress: req.ip
              });
            }

            return res.status(200).json({
              data: updatedTransaction,
              error: "",
            });
          } catch (error) {
            console.error("Error refunding purchase amount:", error);
            return res.status(500).json({
              error: "Failed to refund purchase amount: " + error.message,
            });
          }
        }
        // For any other transaction types
        else {
          const updatedTransaction =
            await GameTransactionModel.model.findByIdAndUpdate(id, { status }, { new: true });

          // Log the rejection activity
          if (req.user) {
            const { logActivity } = require('../utils/logger');
            await logActivity({
              user: req.user,
              action: `${transaction.transactionType} request rejected`,
              entityType: `transaction_${transaction.transactionType}`,
              entityId: id,
              previousState: {
                status: originalTransaction.status
              },
              newState: {
                status: status
              },
              notes: `${transaction.transactionType} request of ${transaction.amount} Sweeps Coins rejected by staff`,
              ipAddress: req.ip
            });
          }

          return res.status(200).json({
            data: updatedTransaction,
            error: "",
          });
        }
      }
      // Handle redeem transactions differently
      else if (transaction.transactionType === "redeem" && ["completed", "approved"].includes(status)) {
        // First update the wallet balance
        const wallet = await Wallet.findOne({
          userId: transaction.userId,
        });

        if (!wallet) {
          return res.status(404).json({
            error: "Wallet not found for this user",
          });
        }

        console.log(
          "Before update - redeemableSwipeCoins:",
          wallet.balance.swipeCoins
        );
        console.log("Transaction amount:", transaction.amount);
        await Wallet.findByIdAndUpdate(
          wallet._id,
          {
            $set: {
              "balance.swipeCoins":
                wallet.balance.swipeCoins + transaction.amount,
            },
          },
          { new: true }
        );

        const updatedWallet = await Wallet.findById(wallet._id);
        console.log(
          "After update - redeemableSwipeCoins:",
          updatedWallet.balance.swipeCoins
        );

        // Now update the transaction
        const updatedTransaction =
          await GameTransactionModel.model.findByIdAndUpdate(id, { status }, { new: true });
        if (req.user && ["completed", "approved"].includes(status)) {
          const { logActivity } = require('../utils/logger');
          const log = await logActivity({
            user: req.user,
            action: 'Redeem request approved',
            entityType: 'transaction_redeem',
            entityId: id,
            previousState: {
              status: originalTransaction.status
            },
            newState: {
              status: status
            },
            notes: `Redeem request of ${transaction.amount} Sweeps Coins approved by staff`,
            ipAddress: req.ip
          });
          console.log("log", log);
        }
        return res.status(200).json({
          data: updatedTransaction,
          error: "",
        });
      } else {
        // For purchase or other types, just update the transaction
        const updatedTransaction =
          await GameTransactionModel.model.findByIdAndUpdate(id, { status }, { new: true });

        // Log the purchase request approval activity
        if (req.user && transaction.transactionType === "purchase" && ["completed", "approved"].includes(status)) {
          const { logActivity } = require('../utils/logger');
          await logActivity({
            user: req.user,
            action: 'Purchase request approved',
            entityType: 'transaction_purchase',
            entityId: id,
            previousState: {
              status: originalTransaction.status
            },
            newState: {
              status: status
            },
            notes: `Purchase request of ${transaction.amount} Sweeps Coins approved by staff`,
            ipAddress: req.ip
          });
        }

        return res.status(200).json({
          data: updatedTransaction,
          error: "",
        });
      }
    } catch (error) {
      console.error("Error updating transaction status:", error);
      return res.status(500).json({
        error: error.message,
      });
    }
  }
}

module.exports = new GameTransactionController();
