const express = require("express");
const mongoose = require("mongoose");
const routes = require("./routes");
const cors = require("cors");
var TransactionsRouter = require("./Router/transactionsRouter");
var TokenRouter = require("./Router/tokenRouter");
var UsersRouter = require("./Router/usersRouter");
const CustomersRouter = require("./Router/customersRouter");
const analyticsRouter = require("./Router/analyticsRouter");
const reportsRouter = require("./Router/reportsRouter");
const cashappRouter = require("./Router/cashAppRouter");
const productRouter = require("./Router/productRouter");
const orderRouter = require("./Router/orderRouter");
const paymentRouter = require("./Router/paymentRouter");
const gameRequestRouter = require("./Router/gameRequestRouter");
const redeemRequestRouter = require("./Router/redeemRequestRouter");
const transactionStatusRouter = require("./Router/transactionStatusRouter");
const redeemRequestLogsRouter = require("./Router/redeemRequestLogsRouter");
const walletRoutes = require("./Router/walletRouter");
const virtualCardRoutes = require("./Router/virtualCardRouter");
const gameTransactionRouter = require("./Router/gameTransactionRouter");
const postalCodeRouter = require("./Router/postalCodeRouter");
const paymentRouters = require("./Routes/zen/paymentRoutes");
const activityLogRouter = require("./Router/activityLogRouter");
const cashoutRequestRouter = require("./Router/cashoutRequestRouter");
require("dotenv").config();

const app = express();

// Apply CORS middleware first
app.use(cors({
    origin: '*', // Allow all origins temporarily for testing
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
    allowedHeaders: [
        'Content-Type',
        'Authorization',
        'Accept',
        'Origin',
        'Referer',
        'User-Agent',
        'Access-Control-Allow-Origin',
        'Access-Control-Allow-Headers',
        'Access-Control-Allow-Methods',
        'Access-Control-Allow-Credentials',
        'X-Requested-With',
        'sec-ch-ua',
        'sec-ch-ua-mobile',
        'sec-ch-ua-platform'
    ],
    credentials: true,
    exposedHeaders: ['set-cookie'],
    preflightContinue: false,
    optionsSuccessStatus: 204
}));

// Middleware
app.use(express.json());

// Debug logging middleware
app.use((req, res, next) => {
    console.log('Incoming request:', {
        method: req.method,
        path: req.path,
        headers: req.headers,
        body: req.body
    });
    next();
});

// Health check endpoint
app.get('/', (req, res) => {
    res.status(200).json({ status: 'ok', message: 'Server is running' });
});

// Debug logging
console.log('Environment:', process.env.NODE_ENV);
console.log('Port:', process.env.PORT);
console.log('MongoDB URL:', process.env.DATABASE_URL ? 'Set' : 'Not Set');

// Routes
app.use("/api", routes);
app.use("/", UsersRouter);
app.use("/", paymentRouter);
app.use("/", productRouter);
app.use("/", orderRouter);
app.use("/", gameRequestRouter);
app.use("/", redeemRequestRouter);
app.use("/", transactionStatusRouter);
app.use("/", redeemRequestLogsRouter);
app.use("/", walletRoutes);
app.use("/", virtualCardRoutes);
app.use("/api", gameTransactionRouter);
app.use("/", postalCodeRouter);
app.use("/", paymentRouters);
app.use("/logs", activityLogRouter);
app.use("/reports", reportsRouter);
app.use("/uploads", express.static("uploads"));
app.use("/", cashoutRequestRouter);

// MongoDB Connection with retry logic
const connectWithRetry = () => {
    mongoose.connect(process.env.DATABASE_URL, {
        useNewUrlParser: true,
        useUnifiedTopology: true,
        serverSelectionTimeoutMS: 5000,
    })
    .then(() => {
        console.log('Connected to MongoDB');
    })
    .catch((err) => {
        console.error('MongoDB connection error:', err);
        console.log('Retrying in 5 seconds...');
        setTimeout(connectWithRetry, 5000);
    });
};

connectWithRetry();

// Error handling middleware
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({
        status: 'error',
        message: 'Something went wrong!',
        error: process.env.NODE_ENV === 'development' ? err.message : undefined
    });
});

// Start server
const PORT = process.env.PORT || 8000;
app.listen(PORT, '0.0.0.0', () => {
    console.log(`Server is running on port ${PORT}`);
});

app.get("/", async (req, res) => {
  res.send("Welcome to Gmail API with NodeJS");
});
