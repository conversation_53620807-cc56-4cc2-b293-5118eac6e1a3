
import React, { useState, useRef, useEffect } from 'react';
import { isFeatureEnabled } from '../../../utils/featureFlags';
import FeatureDisabled from '../../../components/FeatureDisabled';
import GameTransactionsSection from './components/GameTransactionsSection';
import OrderAnalyticsSection from './components/OrderAnalyticsSection';
import TransactionDataTable from './components/TransactionDataTable';
import { useReportsData } from './hooks/useReportsData';
import 'react-date-range/dist/styles.css';
import 'react-date-range/dist/theme/default.css';

// Custom hook for all reports data and logic
const Reports: React.FC = () => {
  const {
    gameFilters,
    gameTransactions,
    gameTransactionsPagination,
    systemTotals,
    reportData,
    loading,
    gameTransactionsLoading,

    // Actions
    handleGameFilterChange,
    handlePageChange,
  } = useReportsData();

  // Local state for UI components
  const [showDatePicker, setShowDatePicker] = useState<boolean>(false);

  // Use React refs for better performance and reliability
  const datePickerRef = useRef<HTMLDivElement>(null);
  const datePickerButtonRef = useRef<HTMLButtonElement>(null);

  // Handle clicks outside the date picker to close it
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        datePickerRef.current &&
        datePickerButtonRef.current &&
        !datePickerRef.current.contains(event.target as Node) &&
        !datePickerButtonRef.current.contains(event.target as Node)
      ) {
        setShowDatePicker(false);
      }
    };

    // Only add the event listener when the date picker is open
    if (showDatePicker) {
      // Use capture phase to ensure we handle the event before it reaches other handlers
      document.addEventListener('mousedown', handleClickOutside, true);
    }

    // Clean up the event listener when the component unmounts or the date picker closes
    return () => {
      document.removeEventListener('mousedown', handleClickOutside, true);
    };
  }, [showDatePicker]);

  // Calculate totals for game transactions section
  const totalTransactions = gameTransactions.length;
  const totalPurchaseAmount = gameTransactions
    .filter(t => t.transactionType === 'purchase')
    .reduce((sum, t) => sum + t.amount, 0);
  const totalRedeemAmount = gameTransactions
    .filter(t => t.transactionType === 'redeem')
    .reduce((sum, t) => sum + t.amount, 0);

  // Check if reports feature is enabled
  if (!isFeatureEnabled('REPORTS_ENABLED')) {
    return <FeatureDisabled featureName="Reports Dashboard" />;
  }

  return (
    <div className="w-full bg-gray-50 rounded-lg p-4 sm:p-5 md:p-6 lg:ml-[250px] lg:max-w-[calc(100%-250px)] min-h-[500px] flex flex-col">
      {/* Order Analytics Section */}
      <OrderAnalyticsSection
        systemTotals={systemTotals}
        reportData={reportData}
        loading={loading}
      />
      {/* Game Transactions Section */}
      <GameTransactionsSection
        gameFilters={gameFilters}
        onFilterChange={handleGameFilterChange}
        totalTransactions={totalTransactions}
        totalPurchaseAmount={totalPurchaseAmount}
        totalRedeemAmount={totalRedeemAmount}
      />

      {/* Transaction Data Table */}
      <TransactionDataTable
        transactions={gameTransactions}
        loading={gameTransactionsLoading}
        pagination={gameTransactionsPagination}
        onPageChange={handlePageChange}
      />
    </div>
  );
};

export default Reports;
