/**
 * Reports Dashboard Component
 *
 * TODO: Refactor to use RTK Query instead of direct API calls
 * This component should be updated to use RTK Query for data fetching.
 * The current implementation uses direct axios calls which should be replaced
 * with RTK Query hooks for better caching, loading states, and error handling.
 *
 * See the commented example in the component for how this would be structured.
 */

import React, { useState, useEffect, Component, type ReactNode, type ErrorInfo } from 'react';
// import { isFeatureEnabled } from '../../../utils/featureFlags';
import FeatureDisabled from '../../../components/FeatureDisabled';
import axios from 'axios';
import { useSelector } from 'react-redux';
import { RootState } from '../../../redux/store';
import { DateRangePicker } from 'react-date-range';
import 'react-date-range/dist/styles.css';
import 'react-date-range/dist/theme/default.css';


// Error Boundary component to catch errors in the DateRangePicker
interface ErrorBoundaryProps {
  children: ReactNode;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    console.error('Error caught by boundary:', error, errorInfo);
  }

  render(): ReactNode {
    if (this.state.hasError) {
      return (
        <div className="text-red-500">
          <h2>Something went wrong in the reports component.</h2>
          <details className="whitespace-pre-wrap">
            {this.state.error?.toString()}
          </details>
        </div>
      );
    }

    return this.props.children;
  }
}

// Custom CSS for responsive date picker and enhanced styling
const datePickerStyles = `
  /* Extra small screens utility class */
  @media (min-width: 480px) {
    .xs\\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  /* Enhanced animations */
  .dashboard-card {
    transition: all 0.3s ease;
  }

  .dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  }

  /* Improved date picker styling */
  .date-picker-container {
    position: relative;
  }

  .date-picker-container .rdrCalendarWrapper {
    font-size: 12px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  }

  .date-picker-container .rdrMonth {
    width: 100%;
    padding: 0.5rem;
  }

  .date-picker-container .rdrDay {
    height: 2.5em;
    width: 2.5em;
  }

  .date-picker-container .rdrMonthAndYearWrapper {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    height: auto;
  }

  .date-picker-container .rdrMonthAndYearPickers select {
    padding: 0.25rem;
  }

  /* Fix for date picker positioning */
  .date-picker-container {
    max-height: 80vh;
    overflow-y: auto;
  }

  .date-picker-container .rdrDateDisplayItem {
    border-radius: 8px;
    background-color: rgba(73, 94, 38, 0.1);
    border-color: rgba(73, 94, 38, 0.2);
  }

  .date-picker-container .rdrDateDisplayItemActive {
    border-color: #495e26;
  }

  .date-picker-container .rdrDayToday .rdrDayNumber span:after {
    background: #495e26;
  }

  .date-picker-container .rdrDayDisabled {
    background-color: #f8f8f8;
  }

  .date-picker-container .rdrMonthAndYearWrapper {
    padding-top: 10px;
  }

  .date-picker-container .rdrDay:not(.rdrDayPassive) .rdrInRange,
  .date-picker-container .rdrDay:not(.rdrDayPassive) .rdrStartEdge,
  .date-picker-container .rdrDay:not(.rdrDayPassive) .rdrEndEdge,
  .date-picker-container .rdrDay:not(.rdrDayPassive) .rdrSelected {
    background-color: #495e26;
  }

  @media (max-width: 640px) {
    .date-picker-container .rdrCalendarWrapper {
      font-size: 12px;
    }
    .date-picker-container .rdrMonth {
      width: 100%;
      min-width: 250px;
    }
    .date-picker-container .rdrDefinedRangesWrapper {
      display: none;
    }
  }

  /* Table styling improvements */
  .reports-table th {
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .reports-table tr {
    transition: background-color 0.2s ease;
  }

  /* Card styling */
  .stat-card {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
  }

  .stat-card:hover {
    transform: translateY(-5px);
  }

  .stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, #495e26, #7a9e42);
  }

  /* Filter button styling */
  .filter-btn {
    transition: all 0.2s ease;
  }

  .filter-btn:hover {
    transform: translateY(-2px);
  }
`;

// Updated interfaces to match the new backend API structure
interface DashboardData {
  summary: {
    totalPurchases: number;
    totalPurchaseAmount: number;
    totalRedeems: number;
    totalRedeemAmount: number;
    totalSweepsBalance: {
      totalSwipeCoins: number;
      totalUnplayedSwipeCoins: number;
      totalRedeemableSwipeCoins: number;
      grandTotal: number;
    };
  };
  purchases: {
    total: number;
    totalAmount: number;
    items: PurchaseItem[];
    groupBy: string;
  };
  redeems: {
    total: number;
    totalAmount: number;
    items: RedeemItem[];
    groupBy: string;
  };
  dateRange: {
    startDate: string | null;
    endDate: string | null;
  };
}

interface PurchaseItem {
  _id: string;
  count: number;
  totalAmount: number;
  paymentMethods?: string[];
  latestPurchase?: string;
}

interface RedeemItem {
  _id: string;
  count: number;
  totalAmount: number;
  games?: string[];
  latestRedeem?: string;
}

interface WalletBalances {
  totalSweepsBalance: {
    totalSwipeCoins: number;
    totalUnplayedSwipeCoins: number;
    totalRedeemableSwipeCoins: number;
    grandTotal: number;
  };
  detailedBalances: {
    totalWallets: number;
    totalGoldCoins: number;
    totalSwipeCoins: number;
    totalUnplayedSwipeCoins: number;
    totalRedeemableSwipeCoins: number;
    avgGoldCoins: number;
    avgSwipeCoins: number;
  };
}

interface DateRange {
  startDate: Date;
  endDate: Date;
  key: string;
}

interface GameTransaction {
  _id: string;
  userId: string;
  gameId: string;
  gameName: string;
  transactionType: 'purchase' | 'redeem';
  amount: number;
  status: 'completed' | 'pending' | 'failed';
  gameInfo?: any;
  createdAt: string;
  __v?: number;
}

interface GameTransactionFilters {
  searchTerm: string;
  dateFilter: {
    startDate: Date | null;
    endDate: Date | null;
  };
  gameFilter: string;
  userFilter: string;
  transactionTypeFilter: string;
  statusFilter: string;
  amountFilter: {
    min: string;
    max: string;
  };
}

interface DashboardCardProps {
  label: string;
  value: string | number;
  icon: React.ReactNode;
  bgColor: string;
}

const DashboardCard: React.FC<DashboardCardProps> = ({ label, value, icon, bgColor }) => (
  <div className="dashboard-card bg-white rounded-lg p-4 border-l-4 border-[#495e26] shadow-sm hover:shadow transition-all duration-300">
    <div className="flex items-center justify-between">
      <div className="flex flex-col">
        <div className="text-xs text-gray-500 mb-1">{label}</div>
        <span className="text-lg font-bold">{value}</span>
      </div>
      <div className={`p-2 rounded-md ${bgColor}`}>{icon}</div>
    </div>
  </div>
);

const Reports: React.FC = () => {
  // Using Redux state for authentication
  useSelector((state: RootState) => state.auth);
  const [loading, setLoading] = useState<boolean>(false);
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [walletBalances, setWalletBalances] = useState<WalletBalances | null>(null);
  const [purchasesData, setPurchasesData] = useState<{ total: number; totalAmount: number; items: PurchaseItem[] }>({
    total: 0,
    totalAmount: 0,
    items: []
  });
  const [redeemsData, setRedeemsData] = useState<{ total: number; totalAmount: number; items: RedeemItem[] }>({
    total: 0,
    totalAmount: 0,
    items: []
  });
  const [activeTab, setActiveTab] = useState<string>('date'); // 'date', 'game', 'user'
  const [reportType, setReportType] = useState<string>('dashboard'); // 'dashboard', 'purchases', 'redeems', 'wallets'
  const [dateRange, setDateRange] = useState<DateRange[]>([
    {
      startDate: new Date(new Date().setDate(new Date().getDate() - 7)),
      endDate: new Date(),
      key: 'selection',
    },
  ]);
  const [showDatePicker, setShowDatePicker] = useState<boolean>(false);

  // Game Transactions Section State
  const [gameTransactions, setGameTransactions] = useState<GameTransaction[]>([]);
  const [gameTransactionsLoading, setGameTransactionsLoading] = useState<boolean>(false);
  const [showGameFilters, setShowGameFilters] = useState<boolean>(false);
  const [gameFilters, setGameFilters] = useState<GameTransactionFilters>({
    searchTerm: '',
    dateFilter: {
      startDate: null,
      endDate: null
    },
    gameFilter: '',
    userFilter: '',
    transactionTypeFilter: '',
    statusFilter: '',
    amountFilter: {
      min: '',
      max: ''
    }
  });
  const [gameTransactionsPagination, setGameTransactionsPagination] = useState({
    currentPage: 1,
    totalItems: 0,
    hasMore: false
  });

  // Use React refs for better performance and reliability
  const datePickerRef = React.useRef<HTMLDivElement>(null);
  const datePickerButtonRef = React.useRef<HTMLButtonElement>(null);

  // Handle clicks outside the date picker to close it
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        datePickerRef.current &&
        datePickerButtonRef.current &&
        !datePickerRef.current.contains(event.target as Node) &&
        !datePickerButtonRef.current.contains(event.target as Node)
      ) {
        setShowDatePicker(false);
      }
    };

    // Only add the event listener when the date picker is open
    if (showDatePicker) {
      // Use capture phase to ensure we handle the event before it reaches other handlers
      document.addEventListener('mousedown', handleClickOutside, true);
    }

    // Clean up the event listener when the component unmounts or the date picker closes
    return () => {
      document.removeEventListener('mousedown', handleClickOutside, true);
    };
  }, [showDatePicker]);

  useEffect(() => {
    // Fetch data when component mounts or filters change
    if (reportType === 'dashboard') {
      fetchDashboardData();
    } else if (reportType === 'purchases') {
      fetchPurchasesData();
    } else if (reportType === 'redeems') {
      fetchRedeemsData();
    }

    // Always fetch wallet balances for the dashboard cards
    fetchWalletBalances();

    // Always fetch game transactions for the top section
    fetchGameTransactions(1);
  }, [activeTab, dateRange, reportType]);

  // Fetch game transactions when filters change
  useEffect(() => {
    fetchGameTransactions(1);
  }, [gameFilters]);

  // Fetch game transactions with filters
  const fetchGameTransactions = async (page: number = 1) => {
    try {
      setGameTransactionsLoading(true);
      const token = localStorage.getItem('Token') || '';

      // Build the URL with query parameters
      const params = new URLSearchParams();

      // Add pagination
      params.append('page', page.toString());
      params.append('limit', '25');

      // Add filters
      if (gameFilters.searchTerm.trim()) {
        params.append('search', gameFilters.searchTerm.trim());
      }

      if (gameFilters.gameFilter.trim()) {
        params.append('gameName', gameFilters.gameFilter.trim());
      }

      if (gameFilters.userFilter.trim()) {
        params.append('userSearch', gameFilters.userFilter.trim());
      }

      if (gameFilters.transactionTypeFilter) {
        params.append('transactionType', gameFilters.transactionTypeFilter);
      }

      if (gameFilters.statusFilter) {
        params.append('status', gameFilters.statusFilter);
      }

      // Add date range filters
      if (gameFilters.dateFilter.startDate) {
        params.append('startDate', gameFilters.dateFilter.startDate.toISOString().split('T')[0]);
      }
      if (gameFilters.dateFilter.endDate) {
        params.append('endDate', gameFilters.dateFilter.endDate.toISOString().split('T')[0]);
      }

      // Add amount range filters
      if (gameFilters.amountFilter.min) {
        params.append('minAmount', gameFilters.amountFilter.min);
      }
      if (gameFilters.amountFilter.max) {
        params.append('maxAmount', gameFilters.amountFilter.max);
      }

      const apiUrl = `${process.env.REACT_APP_API_URL}/api/game-transactions?${params.toString()}`;

      const response = await axios.get(apiUrl, {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      });

      if (response?.data?.success && response?.data?.data) {
        if (page === 1) {
          setGameTransactions(response.data.data);
        } else {
          setGameTransactions(prev => [...prev, ...response.data.data]);
        }

        setGameTransactionsPagination({
          currentPage: page,
          totalItems: response.data.totalItems || 0,
          hasMore: response.data.hasMore || false
        });
      }
    } catch (error) {
      console.error('Error fetching game transactions:', error);
    } finally {
      setGameTransactionsLoading(false);
    }
  };

  // Fetch dashboard analytics data
  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('Token') || '';

      // Format dates for API
      const startDate = dateRange?.[0]?.startDate ?
        dateRange[0].startDate.toISOString().split('T')[0] : null;
      const endDate = dateRange?.[0]?.endDate ?
        dateRange[0].endDate.toISOString().split('T')[0] : null;

      let url = `${process.env.REACT_APP_API_URL}/reports/dashboard`;

      // Add date range parameters
      const params = new URLSearchParams();
      if (startDate) params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);
      // Add groupBy parameter
      params.append('groupBy', activeTab);

      if (params.toString()) {
        url += `?${params.toString()}`;
      }

      const response = await axios.get(url, {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      });

      if (response?.data?.success && response?.data?.data) {
        setDashboardData(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  // Fetch specific purchases data
  const fetchPurchasesData = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('Token') || '';

      const startDate = dateRange?.[0]?.startDate ?
        dateRange[0].startDate.toISOString().split('T')[0] : null;
      const endDate = dateRange?.[0]?.endDate ?
        dateRange[0].endDate.toISOString().split('T')[0] : null;

      let url = `${process.env.REACT_APP_API_URL}/reports/purchases`;

      const params = new URLSearchParams();
      if (startDate) params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);
      params.append('groupBy', activeTab);
      params.append('page', '1');
      params.append('limit', '50');

      url += `?${params.toString()}`;

      const response = await axios.get(url, {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      });

      if (response?.data?.success && response?.data?.data) {
        setPurchasesData(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching purchases data:', error);
    } finally {
      setLoading(false);
    }
  };

  // Fetch specific redeems data
  const fetchRedeemsData = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('Token') || '';

      const startDate = dateRange?.[0]?.startDate ?
        dateRange[0].startDate.toISOString().split('T')[0] : null;
      const endDate = dateRange?.[0]?.endDate ?
        dateRange[0].endDate.toISOString().split('T')[0] : null;

      let url = `${process.env.REACT_APP_API_URL}/reports/redeems`;

      const params = new URLSearchParams();
      if (startDate) params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);
      params.append('groupBy', activeTab);
      params.append('page', '1');
      params.append('limit', '50');

      url += `?${params.toString()}`;

      const response = await axios.get(url, {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      });

      if (response?.data?.success && response?.data?.data) {
        setRedeemsData(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching redeems data:', error);
    } finally {
      setLoading(false);
    }
  };

  // Fetch wallet balances data
  const fetchWalletBalances = async () => {
    try {
      const token = localStorage.getItem('Token') || '';

      const url = `${process.env.REACT_APP_API_URL}/reports/wallet-balances`;

      const response = await axios.get(url, {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      });

      if (response?.data?.success && response?.data?.data) {
        setWalletBalances(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching wallet balances:', error);
    }
  };

  /**
   * Format a date string to a human-readable format
   * @param dateString - The date string to format
   * @returns Formatted date string or 'N/A' if invalid
   */
  const formatDate = (dateString: string): string => {
    if (!dateString) return 'N/A';

    try {
      // Use optional chaining to prevent runtime errors
      const date = new Date(dateString);

      // Check if the date is valid before formatting
      if (isNaN(date?.getTime())) {
        return 'Invalid Date';
      }

      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
      });
    } catch (error) {
      // Log the error but provide a fallback for the UI
      console.error('Error formatting date:', error);
      return 'Invalid Date';
    }
  };

  // Game transactions filter handlers
  const handleGameSearch = (e?: React.FormEvent) => {
    if (e) e.preventDefault();
    setGameTransactions([]);
    setGameTransactionsPagination(prev => ({ ...prev, currentPage: 1 }));
    fetchGameTransactions(1);
  };

  const handleGameFilterChange = (filterType: keyof GameTransactionFilters, value: any) => {
    setGameFilters(prev => ({
      ...prev,
      [filterType]: value
    }));
  };

  const clearGameFilters = () => {
    setGameFilters({
      searchTerm: '',
      dateFilter: {
        startDate: null,
        endDate: null
      },
      gameFilter: '',
      userFilter: '',
      transactionTypeFilter: '',
      statusFilter: '',
      amountFilter: {
        min: '',
        max: ''
      }
    });
    setGameTransactions([]);
    fetchGameTransactions(1);
  };

  // Get current data based on report type
  const getCurrentData = () => {
    switch (reportType) {
      case 'purchases':
        return purchasesData.items || [];
      case 'redeems':
        return redeemsData.items || [];
      case 'dashboard':
      default:
        return dashboardData?.purchases?.items || dashboardData?.redeems?.items || [];
    }
  };

  /**
   * Export report data to CSV file
   * Industry best practices:
   * - Proper error handling with user feedback
   * - Proper CSV escaping to handle special characters
   * - Type safety with optional chaining
   * - Descriptive filename with date
   */
  const exportToCSV = (): void => {
    const currentData = getCurrentData();
    if (!currentData?.length) {
      alert('No data available to export');
      return;
    }

    try {
      // Define CSV headers based on report type
      const headers = reportType === 'purchases' || reportType === 'redeems'
        ? ['ID', 'Count', 'Total Amount', 'Latest Activity']
        : ['Date', 'Count', 'Total Amount', 'Latest Activity'];

      // Helper function to escape CSV values properly
      const escapeCSV = (value: string | number): string => {
        const stringValue = String(value);
        // If the value contains commas, quotes, or newlines, wrap it in quotes and escape any quotes
        if (/[",\n\r]/.test(stringValue)) {
          return `"${stringValue.replace(/"/g, '""')}"`;
        }
        return stringValue;
      };

      // Convert data to CSV format
      const csvRows: string[] = [];

      // Add headers
      csvRows.push(headers.map(escapeCSV).join(','));

      // Add data rows with proper escaping
      currentData.forEach((item: any) => {
        const row = [
          escapeCSV(item?._id || ''),
          escapeCSV(item?.count || 0),
          escapeCSV(`$${(item?.totalAmount || 0).toFixed(2)}`),
          escapeCSV(item?.latestPurchase || item?.latestRedeem ?
            new Date(item?.latestPurchase || item?.latestRedeem).toLocaleDateString() : 'N/A')
        ];

        csvRows.push(row.join(','));
      });

      // Create CSV content
      const csvContent = csvRows.join('\n');

      // Create a Blob with the CSV content
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });

      // Generate a descriptive filename with date and time
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0];
      const filename = `reports_${reportType}_${activeTab}_${timestamp}.csv`;

      // Use the browser's download API
      // TypeScript type assertion for IE11 & Edge support
      const nav = window.navigator as any;
      if (nav.msSaveOrOpenBlob) {
        // IE11 & Edge
        nav.msSaveOrOpenBlob(blob, filename);
      } else {
        // Modern browsers
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);

        // Set link properties
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';

        // Append link to document, click it, and remove it
        document.body.appendChild(link);
        link.click();

        // Clean up
        setTimeout(() => {
          document.body.removeChild(link);
          URL.revokeObjectURL(url); // Free up memory by revoking the object URL
        }, 100);
      }
    } catch (error) {
      console.error('Error exporting CSV:', error);
      alert('Failed to export data. Please try again.');
    }
  };

  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
  };

  const handleDateRangeChange = (ranges: any) => {
    setDateRange([ranges.selection]);
  };

  const toggleDatePicker = () => {
    setShowDatePicker(!showDatePicker);
  };

  // Calculate summary data is performed directly in the component rendering

  // Check if reports feature is enabled
  // if (!isFeatureEnabled('REPORTS_ENABLED')) {
  //   return <FeatureDisabled featureName="Reports Dashboard" />;
  // }

  return (
    <div className="w-full bg-gray-50 rounded-lg p-4 sm:p-5 md:p-6 lg:ml-[250px] lg:max-w-[calc(100%-250px)] min-h-[500px] flex flex-col">
      {/* Add custom styles for date picker */}
      <style>{datePickerStyles}</style>

      {/* Header Section */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <h2 className="text-xl sm:text-2xl font-bold text-gray-800">Reports Dashboard</h2>

        {/* Filter Controls - Responsive layout */}
        <div className="flex flex-col w-full md:w-auto gap-3 bg-white p-3 rounded-lg shadow-sm">
          {/* Grouping filters */}
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => handleTabChange('date')}
              className={`flex-1 md:flex-none px-3 py-2 text-sm rounded-lg ${
                activeTab === 'date'
                  ? 'bg-[#495e26] text-white'
                  : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
              } transition-colors`}
            >
              By Date
            </button>
            <button
              onClick={() => handleTabChange('game')}
              className={`flex-1 md:flex-none px-3 py-2 text-sm rounded-lg ${
                activeTab === 'game'
                  ? 'bg-[#495e26] text-white'
                  : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
              } transition-colors`}
            >
              By Game
            </button>
            <button
              onClick={() => handleTabChange('user')}
              className={`flex-1 md:flex-none px-3 py-2 text-sm rounded-lg ${
                activeTab === 'user'
                  ? 'bg-[#495e26] text-white'
                  : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
              } transition-colors`}
            >
              By User
            </button>
            <div className="relative flex-1 md:flex-none">
              <button
                onClick={toggleDatePicker}
                ref={datePickerButtonRef}
                data-testid="date-picker-button"
                aria-label="Select date range"
                aria-expanded={showDatePicker}
                className="w-full md:w-auto px-3 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors flex items-center justify-center md:justify-start gap-2"
              >
                <svg className="w-4 h-4 text-[#495e26]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                <span className="text-sm">Date Range</span>
              </button>
              {showDatePicker && (
                <div className="absolute left-0 mt-2 z-20 bg-white shadow-md rounded-lg border border-gray-100 w-full sm:w-auto">
                  {/* Custom styling for mobile date picker */}
                  <div ref={datePickerRef} className="date-picker-container w-full max-w-[calc(100vw-20px)] sm:w-[280px] overflow-x-auto" role="dialog" aria-modal="true" aria-label="Date range picker">
                    {/* Wrap DateRangePicker in error boundary */}
                    <ErrorBoundary>
                      <DateRangePicker
                        ranges={dateRange}
                        onChange={handleDateRangeChange}
                        months={1}
                        direction="horizontal"
                        className="custom-date-picker"
                        // Using a safe format for weekday display
                        weekdayDisplayFormat="E"
                        weekStartsOn={1}
                        showMonthAndYearPickers={true}
                        showDateDisplay={false}
                        monthDisplayFormat="MMM yyyy"
                        dayDisplayFormat="d"
                      />
                    </ErrorBoundary>
                  </div>
                  <div className="p-3 flex justify-end">
                    <button
                      onClick={toggleDatePicker}
                      className="px-3 py-2 bg-[#495e26] text-white text-sm rounded-lg"
                    >
                      Apply
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Report type filters */}
          <div className="flex flex-wrap gap-2">
            <div className="flex items-center bg-gray-50 rounded-lg p-2 w-full md:w-auto border border-gray-100">
              <span className="text-sm mr-2 text-gray-600">Report Type:</span>
              <div className="flex flex-1 md:flex-none">
                <button
                  onClick={() => setReportType('dashboard')}
                  className={`flex-1 px-3 py-1.5 text-sm rounded-l-lg ${
                    reportType === 'dashboard'
                      ? 'bg-[#495e26] text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  } transition-colors`}
                >
                  Dashboard
                </button>
                <button
                  onClick={() => setReportType('purchases')}
                  className={`flex-1 px-3 py-1.5 text-sm ${
                    reportType === 'purchases'
                      ? 'bg-[#495e26] text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  } transition-colors`}
                >
                  Purchases
                </button>
                <button
                  onClick={() => setReportType('redeems')}
                  className={`flex-1 px-3 py-1.5 text-sm rounded-r-lg ${
                    reportType === 'redeems'
                      ? 'bg-[#495e26] text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  } transition-colors`}
                >
                  Redeems
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Game Transactions Section */}
      <div className="bg-white rounded-lg shadow-sm mb-6">
        {/* Header with Filter Toggle */}
        <div className="flex items-center justify-between p-5 border-b border-gray-100">
          <h3 className="text-lg font-semibold text-gray-800">Game Transactions</h3>
          <div className="flex items-center gap-2">
            <button
              onClick={() => setShowGameFilters(!showGameFilters)}
              className="flex items-center gap-2 px-3 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors"
            >
              <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z" />
              </svg>
              <span className="text-sm">Filters</span>
              <svg className={`w-4 h-4 transition-transform ${showGameFilters ? 'rotate-180' : ''}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>
          </div>
        </div>

        {/* Collapsible Filter Content */}
        <div className={`p-4 ${showGameFilters ? 'block' : 'hidden'}`}>
          {/* First Row - 3 Filters */}
          <div className="flex flex-wrap mb-4">
            {/* General Search */}
            <div className="w-full md:w-1/3 px-2 mb-4 md:mb-0">
              <div className="relative">
                <div className="flex items-center mb-1">
                  <label className="block text-gray-700 text-base font-medium">General Search</label>
                  <div className="relative group ml-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500 hover:text-gray-700 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-800 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none whitespace-nowrap z-10">
                      Search by Transaction ID or Amount
                    </div>
                  </div>
                </div>
                <input
                  type="text"
                  placeholder="Search by transaction ID or amount"
                  value={gameFilters.searchTerm}
                  onChange={(e) => handleGameFilterChange('searchTerm', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#495e26] focus:border-transparent"
                />
              </div>
            </div>

            {/* Game Filter */}
            <div className="w-full md:w-1/3 px-2 mb-4 md:mb-0">
              <div className="relative">
                <div className="flex items-center mb-1">
                  <label className="block text-gray-700 text-base font-medium">Game</label>
                  <div className="relative group ml-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500 hover:text-gray-700 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-800 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none whitespace-nowrap z-10">
                      Search by game name
                    </div>
                  </div>
                </div>
                <input
                  type="text"
                  placeholder="Filter by game name"
                  value={gameFilters.gameFilter}
                  onChange={(e) => handleGameFilterChange('gameFilter', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#495e26] focus:border-transparent"
                />
              </div>
            </div>

            {/* User Filter */}
            <div className="w-full md:w-1/3 px-2 mb-4 md:mb-0">
              <div className="relative">
                <div className="flex items-center mb-1">
                  <label className="block text-gray-700 text-base font-medium">User</label>
                  <div className="relative group ml-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500 hover:text-gray-700 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-800 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none whitespace-nowrap z-10">
                      Search by username or user ID
                    </div>
                  </div>
                </div>
                <input
                  type="text"
                  placeholder="Filter by user"
                  value={gameFilters.userFilter}
                  onChange={(e) => handleGameFilterChange('userFilter', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#495e26] focus:border-transparent"
                />
              </div>
            </div>
          </div>

          {/* Second Row - 3 Filters */}
          <div className="flex flex-wrap mb-4">
            {/* Transaction Type Filter */}
            <div className="w-full md:w-1/3 px-2 mb-4 md:mb-0">
              <div className="relative">
                <label className="block text-gray-700 text-base font-medium mb-1">Transaction Type</label>
                <select
                  value={gameFilters.transactionTypeFilter}
                  onChange={(e) => handleGameFilterChange('transactionTypeFilter', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#495e26] focus:border-transparent"
                >
                  <option value="">All Types</option>
                  <option value="purchase">Purchase</option>
                  <option value="redeem">Redeem</option>
                </select>
              </div>
            </div>

            {/* Status Filter */}
            <div className="w-full md:w-1/3 px-2 mb-4 md:mb-0">
              <div className="relative">
                <label className="block text-gray-700 text-base font-medium mb-1">Status</label>
                <select
                  value={gameFilters.statusFilter}
                  onChange={(e) => handleGameFilterChange('statusFilter', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#495e26] focus:border-transparent"
                >
                  <option value="">All Status</option>
                  <option value="completed">Completed</option>
                  <option value="pending">Pending</option>
                  <option value="failed">Failed</option>
                </select>
              </div>
            </div>

            {/* Amount Range Filter */}
            <div className="w-full md:w-1/3 px-2 mb-4 md:mb-0">
              <div className="relative">
                <label className="block text-gray-700 text-base font-medium mb-1">Amount Range</label>
                <div className="flex gap-2">
                  <input
                    type="number"
                    placeholder="Min"
                    value={gameFilters.amountFilter.min}
                    onChange={(e) => handleGameFilterChange('amountFilter', { ...gameFilters.amountFilter, min: e.target.value })}
                    className="w-1/2 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#495e26] focus:border-transparent"
                  />
                  <input
                    type="number"
                    placeholder="Max"
                    value={gameFilters.amountFilter.max}
                    onChange={(e) => handleGameFilterChange('amountFilter', { ...gameFilters.amountFilter, max: e.target.value })}
                    className="w-1/2 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#495e26] focus:border-transparent"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-wrap gap-2 pt-4 border-t border-gray-100">
            <button
              onClick={handleGameSearch}
              className="flex items-center gap-2 px-4 py-2 bg-[#495e26] text-white rounded-lg hover:bg-opacity-90 transition-colors"
            >
              <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
              Apply Filters
            </button>
            <button
              onClick={clearGameFilters}
              className="flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
            >
              <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
              Clear Filters
            </button>
          </div>
        </div>

        {/* Game Transactions Table */}
        <div className="overflow-x-auto">
          {gameTransactionsLoading ? (
            <div className="flex justify-center items-center h-32">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-[#495e26]"></div>
            </div>
          ) : (
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Transaction ID</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Game Name</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User ID</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-100">
                {gameTransactions.length > 0 ? (
                  gameTransactions.map((transaction, index) => (
                    <tr key={index} className="hover:bg-gray-50 transition-colors">
                      <td className="px-4 py-3 text-gray-800 font-medium">{transaction._id}</td>
                      <td className="px-4 py-3 text-gray-700">{transaction.gameName}</td>
                      <td className="px-4 py-3 text-gray-700">{transaction.userId}</td>
                      <td className="px-4 py-3">
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          transaction.transactionType === 'purchase'
                            ? 'bg-blue-100 text-blue-800'
                            : 'bg-green-100 text-green-800'
                        }`}>
                          {transaction.transactionType}
                        </span>
                      </td>
                      <td className="px-4 py-3 font-medium text-green-600">${transaction.amount.toFixed(2)}</td>
                      <td className="px-4 py-3">
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          transaction.status === 'completed'
                            ? 'bg-green-100 text-green-800'
                            : transaction.status === 'pending'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {transaction.status}
                        </span>
                      </td>
                      <td className="px-4 py-3 text-gray-500 text-xs">
                        {new Date(transaction.createdAt).toLocaleDateString()}
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={7} className="px-4 py-6 text-center text-gray-500">
                      <div className="flex flex-col items-center justify-center py-4">
                        <svg className="w-8 h-8 text-gray-300 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <p className="text-gray-500">No game transactions found</p>
                        <p className="text-gray-400 text-xs mt-1">Try adjusting your filters</p>
                      </div>
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          )}
        </div>

        {/* Pagination */}
        {gameTransactionsPagination.hasMore && (
          <div className="p-4 border-t border-gray-100">
            <button
              onClick={() => fetchGameTransactions(gameTransactionsPagination.currentPage + 1)}
              disabled={gameTransactionsLoading}
              className="w-full px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors disabled:opacity-50"
            >
              {gameTransactionsLoading ? 'Loading...' : 'Load More'}
            </button>
          </div>
        )}
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 sm:h-16 sm:w-16 border-t-4 border-b-4 border-[#495e26] shadow-md"></div>
        </div>
      ) : (
        <>
          {/* All Metrics in a single section */}
          <div className="bg-white rounded-lg p-5 mb-6 shadow-sm">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-gray-800">System Metrics</h3>
              <div className="flex items-center gap-2">
                <div className="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
                  {activeTab === 'daily' ? 'Daily View' : activeTab === 'weekly' ? 'Weekly View' : 'Monthly View'}
                </div>
                <button
                  onClick={exportToCSV}
                  className="flex items-center gap-1 px-3 py-1 bg-[#495e26] text-white text-sm rounded-lg hover:bg-opacity-90 transition-colors"
                  title="Export data to CSV"
                >
                  <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                  </svg>
                  <span>Export CSV</span>
                </button>
              </div>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {/* System-wide Totals from Wallet Balances */}
              <DashboardCard
                label="Total Sweeps Coins"
                value={walletBalances?.totalSweepsBalance?.grandTotal?.toLocaleString() || '0'}
                icon={
                  <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                }
                bgColor="bg-blue-50 text-blue-500"
              />

              <DashboardCard
                label="Total Gold Coins"
                value={walletBalances?.detailedBalances?.totalGoldCoins?.toLocaleString() || '0'}
                icon={
                  <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                }
                bgColor="bg-yellow-50 text-yellow-600"
              />

              <DashboardCard
                label="Total Wallets"
                value={walletBalances?.detailedBalances?.totalWallets?.toLocaleString() || '0'}
                icon={
                  <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                }
                bgColor="bg-purple-50 text-purple-600"
              />

              <DashboardCard
                label="Avg Gold Coins"
                value={Math.round(walletBalances?.detailedBalances?.avgGoldCoins || 0).toLocaleString()}
                icon={
                  <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                }
                bgColor="bg-green-50 text-green-600"
              />

              {/* Dashboard Summary Metrics */}
              {dashboardData?.summary && (
                <>
                  <DashboardCard
                    label="Total Purchases"
                    value={dashboardData.summary.totalPurchases?.toLocaleString() || '0'}
                    icon={
                      <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z" />
                      </svg>
                    }
                    bgColor="bg-purple-50 text-purple-600"
                  />

                  <DashboardCard
                    label="Purchase Amount"
                    value={`$${dashboardData.summary.totalPurchaseAmount?.toLocaleString() || '0'}`}
                    icon={
                      <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    }
                    bgColor="bg-indigo-50 text-indigo-600"
                  />

                  <DashboardCard
                    label="Total Redeems"
                    value={dashboardData.summary.totalRedeems?.toLocaleString() || '0'}
                    icon={
                      <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                      </svg>
                    }
                    bgColor="bg-orange-50 text-orange-600"
                  />

                  <DashboardCard
                    label="Redeem Amount"
                    value={`$${dashboardData.summary.totalRedeemAmount?.toLocaleString() || '0'}`}
                    icon={
                      <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                      </svg>
                    }
                    bgColor="bg-green-50 text-green-600"
                  />
                </>
              )}
            </div>
          </div>

          {/* Data Table Section */}
          <div className="bg-white rounded-lg shadow-sm">
            <div className="flex items-center justify-between p-5 border-b border-gray-100">
              <h3 className="text-lg font-semibold text-gray-800">Transaction Data</h3>
              <svg className="w-5 h-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
              </svg>
            </div>

            {/* Desktop Table View */}
            <div className="hidden md:block w-full overflow-x-auto lg:flex-1 lg:overflow-y-auto">
              <div className="min-w-[1000px]">
                <table className="w-full text-sm reports-table">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {activeTab === 'date' ? 'Date' : activeTab === 'game' ? 'Game ID' : 'User ID'}
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Count
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Total Amount
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Latest Activity
                      </th>
                      {reportType === 'purchases' && (
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Payment Methods
                        </th>
                      )}
                      {reportType === 'redeems' && (
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Games
                        </th>
                      )}
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-100">
                    {(() => {
                      const currentData = getCurrentData();
                      return currentData.length > 0 ? (
                        currentData.map((item: any, index: number) => (
                          <tr
                            key={index}
                            className="hover:bg-gray-50 transition-colors"
                          >
                            <td className="px-4 py-3 text-gray-800 font-medium">
                              {activeTab === 'date' ? formatDate(item?._id) : item?._id || 'N/A'}
                            </td>
                            <td className="px-4 py-3 text-gray-700">{item?.count || 0}</td>
                            <td className="px-4 py-3 font-medium text-green-600">
                              ${(item?.totalAmount || 0).toFixed(2)}
                            </td>
                            <td className="px-4 py-3 text-gray-500 text-xs">
                              {item?.latestPurchase || item?.latestRedeem ?
                                new Date(item?.latestPurchase || item?.latestRedeem).toLocaleDateString() : 'N/A'}
                            </td>
                            {reportType === 'purchases' && (
                              <td className="px-4 py-3 text-gray-700">
                                {item?.paymentMethods?.join(', ') || 'N/A'}
                              </td>
                            )}
                            {reportType === 'redeems' && (
                              <td className="px-4 py-3 text-gray-700">
                                {item?.games?.length || 0} games
                              </td>
                            )}
                          </tr>
                        ))
                      ) : (
                        <tr>
                          <td colSpan={reportType === 'dashboard' ? 4 : 5} className="px-4 py-6 text-center text-gray-500">
                            <div className="flex flex-col items-center justify-center py-4">
                              <svg className="w-8 h-8 text-gray-300 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                              <p className="text-gray-500">No data available for the selected period</p>
                              <p className="text-gray-400 text-xs mt-1">Try changing your filters or date range</p>
                            </div>
                          </td>
                        </tr>
                      );
                    })()}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Mobile Card View */}
            <div className="md:hidden p-4">
              {(() => {
                const currentData = getCurrentData();
                return currentData.length > 0 ? (
                  <div className="space-y-4">
                    {currentData.map((item: any, index: number) => (
                      <div
                        key={index}
                        className="bg-white rounded-lg border-l-4 border-[#495e26] p-4 shadow-sm"
                      >
                        <div className="flex justify-between items-start mb-3">
                          <h3 className="text-base font-bold text-gray-800">
                            {activeTab === 'date' ? formatDate(item?._id) : item?._id || 'N/A'}
                          </h3>
                          <span className="text-xs text-gray-500">
                            {item?.latestPurchase || item?.latestRedeem ?
                              new Date(item?.latestPurchase || item?.latestRedeem).toLocaleDateString() : 'N/A'}
                          </span>
                        </div>

                        <div className="grid grid-cols-2 gap-3 text-sm">
                          <div className="border-b border-gray-100 pb-2">
                            <p className="text-gray-500 text-xs">Count</p>
                            <p className="font-bold text-gray-800">{item?.count || 0}</p>
                          </div>
                          <div className="border-b border-gray-100 pb-2">
                            <p className="text-gray-500 text-xs">Total Amount</p>
                            <p className="font-bold text-green-600">${(item?.totalAmount || 0).toFixed(2)}</p>
                          </div>
                          {reportType === 'purchases' && item?.paymentMethods && (
                            <div className="col-span-2 pt-1">
                              <p className="text-gray-500 text-xs">Payment Methods</p>
                              <p className="font-bold text-gray-800">{item.paymentMethods.join(', ')}</p>
                            </div>
                          )}
                          {reportType === 'redeems' && item?.games && (
                            <div className="col-span-2 pt-1">
                              <p className="text-gray-500 text-xs">Games</p>
                              <p className="font-bold text-gray-800">{item.games.length} games</p>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="bg-white rounded-lg border border-gray-100 p-4 text-center">
                    <svg className="w-8 h-8 text-gray-300 mx-auto mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <p className="text-gray-500">No data available</p>
                    <p className="text-gray-400 text-xs mt-1">Try changing your filters or date range</p>
                  </div>
                );
              })()}
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default Reports;
