
import React, { useState, useRef, useEffect } from 'react';
import { isFeatureEnabled } from '../../../utils/featureFlags';
import FeatureDisabled from '../../../components/FeatureDisabled';
import ReportsHeader from './components/ReportsHeader';
import GameTransactionsSection from './components/GameTransactionsSection';
import OrderAnalyticsSection from './components/OrderAnalyticsSection';
import TransactionDataTable from './components/TransactionDataTable';
import { useReportsData } from './hooks/useReportsData';
import 'react-date-range/dist/styles.css';
import 'react-date-range/dist/theme/default.css';

interface ReportData {
  date: string;
  newCustomers: number;
  totalCustomers: number;
  averageProcessedTime: number;
  totalSales?: number;
  transactionCount?: number;
  gamePurchases?: number;
  gameRedeems?: number;
  goldCoinPurchases?: number;
  cashRedeems?: number;
  approvalTime?: string;
}

interface SystemTotals {
  totalSweepsCoins: number;
  totalGoldCoins: number;
  totalGamePurchases: number;
  totalCashRedeems: number;
}

interface DateRange {
  startDate: Date;
  endDate: Date;
  key: string;
}

interface GameTransaction {
  _id: string;
  userId: string;
  gameId: string;
  gameName: string;
  transactionType: 'purchase' | 'redeem';
  amount: number;
  status: 'pending' | 'completed' | 'failed';
  gameInfo: any;
  createdAt: string;
}

interface GameTransactionFilters {
  searchTerm: string;
  dateFilter: {
    startDate: Date | null;
    endDate: Date | null;
  };
  gameFilter: string;
  userFilter: string;
  transactionTypeFilter: string;
  statusFilter: string;
  amountFilter: {
    min: string;
    max: string;
  };
}

interface GameTransactionsPagination {
  currentPage: number;
  totalItems: number;
  hasMore: boolean;
}

interface DashboardCardProps {
  label: string;
  value: string | number;
  icon: React.ReactNode;
  bgColor: string;
}

const DashboardCard: React.FC<DashboardCardProps> = ({ label, value, icon, bgColor }) => (
  <div className="dashboard-card bg-white rounded-lg p-4 border-l-4 border-[#495e26] shadow-sm hover:shadow transition-all duration-300">
    <div className="flex items-center justify-between">
      <div className="flex flex-col">
        <div className="text-xs text-gray-500 mb-1">{label}</div>
        <span className="text-lg font-bold">{value}</span>
      </div>
      <div className={`p-2 rounded-md ${bgColor}`}>{icon}</div>
    </div>
  </div>
);

const Reports: React.FC = () => {
  // Custom hook for all reports data and logic
  const {
    // State
    activeTab,
    dateRange,
    gameFilters,
    gameTransactions,
    gameTransactionsPagination,
    systemTotals,
    reportData,
    loading,
    gameTransactionsLoading,

    // Actions
    handleGameFilterChange,
    handleDateRangeChange,
    handleTabChange,
    handlePageChange,
  } = useReportsData();

  // Local state for UI components
  const [showDatePicker, setShowDatePicker] = useState<boolean>(false);

  // Use React refs for better performance and reliability
  const datePickerRef = useRef<HTMLDivElement>(null);
  const datePickerButtonRef = useRef<HTMLButtonElement>(null);

  // Handle clicks outside the date picker to close it
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        datePickerRef.current &&
        datePickerButtonRef.current &&
        !datePickerRef.current.contains(event.target as Node) &&
        !datePickerButtonRef.current.contains(event.target as Node)
      ) {
        setShowDatePicker(false);
      }
    };

    // Only add the event listener when the date picker is open
    if (showDatePicker) {
      // Use capture phase to ensure we handle the event before it reaches other handlers
      document.addEventListener('mousedown', handleClickOutside, true);
    }

    // Clean up the event listener when the component unmounts or the date picker closes
    return () => {
      document.removeEventListener('mousedown', handleClickOutside, true);
    };
  }, [showDatePicker]);

  const toggleDatePicker = () => {
    setShowDatePicker(!showDatePicker);
  };

  // Calculate totals for game transactions section
  const totalTransactions = gameTransactions.length;
  const totalPurchaseAmount = gameTransactions
    .filter(t => t.transactionType === 'purchase')
    .reduce((sum, t) => sum + t.amount, 0);
  const totalRedeemAmount = gameTransactions
    .filter(t => t.transactionType === 'redeem')
    .reduce((sum, t) => sum + t.amount, 0);

  // Custom styles for date picker
  const datePickerStyles = `
    .custom-date-picker .rdrCalendarWrapper {
      font-size: 12px;
    }
    .custom-date-picker .rdrDateRangeWrapper {
      width: 100%;
    }
    .custom-date-picker .rdrDefinedRangesWrapper {
      display: none;
    }
    .custom-date-picker .rdrMonth {
      width: 100%;
    }
    .custom-date-picker .rdrWeekDays {
      padding: 0;
    }
    .custom-date-picker .rdrDay {
      height: 2em;
    }
    @media (max-width: 640px) {
      .date-picker-container {
        transform: translateX(-50%);
        left: 50%;
      }
    }
  `;



  /**
   * Format a date string to a human-readable format
   * @param dateString - The date string to format
   * @returns Formatted date string or 'N/A' if invalid
   */
  const formatDate = (dateString: string): string => {
    if (!dateString) return 'N/A';

    try {
      // Use optional chaining to prevent runtime errors
      const date = new Date(dateString);

      // Check if the date is valid before formatting
      if (isNaN(date?.getTime())) {
        return 'Invalid Date';
      }

      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
      });
    } catch (error) {
      // Log the error but provide a fallback for the UI
      console.error('Error formatting date:', error);
      return 'Invalid Date';
    }
  };

  /**
   * Export report data to CSV file
   * Industry best practices:
   * - Proper error handling with user feedback
   * - Proper CSV escaping to handle special characters
   * - Type safety with optional chaining
   * - Descriptive filename with date
   */
  const exportToCSV = (): void => {
    if (!reportData?.length) {
      alert('No data available to export');
      return;
    }

    try {
      // Define CSV headers
      const headers = [
        'Date',
        'Game Purchases',
        'Game Redeems',
        'Gold Coin Purchases',
        'Cash Redeems',
        'Total Transactions',
        'Approval Time'
      ];

      // Helper function to escape CSV values properly
      const escapeCSV = (value: string | number): string => {
        const stringValue = String(value);
        // If the value contains commas, quotes, or newlines, wrap it in quotes and escape any quotes
        if (/[",\n\r]/.test(stringValue)) {
          return `"${stringValue.replace(/"/g, '""')}"`;
        }
        return stringValue;
      };

      // Convert data to CSV format
      const csvRows: string[] = [];

      // Add headers
      csvRows.push(headers.map(escapeCSV).join(','));

      // Add data rows with proper escaping
      reportData.forEach(item => {
        const row = [
          escapeCSV(formatDate(item?.date || '')),
          escapeCSV(item?.gamePurchases || 0),
          escapeCSV(item?.gameRedeems || 0),
          escapeCSV(item?.goldCoinPurchases || 0),
          escapeCSV(`$${(item?.cashRedeems || 0).toFixed(2)}`),
          escapeCSV(item?.transactionCount || 0),
          escapeCSV(item?.approvalTime ? new Date(item?.approvalTime).toLocaleTimeString() : 'N/A')
        ];

        csvRows.push(row.join(','));
      });

      // Create CSV content
      const csvContent = csvRows.join('\n');

      // Create a Blob with the CSV content
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });

      // Generate a descriptive filename with date and time
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0];
      const filename = `reports_${activeTab}_${timestamp}.csv`;

      // Use the browser's download API
      // TypeScript type assertion for IE11 & Edge support
      const nav = window.navigator as any;
      if (nav.msSaveOrOpenBlob) {
        // IE11 & Edge
        nav.msSaveOrOpenBlob(blob, filename);
      } else {
        // Modern browsers
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);

        // Set link properties
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';

        // Append link to document, click it, and remove it
        document.body.appendChild(link);
        link.click();

        // Clean up
        setTimeout(() => {
          document.body.removeChild(link);
          URL.revokeObjectURL(url); // Free up memory by revoking the object URL
        }, 100);
      }
    } catch (error) {
      console.error('Error exporting CSV:', error);
      alert('Failed to export data. Please try again.');
    }
  };



  // Calculate summary data is performed directly in the component rendering

  // Check if reports feature is enabled
  if (!isFeatureEnabled('REPORTS_ENABLED')) {
    return <FeatureDisabled featureName="Reports Dashboard" />;
  }

  return (
    <div className="w-full bg-gray-50 rounded-lg p-4 sm:p-5 md:p-6 lg:ml-[250px] lg:max-w-[calc(100%-250px)] min-h-[500px] flex flex-col">
      {/* Reports Header */}
      <ReportsHeader
        activeTab={activeTab}
        dateRange={dateRange}
        showDatePicker={showDatePicker}
        onTabChange={handleTabChange}
        onDateRangeChange={handleDateRangeChange}
        onToggleDatePicker={toggleDatePicker}
        datePickerButtonRef={datePickerButtonRef}
        datePickerRef={datePickerRef}
        datePickerStyles={datePickerStyles}
      />

      {/* Order Analytics Section */}
      <OrderAnalyticsSection
        systemTotals={systemTotals}
        reportData={reportData}
        loading={loading}
      />

      {/* Game Transactions Section */}
      <GameTransactionsSection
        gameFilters={gameFilters}
        onFilterChange={handleGameFilterChange}
        totalTransactions={totalTransactions}
        totalPurchaseAmount={totalPurchaseAmount}
        totalRedeemAmount={totalRedeemAmount}
      />

      {/* Transaction Data Table */}
      <TransactionDataTable
        transactions={gameTransactions}
        loading={gameTransactionsLoading}
        pagination={gameTransactionsPagination}
        onPageChange={handlePageChange}
      />
    </div>
  );
};

export default Reports;
