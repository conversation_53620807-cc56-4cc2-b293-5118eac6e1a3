
import React, { useState, useEffect, Component, type ReactNode, type ErrorInfo } from 'react';
import { isFeatureEnabled } from '../../../utils/featureFlags';
import FeatureDisabled from '../../../components/FeatureDisabled';
import axios from 'axios';
import { useSelector } from 'react-redux';
import { RootState } from '../../../redux/store';
import { DateRangePicker } from 'react-date-range';
import 'react-date-range/dist/styles.css';
import 'react-date-range/dist/theme/default.css';

// Error Boundary component to catch errors in the DateRangePicker
interface ErrorBoundaryProps {
  children: ReactNode;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    console.error('Error caught by boundary:', error, errorInfo);
  }

  render(): ReactNode {
    if (this.state.hasError) {
      return (
        <div className="text-red-500">
          <h2>Something went wrong in the reports component.</h2>
          <details className="whitespace-pre-wrap">
            {this.state.error?.toString()}
          </details>
        </div>
      );
    }

    return this.props.children;
  }
}

// Custom CSS for responsive date picker and enhanced styling
const datePickerStyles = `
  /* Extra small screens utility class */
  @media (min-width: 480px) {
    .xs\\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  /* Enhanced animations */
  .dashboard-card {
    transition: all 0.3s ease;
  }

  .dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  }

  /* Improved date picker styling */
  .date-picker-container {
    position: relative;
  }

  .date-picker-container .rdrCalendarWrapper {
    font-size: 12px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  }

  .date-picker-container .rdrMonth {
    width: 100%;
    padding: 0.5rem;
  }

  .date-picker-container .rdrDay {
    height: 2.5em;
    width: 2.5em;
  }

  .date-picker-container .rdrMonthAndYearWrapper {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    height: auto;
  }

  .date-picker-container .rdrMonthAndYearPickers select {
    padding: 0.25rem;
  }

  /* Fix for date picker positioning */
  .date-picker-container {
    max-height: 80vh;
    overflow-y: auto;
  }

  .date-picker-container .rdrDateDisplayItem {
    border-radius: 8px;
    background-color: rgba(73, 94, 38, 0.1);
    border-color: rgba(73, 94, 38, 0.2);
  }

  .date-picker-container .rdrDateDisplayItemActive {
    border-color: #495e26;
  }

  .date-picker-container .rdrDayToday .rdrDayNumber span:after {
    background: #495e26;
  }

  .date-picker-container .rdrDayDisabled {
    background-color: #f8f8f8;
  }

  .date-picker-container .rdrMonthAndYearWrapper {
    padding-top: 10px;
  }

  .date-picker-container .rdrDay:not(.rdrDayPassive) .rdrInRange,
  .date-picker-container .rdrDay:not(.rdrDayPassive) .rdrStartEdge,
  .date-picker-container .rdrDay:not(.rdrDayPassive) .rdrEndEdge,
  .date-picker-container .rdrDay:not(.rdrDayPassive) .rdrSelected {
    background-color: #495e26;
  }

  @media (max-width: 640px) {
    .date-picker-container .rdrCalendarWrapper {
      font-size: 12px;
    }
    .date-picker-container .rdrMonth {
      width: 100%;
      min-width: 250px;
    }
    .date-picker-container .rdrDefinedRangesWrapper {
      display: none;
    }
  }

  /* Table styling improvements */
  .reports-table th {
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .reports-table tr {
    transition: background-color 0.2s ease;
  }

  /* Card styling */
  .stat-card {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
  }

  .stat-card:hover {
    transform: translateY(-5px);
  }

  .stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, #495e26, #7a9e42);
  }

  /* Filter button styling */
  .filter-btn {
    transition: all 0.2s ease;
  }

  .filter-btn:hover {
    transform: translateY(-2px);
  }
`;

interface ReportData {
  date: string;
  newCustomers: number;
  totalCustomers: number;
  averageProcessedTime: number;
  totalSales?: number;
  transactionCount?: number;
  gamePurchases?: number;
  gameRedeems?: number;
  goldCoinPurchases?: number;
  cashRedeems?: number;
  approvalTime?: string;
}

interface SystemTotals {
  totalSweepsCoins: number;
  totalGoldCoins: number;
  totalGamePurchases: number;
  totalCashRedeems: number;
}

interface DateRange {
  startDate: Date;
  endDate: Date;
  key: string;
}

interface GameTransaction {
  _id: string;
  userId: string;
  gameId: string;
  gameName: string;
  transactionType: 'purchase' | 'redeem';
  amount: number;
  status: 'pending' | 'completed' | 'failed';
  gameInfo: any;
  createdAt: string;
}

interface GameTransactionFilters {
  searchTerm: string;
  dateFilter: {
    startDate: Date | null;
    endDate: Date | null;
  };
  gameFilter: string;
  userFilter: string;
  transactionTypeFilter: string;
  statusFilter: string;
  amountFilter: {
    min: string;
    max: string;
  };
}

interface GameTransactionsPagination {
  currentPage: number;
  totalItems: number;
  hasMore: boolean;
}

interface DashboardCardProps {
  label: string;
  value: string | number;
  icon: React.ReactNode;
  bgColor: string;
}

const DashboardCard: React.FC<DashboardCardProps> = ({ label, value, icon, bgColor }) => (
  <div className="dashboard-card bg-white rounded-lg p-4 border-l-4 border-[#495e26] shadow-sm hover:shadow transition-all duration-300">
    <div className="flex items-center justify-between">
      <div className="flex flex-col">
        <div className="text-xs text-gray-500 mb-1">{label}</div>
        <span className="text-lg font-bold">{value}</span>
      </div>
      <div className={`p-2 rounded-md ${bgColor}`}>{icon}</div>
    </div>
  </div>
);

const Reports: React.FC = () => {
  // Using Redux state for authentication
  useSelector((state: RootState) => state.auth);
  const [loading, setLoading] = useState<boolean>(false);
  const [reportData, setReportData] = useState<ReportData[]>([]);
  const [systemTotals, setSystemTotals] = useState<SystemTotals>({
    totalSweepsCoins: 0,
    totalGoldCoins: 0,
    totalGamePurchases: 0,
    totalCashRedeems: 0
  });
  const [activeTab, setActiveTab] = useState<string>('daily');
  const [filterType, setFilterType] = useState<string>('approvalTime'); // 'approvalTime' or 'requestTime'
  const [dateRange, setDateRange] = useState<DateRange[]>([
    {
      startDate: new Date(new Date().setDate(new Date().getDate() - 7)),
      endDate: new Date(),
      key: 'selection',
    },
  ]);
  const [showDatePicker, setShowDatePicker] = useState<boolean>(false);

  // Game transactions state
  const [gameTransactions, setGameTransactions] = useState<GameTransaction[]>([]);
  const [gameTransactionsLoading, setGameTransactionsLoading] = useState<boolean>(false);
  const [gameTransactionsPagination, setGameTransactionsPagination] = useState<GameTransactionsPagination>({
    currentPage: 1,
    totalItems: 0,
    hasMore: false
  });
  const [showGameFilters, setShowGameFilters] = useState<boolean>(false);

  // Game transaction filters
  const [gameFilters, setGameFilters] = useState<GameTransactionFilters>({
    searchTerm: '',
    dateFilter: {
      startDate: null,
      endDate: null
    },
    gameFilter: '',
    userFilter: '',
    transactionTypeFilter: '',
    statusFilter: '',
    amountFilter: {
      min: '',
      max: ''
    }
  });

  // Use React refs for better performance and reliability
  const datePickerRef = React.useRef<HTMLDivElement>(null);
  const datePickerButtonRef = React.useRef<HTMLButtonElement>(null);

  // Handle clicks outside the date picker to close it
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        datePickerRef.current &&
        datePickerButtonRef.current &&
        !datePickerRef.current.contains(event.target as Node) &&
        !datePickerButtonRef.current.contains(event.target as Node)
      ) {
        setShowDatePicker(false);
      }
    };

    // Only add the event listener when the date picker is open
    if (showDatePicker) {
      // Use capture phase to ensure we handle the event before it reaches other handlers
      document.addEventListener('mousedown', handleClickOutside, true);
    }

    // Clean up the event listener when the component unmounts or the date picker closes
    return () => {
      document.removeEventListener('mousedown', handleClickOutside, true);
    };
  }, [showDatePicker]);

  useEffect(() => {
    // This would be replaced by RTK Query's automatic data fetching
    fetchReportData();
    fetchSystemTotals();
    fetchGameTransactions(1);

    // With RTK Query, we wouldn't need these manual fetch calls
    // The data would be automatically fetched when the query parameters change
  }, [activeTab, dateRange, filterType]);

  // Fetch game transactions when filters change
  useEffect(() => {
    fetchGameTransactions(1);
  }, [gameFilters]);

  // NOTE: This is a placeholder for RTK Query implementation
  // In a real implementation, we would use RTK Query hooks instead of these direct API calls

  // Example of how this would be structured with RTK Query:
  // const { data: systemTotals, isLoading: isSystemTotalsLoading } = useGetSystemTotalsQuery();
  // const { data: reportData, isLoading: isReportDataLoading } = useGetReportDataQuery({
  //   groupBy: activeTab,
  //   startDate: dateRange[0]?.startDate?.toISOString(),
  //   endDate: dateRange[0]?.endDate?.toISOString(),
  //   filterBy: filterType
  // });

  // Fetch system-wide totals - To be replaced with RTK Query
  const fetchSystemTotals = async () => {
    try {
      const token = localStorage.getItem('Token') || '';

      const url = `${process.env.REACT_APP_API_URL}/analytics/system-totals`;

      const response = await axios.get(url, {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          Authorization: `Bearer ${token}`,
        },
      });

      if (response?.data?.data) {
        setSystemTotals(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching system totals:', error);
    }
  };

  // Fetch game transactions with filters
  const fetchGameTransactions = async (page: number = 1) => {
    try {
      setGameTransactionsLoading(true);
      const token = localStorage.getItem('Token') || '';

      // Build query parameters
      const params = new URLSearchParams();
      params.append('page', page.toString());
      params.append('limit', '25');

      // Add filters
      if (gameFilters.searchTerm.trim()) {
        params.append('search', gameFilters.searchTerm.trim());
      }
      if (gameFilters.gameFilter.trim()) {
        params.append('gameName', gameFilters.gameFilter.trim());
      }
      if (gameFilters.userFilter.trim()) {
        params.append('userSearch', gameFilters.userFilter.trim());
      }
      if (gameFilters.transactionTypeFilter) {
        params.append('transactionType', gameFilters.transactionTypeFilter);
      }
      if (gameFilters.statusFilter) {
        params.append('status', gameFilters.statusFilter);
      }
      if (gameFilters.dateFilter.startDate) {
        params.append('startDate', gameFilters.dateFilter.startDate.toISOString().split('T')[0]);
      }
      if (gameFilters.dateFilter.endDate) {
        params.append('endDate', gameFilters.dateFilter.endDate.toISOString().split('T')[0]);
      }
      if (gameFilters.amountFilter.min) {
        params.append('minAmount', gameFilters.amountFilter.min);
      }
      if (gameFilters.amountFilter.max) {
        params.append('maxAmount', gameFilters.amountFilter.max);
      }

      const apiUrl = `${process.env.REACT_APP_API_URL}/game-transactions?${params.toString()}`;

      const response = await axios.get(apiUrl, {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          Authorization: `Bearer ${token}`,
        },
      });

      if (response?.data?.success && response?.data?.data) {
        if (page === 1) {
          setGameTransactions(response.data.data);
        } else {
          setGameTransactions(prev => [...prev, ...response.data.data]);
        }

        setGameTransactionsPagination({
          currentPage: response.data.pagination.currentPage,
          totalItems: response.data.pagination.totalItems || 0,
          hasMore: response.data.pagination.hasMore || false
        });

        // Store summary data if available
        if (response.data.summary) {
          console.log('Game transactions summary:', response.data.summary);
        }
      }
    } catch (error) {
      console.error('Error fetching game transactions:', error);
    } finally {
      setGameTransactionsLoading(false);
    }
  };

  // Fetch transaction and user data - To be replaced with RTK Query
  const fetchReportData = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('Token') || '';

      let url = `${process.env.REACT_APP_API_URL}/analytics?groupBy=${activeTab}`;

      // Add date range filter - using optional chaining to prevent errors
      if (dateRange?.[0]?.startDate && dateRange?.[0]?.endDate) {
        url += `&startDate=${dateRange[0].startDate.toISOString()}`;
        url += `&endDate=${dateRange[0].endDate.toISOString()}`;
      }

      // Add filter type (approval time vs request time)
      url += `&filterBy=${filterType}`;

      const response = await axios.get(url, {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          Authorization: `Bearer ${token}`,
        },
      });

      if (response?.data?.data) {
        setReportData(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching report data:', error);
    } finally {
      setLoading(false);
    }
  };

  /**
   * Format a date string to a human-readable format
   * @param dateString - The date string to format
   * @returns Formatted date string or 'N/A' if invalid
   */
  const formatDate = (dateString: string): string => {
    if (!dateString) return 'N/A';

    try {
      // Use optional chaining to prevent runtime errors
      const date = new Date(dateString);

      // Check if the date is valid before formatting
      if (isNaN(date?.getTime())) {
        return 'Invalid Date';
      }

      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
      });
    } catch (error) {
      // Log the error but provide a fallback for the UI
      console.error('Error formatting date:', error);
      return 'Invalid Date';
    }
  };

  /**
   * Export report data to CSV file
   * Industry best practices:
   * - Proper error handling with user feedback
   * - Proper CSV escaping to handle special characters
   * - Type safety with optional chaining
   * - Descriptive filename with date
   */
  const exportToCSV = (): void => {
    if (!reportData?.length) {
      alert('No data available to export');
      return;
    }

    try {
      // Define CSV headers
      const headers = [
        'Date',
        'Game Purchases',
        'Game Redeems',
        'Gold Coin Purchases',
        'Cash Redeems',
        'Total Transactions',
        'Approval Time'
      ];

      // Helper function to escape CSV values properly
      const escapeCSV = (value: string | number): string => {
        const stringValue = String(value);
        // If the value contains commas, quotes, or newlines, wrap it in quotes and escape any quotes
        if (/[",\n\r]/.test(stringValue)) {
          return `"${stringValue.replace(/"/g, '""')}"`;
        }
        return stringValue;
      };

      // Convert data to CSV format
      const csvRows: string[] = [];

      // Add headers
      csvRows.push(headers.map(escapeCSV).join(','));

      // Add data rows with proper escaping
      reportData.forEach(item => {
        const row = [
          escapeCSV(formatDate(item?.date || '')),
          escapeCSV(item?.gamePurchases || 0),
          escapeCSV(item?.gameRedeems || 0),
          escapeCSV(item?.goldCoinPurchases || 0),
          escapeCSV(`$${(item?.cashRedeems || 0).toFixed(2)}`),
          escapeCSV(item?.transactionCount || 0),
          escapeCSV(item?.approvalTime ? new Date(item?.approvalTime).toLocaleTimeString() : 'N/A')
        ];

        csvRows.push(row.join(','));
      });

      // Create CSV content
      const csvContent = csvRows.join('\n');

      // Create a Blob with the CSV content
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });

      // Generate a descriptive filename with date and time
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0];
      const filename = `reports_${activeTab}_${timestamp}.csv`;

      // Use the browser's download API
      // TypeScript type assertion for IE11 & Edge support
      const nav = window.navigator as any;
      if (nav.msSaveOrOpenBlob) {
        // IE11 & Edge
        nav.msSaveOrOpenBlob(blob, filename);
      } else {
        // Modern browsers
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);

        // Set link properties
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';

        // Append link to document, click it, and remove it
        document.body.appendChild(link);
        link.click();

        // Clean up
        setTimeout(() => {
          document.body.removeChild(link);
          URL.revokeObjectURL(url); // Free up memory by revoking the object URL
        }, 100);
      }
    } catch (error) {
      console.error('Error exporting CSV:', error);
      alert('Failed to export data. Please try again.');
    }
  };

  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
  };

  const handleDateRangeChange = (ranges: any) => {
    setDateRange([ranges.selection]);
  };

  const toggleDatePicker = () => {
    setShowDatePicker(!showDatePicker);
  };

  // Game transactions filter handlers
  const handleGameSearch = (e?: React.FormEvent) => {
    if (e) e.preventDefault();
    setGameTransactions([]);
    setGameTransactionsPagination(prev => ({ ...prev, currentPage: 1 }));
    fetchGameTransactions(1);
  };

  const handleGameFilterChange = (filterType: keyof GameTransactionFilters, value: any) => {
    setGameFilters(prev => ({
      ...prev,
      [filterType]: value
    }));
  };

  const clearGameFilters = () => {
    setGameFilters({
      searchTerm: '',
      dateFilter: {
        startDate: null,
        endDate: null
      },
      gameFilter: '',
      userFilter: '',
      transactionTypeFilter: '',
      statusFilter: '',
      amountFilter: {
        min: '',
        max: ''
      }
    });
    setGameTransactions([]);
    fetchGameTransactions(1);
  };

  // Calculate summary data is performed directly in the component rendering

  // Check if reports feature is enabled
  if (!isFeatureEnabled('REPORTS_ENABLED')) {
    return <FeatureDisabled featureName="Reports Dashboard" />;
  }

  return (
    <div className="w-full bg-gray-50 rounded-lg p-4 sm:p-5 md:p-6 lg:ml-[250px] lg:max-w-[calc(100%-250px)] min-h-[500px] flex flex-col">
      {/* Add custom styles for date picker */}
      <style>{datePickerStyles}</style>

      {/* Header Section */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <h2 className="text-xl sm:text-2xl font-bold text-gray-800">Reports Dashboard</h2>

        {/* Filter Controls - Responsive layout */}
        <div className="flex flex-col w-full md:w-auto gap-3 bg-white p-3 rounded-lg shadow-sm">
          {/* Time period filters */}
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => handleTabChange('daily')}
              className={`flex-1 md:flex-none px-3 py-2 text-sm rounded-lg ${activeTab === 'daily'
                  ? 'bg-[#495e26] text-white'
                  : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
                } transition-colors`}
            >
              Daily
            </button>
            <button
              onClick={() => handleTabChange('weekly')}
              className={`flex-1 md:flex-none px-3 py-2 text-sm rounded-lg ${activeTab === 'weekly'
                  ? 'bg-[#495e26] text-white'
                  : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
                } transition-colors`}
            >
              Weekly
            </button>
            <button
              onClick={() => handleTabChange('monthly')}
              className={`flex-1 md:flex-none px-3 py-2 text-sm rounded-lg ${activeTab === 'monthly'
                  ? 'bg-[#495e26] text-white'
                  : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
                } transition-colors`}
            >
              Monthly
            </button>
            <div className="relative flex-1 md:flex-none">
              <button
                onClick={toggleDatePicker}
                ref={datePickerButtonRef}
                data-testid="date-picker-button"
                aria-label="Select date range"
                aria-expanded={showDatePicker}
                className="w-full md:w-auto px-3 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors flex items-center justify-center md:justify-start gap-2"
              >
                <svg className="w-4 h-4 text-[#495e26]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                <span className="text-sm">Date Range</span>
              </button>
              {showDatePicker && (
                <div className="absolute left-0 mt-2 z-20 bg-white shadow-md rounded-lg border border-gray-100 w-full sm:w-auto">
                  {/* Custom styling for mobile date picker */}
                  <div ref={datePickerRef} className="date-picker-container w-full max-w-[calc(100vw-20px)] sm:w-[280px] overflow-x-auto" role="dialog" aria-modal="true" aria-label="Date range picker">
                    {/* Wrap DateRangePicker in error boundary */}
                    <ErrorBoundary>
                      <DateRangePicker
                        ranges={dateRange}
                        onChange={handleDateRangeChange}
                        months={1}
                        direction="horizontal"
                        className="custom-date-picker"
                        // Using a safe format for weekday display
                        weekdayDisplayFormat="E"
                        weekStartsOn={1}
                        showMonthAndYearPickers={true}
                        showDateDisplay={false}
                        monthDisplayFormat="MMM yyyy"
                        dayDisplayFormat="d"
                      />
                    </ErrorBoundary>
                  </div>
                  <div className="p-3 flex justify-end">
                    <button
                      onClick={toggleDatePicker}
                      className="px-3 py-2 bg-[#495e26] text-white text-sm rounded-lg"
                    >
                      Apply
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Filter by approval time vs request time */}
          <div className="flex flex-wrap gap-2">
            <div className="flex items-center bg-gray-50 rounded-lg p-2 w-full md:w-auto border border-gray-100">
              <span className="text-sm mr-2 text-gray-600">Filter by:</span>
              <div className="flex flex-1 md:flex-none">
                <button
                  onClick={() => setFilterType('approvalTime')}
                  className={`flex-1 px-3 py-1.5 text-sm rounded-l-lg ${filterType === 'approvalTime'
                      ? 'bg-[#495e26] text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    } transition-colors`}
                >
                  Approval Time
                </button>
                <button
                  onClick={() => setFilterType('requestTime')}
                  className={`flex-1 px-3 py-1.5 text-sm rounded-r-lg ${filterType === 'requestTime'
                      ? 'bg-[#495e26] text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    } transition-colors`}
                >
                  Request Time
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Game Transactions Section */}
      <div className="bg-white rounded-lg shadow-sm mb-6">
        {/* Header */}
        <div className="flex items-center justify-between p-5 border-b border-gray-100">
          <h3 className="text-lg font-semibold text-gray-800">Game Transactions</h3>
        </div>

        {/* Filters Section with Toggle Button - Exact copy from purchase request table */}
        <div className="w-full bg-[#f5f5f0] rounded-lg mb-4 overflow-hidden border border-gray-200">
          <div className="flex justify-between items-center p-4" onClick={() => setShowGameFilters(!showGameFilters)}>
            <div className="flex items-center cursor-pointer">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-[#4D7C0F]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
              </svg>
              <span className="font-medium">Filter</span>
            </div>
            <div className="flex items-center">
              <span className="text-sm text-gray-600 mr-2">
                {gameTransactions.length} transactions found
              </span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className={`h-5 w-5 text-gray-500 transition-transform duration-200 ${showGameFilters ? 'rotate-180' : ''}`}
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </div>
          </div>

          {/* Filter Content - Exact copy from purchase request table */}
          <div className={`p-4 ${showGameFilters ? 'block' : 'hidden'}`}>
            {/* First Row - 3 Filters */}
            <div className="flex flex-wrap mb-4">
              {/* General Search */}
              <div className="w-full md:w-1/3 px-2 mb-4 md:mb-0">
                <div className="relative">
                  <div className="flex items-center mb-1">
                    <label className="block text-gray-700 text-base font-medium">General Search</label>
                    <div className="relative group ml-2">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500 hover:text-gray-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-800 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none whitespace-nowrap z-10">
                        Search by Transaction ID or Amount only
                      </div>
                    </div>
                  </div>
                  <input
                    type="text"
                    placeholder="Search by transaction ID or amount"
                    value={gameFilters.searchTerm}
                    onChange={(e) => handleGameFilterChange('searchTerm', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#4D7C0F] focus:border-transparent"
                  />
                </div>
              </div>

              {/* Game Filter */}
              <div className="w-full md:w-1/3 px-2 mb-4 md:mb-0">
                <div className="relative">
                  <div className="flex items-center mb-1">
                    <label className="block text-gray-700 text-base font-medium">Game</label>
                    <div className="relative group ml-2">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500 hover:text-gray-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-800 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none whitespace-nowrap z-10">
                        Search by game name (partial matches supported)
                      </div>
                    </div>
                  </div>
                  <input
                    type="text"
                    placeholder="Filter by game name"
                    value={gameFilters.gameFilter}
                    onChange={(e) => handleGameFilterChange('gameFilter', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#4D7C0F] focus:border-transparent"
                  />
                </div>
              </div>

              {/* User Filter */}
              <div className="w-full md:w-1/3 px-2 mb-4 md:mb-0">
                <div className="relative">
                  <div className="flex items-center mb-1">
                    <label className="block text-gray-700 text-base font-medium">User</label>
                    <div className="relative group ml-2">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500 hover:text-gray-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-800 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none whitespace-nowrap z-10">
                        Search by username, first name, last name, or full name
                      </div>
                    </div>
                  </div>
                  <input
                    type="text"
                    placeholder="Search by username, first name, or last name"
                    value={gameFilters.userFilter}
                    onChange={(e) => handleGameFilterChange('userFilter', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#4D7C0F] focus:border-transparent"
                  />
                </div>
              </div>
            </div>

            {/* Second Row - 3 Filters */}
            <div className="flex flex-wrap">
              {/* Email Filter */}
              <div className="w-full md:w-1/3 px-2 mb-4 md:mb-0">
                <div className="relative">
                  <div className="flex items-center mb-1">
                    <label className="block text-gray-700 text-base font-medium">Email</label>
                    <div className="relative group ml-2">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500 hover:text-gray-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-800 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none whitespace-nowrap z-10">
                        Search by user email address (partial matches supported)
                      </div>
                    </div>
                  </div>
                  <input
                    type="text"
                    placeholder="Filter by email"
                    value={gameFilters.userFilter}
                    onChange={(e) => handleGameFilterChange('userFilter', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#4D7C0F] focus:border-transparent"
                  />
                </div>
              </div>

              {/* Status Filter */}
              <div className="w-full md:w-1/3 px-2 mb-4 md:mb-0">
                <div className="relative">
                  <div className="flex items-center mb-1">
                    <label className="block text-gray-700 text-base font-medium">Status</label>
                    <div className="relative group ml-2">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500 hover:text-gray-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-800 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none whitespace-nowrap z-10">
                        Filter by transaction status
                      </div>
                    </div>
                  </div>
                  <select
                    value={gameFilters.statusFilter}
                    onChange={(e) => handleGameFilterChange('statusFilter', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#4D7C0F] focus:border-transparent"
                  >
                    <option value="">All Status</option>
                    <option value="pending">Pending</option>
                    <option value="completed">Completed</option>
                    <option value="rejected">Rejected</option>
                    <option value="failed">Failed</option>
                  </select>
                </div>
              </div>

              {/* Date Range Filter */}
              <div className="w-full md:w-1/3 px-2 mb-4 md:mb-0">
                <div className="relative">
                  <div className="flex items-center mb-1">
                    <label className="block text-gray-700 text-base font-medium">Date Range</label>
                    <div className="relative group ml-2">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500 hover:text-gray-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-800 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none whitespace-nowrap z-10">
                        Filter by date range
                      </div>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <input
                      type="date"
                      value={gameFilters.dateFilter.startDate ? gameFilters.dateFilter.startDate.toISOString().split('T')[0] : ''}
                      onChange={(e) => handleGameFilterChange('dateFilter', {
                        ...gameFilters.dateFilter,
                        startDate: e.target.value ? new Date(e.target.value) : null
                      })}
                      className="w-1/2 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#4D7C0F] focus:border-transparent"
                    />
                    <input
                      type="date"
                      value={gameFilters.dateFilter.endDate ? gameFilters.dateFilter.endDate.toISOString().split('T')[0] : ''}
                      onChange={(e) => handleGameFilterChange('dateFilter', {
                        ...gameFilters.dateFilter,
                        endDate: e.target.value ? new Date(e.target.value) : null
                      })}
                      className="w-1/2 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#4D7C0F] focus:border-transparent"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-wrap gap-2 pt-4 border-t border-gray-100 mt-4">
              <button
                onClick={handleGameSearch}
                className="flex items-center gap-2 px-4 py-2 bg-[#4D7C0F] text-white rounded-lg hover:bg-opacity-90 transition-colors"
              >
                <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
                Apply Filters
              </button>
              <button
                onClick={clearGameFilters}
                className="flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
              >
                <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
                Clear Filters
              </button>
            </div>
          </div>

          {/* Game Transactions Table - Exact style from purchase request table */}
          <div className="overflow-x-auto">
            {gameTransactionsLoading ? (
              <div className="flex justify-center items-center h-32">
                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-[#4D7C0F]"></div>
              </div>
            ) : (
              <div className="min-w-[1000px] shadow-md rounded-lg border border-gray-200">
                <table className="w-full text-2xl">
                  <thead className="bg-lime-900">
                    <tr>
                      <th className="px-4 py-4 text-left text-base sm:text-lg font-bold text-white whitespace-nowrap">
                        TRANSACTION ID
                      </th>
                      <th className="px-4 py-4 text-left text-base sm:text-lg font-bold text-white whitespace-nowrap">
                        USER
                      </th>
                      <th className="px-4 py-4 text-left text-base sm:text-lg font-bold text-white whitespace-nowrap">
                        EMAIL
                      </th>
                      <th className="px-4 py-4 text-left text-base sm:text-lg font-bold text-white whitespace-nowrap">
                        GAME
                      </th>
                      <th className="px-4 py-4 text-left text-base sm:text-lg font-bold text-white whitespace-nowrap">
                        DATE
                      </th>
                      <th className="px-4 py-4 text-left text-base sm:text-lg font-bold text-white whitespace-nowrap">
                        AMOUNT
                      </th>
                      <th className="px-4 py-4 text-left text-base sm:text-lg font-bold text-white whitespace-nowrap">
                        STATUS
                      </th>
                      <th className="px-4 py-4 text-left text-base sm:text-lg font-bold text-white whitespace-nowrap">
                        TYPE
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {gameTransactions.length > 0 ? (
                      gameTransactions.map((transaction, index) => (
                        <tr key={transaction._id} className={`hover:bg-gray-50 ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}>
                          <td className="px-4 py-4 text-gray-800 font-medium">{transaction._id}</td>
                          <td className="px-4 py-4 text-lime-900 font-medium">
                            {transaction.userId || 'Unknown User'}
                          </td>
                          <td className="px-4 py-4 text-gray-700">
                            {transaction.userId ? 'User Email' : 'No email'}
                          </td>
                          <td className="px-4 py-4 text-lime-900 font-medium">{transaction.gameName}</td>
                          <td className="px-4 py-4 text-gray-700">
                            {new Date(transaction.createdAt).toLocaleDateString()}
                          </td>
                          <td className="px-4 py-4 font-semibold text-gray-800">${transaction.amount}</td>
                          <td className="px-4 py-4">
                            <span
                              className={`px-3 py-1 text-sm font-semibold rounded-full ${transaction.status === 'completed'
                                  ? 'bg-green-100 text-green-800'
                                  
                                    : transaction.status === 'failed'
                                      ? 'bg-gray-100 text-gray-800'
                                      : 'bg-blue-100 text-blue-800'
                                }`}
                            >
                              {transaction.status.toUpperCase()}
                            </span>
                          </td>
                          <td className="px-4 py-4">
                            <span
                              className={`px-3 py-1 text-sm font-semibold rounded-full ${transaction.transactionType === 'purchase'
                                  ? 'bg-purple-100 text-purple-800'
                                  : 'bg-orange-100 text-orange-800'
                                }`}
                            >
                              {transaction.transactionType.toUpperCase()}
                            </span>
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan={8} className="px-4 py-6 text-center text-gray-500">
                          <div className="flex flex-col items-center justify-center py-4">
                            <svg className="w-8 h-8 text-gray-300 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <p className="text-gray-500">No game transactions found</p>
                            <p className="text-gray-400 text-xs mt-1">Try adjusting your filters</p>
                          </div>
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            )}
          </div>

          {/* Pagination */}
          {gameTransactionsPagination.hasMore && (
            <div className="p-4 border-t border-gray-100">
              <button
                onClick={() => fetchGameTransactions(gameTransactionsPagination.currentPage + 1)}
                disabled={gameTransactionsLoading}
                className="w-full px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors disabled:opacity-50"
              >
                {gameTransactionsLoading ? 'Loading...' : 'Load More'}
              </button>
            </div>
          )}
        </div>

        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 sm:h-16 sm:w-16 border-t-4 border-b-4 border-[#495e26] shadow-md"></div>
          </div>
        ) : (
          <>
            {/* Order Data Metrics Section */}
            <div className="bg-white rounded-lg p-5 mb-6 shadow-sm">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold text-gray-800">Order Transaction Analytics</h3>
                <div className="flex items-center gap-2">
                  <div className="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
                    {activeTab === 'daily' ? 'Daily View' : activeTab === 'weekly' ? 'Weekly View' : 'Monthly View'}
                  </div>
                  <button
                    onClick={exportToCSV}
                    className="flex items-center gap-1 px-3 py-1 bg-[#495e26] text-white text-sm rounded-lg hover:bg-opacity-90 transition-colors"
                    title="Export data to CSV"
                  >
                    <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                    </svg>
                    <span>Export CSV</span>
                  </button>
                </div>
              </div>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {/* Order-based Metrics from reportData */}
                <DashboardCard
                  label="Total Orders (Card/Crypto)"
                  value={reportData?.reduce((sum, item) => sum + (item?.gamePurchases || 0), 0).toLocaleString() || '0'}
                  icon={
                    <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 11-4 0v-6m4 0V9a2 2 0 10-4 0v4.01" />
                    </svg>
                  }
                  bgColor="bg-blue-50 text-blue-500"
                />

                <DashboardCard
                  label="Total Redeems (Push-to-Card)"
                  value={reportData?.reduce((sum, item) => sum + (item?.gameRedeems || 0), 0).toLocaleString() || '0'}
                  icon={
                    <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                  }
                  bgColor="bg-green-50 text-green-600"
                />

                <DashboardCard
                  label="Total Purchase Amount"
                  value={`$${reportData?.reduce((sum, item) => sum + (item?.goldCoinPurchases || 0), 0).toLocaleString() || '0'}`}
                  icon={
                    <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  }
                  bgColor="bg-purple-50 text-purple-600"
                />

                <DashboardCard
                  label="Total Redeem Amount"
                  value={`$${reportData?.reduce((sum, item) => sum + (item?.cashRedeems || 0), 0).toLocaleString() || '0'}`}
                  icon={
                    <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                  }
                  bgColor="bg-orange-50 text-orange-600"
                />

                {/* Period-specific metrics */}
                <DashboardCard
                  label="Average Order Value"
                  value={`$${reportData?.length > 0 ?
                    (reportData.reduce((sum, item) => sum + (item?.goldCoinPurchases || 0), 0) /
                      reportData.reduce((sum, item) => sum + (item?.gamePurchases || 0), 0) || 1).toFixed(2) : '0'}`}
                  icon={
                    <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                  }
                  bgColor="bg-indigo-50 text-indigo-600"
                />

                <DashboardCard
                  label="Total Transactions"
                  value={reportData?.reduce((sum, item) => sum + (item?.transactionCount || 0), 0).toLocaleString() || '0'}
                  icon={
                    <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                  }
                  bgColor="bg-gray-50 text-gray-600"
                />

                <DashboardCard
                  label="Success Rate"
                  value={`${reportData?.length > 0 ?
                    ((reportData.reduce((sum, item) => sum + (item?.gamePurchases || 0) + (item?.gameRedeems || 0), 0) /
                      reportData.reduce((sum, item) => sum + (item?.transactionCount || 0), 0)) * 100 || 0).toFixed(1) : '0'}%`}
                  icon={
                    <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  }
                  bgColor="bg-emerald-50 text-emerald-600"
                />

                <DashboardCard
                  label="Net Revenue"
                  value={`$${(reportData?.reduce((sum, item) => sum + (item?.goldCoinPurchases || 0), 0) -
                    reportData?.reduce((sum, item) => sum + (item?.cashRedeems || 0), 0) || 0).toLocaleString()}`}
                  icon={
                    <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                    </svg>
                  }
                  bgColor="bg-teal-50 text-teal-600"
                />
              </div>
            </div>

            {/* Data Table Section */}
            <div className="bg-white rounded-lg shadow-sm">
              <div className="flex items-center justify-between p-5 border-b border-gray-100">
                <h3 className="text-lg font-semibold text-gray-800">Transaction Data</h3>
                <svg className="w-5 h-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
                </svg>
              </div>

              {/* Desktop Table View */}
              <div className="hidden md:block w-full overflow-x-auto lg:flex-1 lg:overflow-y-auto">
                <div className="min-w-[1000px]">
                  <table className="w-full text-sm reports-table">
                    <thead>
                      <tr className="border-b border-gray-200">
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Date
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Game Purchases
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Game Redeems
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Gold Coin Purchases
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Cash Redeems
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Total Transactions
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Approval Time
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-100">
                      {reportData.length > 0 ? (
                        reportData.map((item, index) => (
                          <tr
                            key={index}
                            className="hover:bg-gray-50 transition-colors"
                          >
                            <td className="px-4 py-3 text-gray-800 font-medium">{formatDate(item?.date)}</td>
                            <td className="px-4 py-3 text-gray-700">{item?.gamePurchases || 0}</td>
                            <td className="px-4 py-3 text-gray-700">{item?.gameRedeems || 0}</td>
                            <td className="px-4 py-3 text-gray-700">{item?.goldCoinPurchases || 0}</td>
                            <td className="px-4 py-3 font-medium text-green-600">${(item?.cashRedeems || 0).toFixed(2)}</td>
                            <td className="px-4 py-3 text-gray-700">{item?.transactionCount || 0}</td>
                            <td className="px-4 py-3 text-gray-500 text-xs">
                              {item?.approvalTime ? new Date(item?.approvalTime).toLocaleTimeString() : 'N/A'}
                            </td>
                          </tr>
                        ))
                      ) : (
                        <tr>
                          <td colSpan={7} className="px-4 py-6 text-center text-gray-500">
                            <div className="flex flex-col items-center justify-center py-4">
                              <svg className="w-8 h-8 text-gray-300 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                              <p className="text-gray-500">No data available for the selected period</p>
                              <p className="text-gray-400 text-xs mt-1">Try changing your filters or date range</p>
                            </div>
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Mobile Card View */}
              <div className="md:hidden p-4">
                {reportData.length > 0 ? (
                  <div className="space-y-4">
                    {reportData.map((item, index) => (
                      <div
                        key={index}
                        className="bg-white rounded-lg border-l-4 border-[#495e26] p-4 shadow-sm"
                      >
                        <div className="flex justify-between items-start mb-3">
                          <h3 className="text-base font-bold text-gray-800">
                            {formatDate(item?.date)}
                          </h3>
                          <span className="text-xs text-gray-500">
                            {item?.approvalTime ? new Date(item?.approvalTime).toLocaleTimeString() : 'N/A'}
                          </span>
                        </div>

                        <div className="grid grid-cols-2 gap-3 text-sm">
                          <div className="border-b border-gray-100 pb-2">
                            <p className="text-gray-500 text-xs">Game Purchases</p>
                            <p className="font-bold text-gray-800">{item?.gamePurchases || 0}</p>
                          </div>
                          <div className="border-b border-gray-100 pb-2">
                            <p className="text-gray-500 text-xs">Game Redeems</p>
                            <p className="font-bold text-gray-800">{item?.gameRedeems || 0}</p>
                          </div>
                          <div className="border-b border-gray-100 pb-2">
                            <p className="text-gray-500 text-xs">Gold Coin Purchases</p>
                            <p className="font-bold text-gray-800">{item?.goldCoinPurchases || 0}</p>
                          </div>
                          <div className="border-b border-gray-100 pb-2">
                            <p className="text-gray-500 text-xs">Cash Redeems</p>
                            <p className="font-bold text-green-600">${(item?.cashRedeems || 0).toFixed(2)}</p>
                          </div>
                          <div className="col-span-2 pt-1">
                            <p className="text-gray-500 text-xs">Total Transactions</p>
                            <p className="font-bold text-gray-800">{item?.transactionCount || 0}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="bg-white rounded-lg border border-gray-100 p-4 text-center">
                    <svg className="w-8 h-8 text-gray-300 mx-auto mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <p className="text-gray-500">No data available</p>
                    <p className="text-gray-400 text-xs mt-1">Try changing your filters or date range</p>
                  </div>
                )}
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  )
}

export default Reports;
