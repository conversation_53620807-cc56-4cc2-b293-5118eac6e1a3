import React, { SVGProps } from 'react';

const SvgSignalWifiStatusbarConnectedNoInternet3 = (props: SVGProps<SVGSVGElement>) => {
  return (
    <svg viewBox="0 0 24 24" fill="currentColor" className="svg-icon" {...props}>
      <path d="M0 0h24v24H0V0z" fill="none" />
      <path
        d="M17 9V8h5.92C19.97 5.51 16.16 4 12 4 7.31 4 3.07 5.9 0 8.98l2.82 2.82A12.93 12.93 0 0112 8c1.77 0 3.46.36 5 1z"
        fillOpacity={0.3}
      />
      <path d="M2.82 11.8L12 21l5-5.01V9c-1.54-.64-3.23-1-5-1-3.59 0-6.83 1.45-9.18 3.8zM19 18h2v2h-2zM19 10h2v6h-2z" />
    </svg>
  );
};

export default SvgSignalWifiStatusbarConnectedNoInternet3;
