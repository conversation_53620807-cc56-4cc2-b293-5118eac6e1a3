import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON><PERSON>lertTriangle, FiRefreshCw } from 'react-icons/fi';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  componentName?: string;
  showDetails?: boolean;
  suppressConsoleError?: boolean;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

/**
 * RobustErrorBoundary component that provides comprehensive error handling
 * with special focus on preventing errors from leaking to the UI in production.
 */
class RobustErrorBoundary extends Component<Props, State> {
  private originalConsoleError: typeof console.error | null = null;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidMount() {
    if (this.props.suppressConsoleError && process.env.NODE_ENV !== 'development') {
      // Store original console.error
      this.originalConsoleError = console.error;
      
      // Override console.error to suppress React error messages in production
      console.error = (...args) => {
        // Check if this is a React error related to this component
        const isReactError = args.some(arg => 
          typeof arg === 'string' && 
          (arg.includes('React will try to recreate this component tree') || 
           arg.includes('The above error occurred in the') ||
           arg.includes('Consider adding an error boundary'))
        );

        // If it's a React error and we're in production, suppress it
        if (isReactError && process.env.NODE_ENV !== 'development') {
          // Still log it for monitoring, but in a way that doesn't trigger the overlay
          if (this.originalConsoleError) {
            this.originalConsoleError('Suppressed React error in production:', ...args);
          }
          return;
        }

        // Otherwise, pass through to original console.error
        if (this.originalConsoleError) {
          this.originalConsoleError.apply(console, args);
        }
      };
    }
  }

  componentWillUnmount() {
    // Restore original console.error when component unmounts
    if (this.originalConsoleError) {
      console.error = this.originalConsoleError;
      this.originalConsoleError = null;
    }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // Log the error to an error reporting service
    this.logErrorToService(error, errorInfo);
    
    this.setState({
      error,
      errorInfo
    });

    // Prevent the error from propagating to the console in production
    if (process.env.NODE_ENV !== 'development') {
      // This prevents the red error screen in production
      // but still allows proper logging
      if (window && window.console && window.console.error) {
        const originalConsoleError = window.console.error;
        window.console.error = (...args) => {
          if (args[0] === error || 
              (typeof args[0] === 'string' && args[0].includes(error.message))) {
            // Suppress this specific error from showing in console
            return;
          }
          originalConsoleError.apply(window.console, args);
        };
        
        // Restore original after a short delay
        setTimeout(() => {
          window.console.error = originalConsoleError;
        }, 1000);
      }
    }
  }

  logErrorToService(error: Error, errorInfo: ErrorInfo): void {
    // In a real app, you would send this to your error tracking service
    // like Sentry, LogRocket, etc.
    console.error('Error caught by RobustErrorBoundary:', {
      error: error.toString(),
      componentStack: errorInfo.componentStack,
      componentName: this.props.componentName || 'Unknown'
    });

    // You could also log to your backend API
    // Example:
    // fetch('/api/log-error', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify({
    //     error: error.toString(),
    //     stack: error.stack,
    //     componentStack: errorInfo.componentStack,
    //     componentName: this.props.componentName || 'Unknown',
    //     url: window.location.href,
    //     userAgent: navigator.userAgent,
    //     timestamp: new Date().toISOString()
    //   })
    // }).catch(console.error);
  }

  handleRetry = (): void => {
    this.setState({ hasError: false, error: null, errorInfo: null });
  };

  handleReload = (): void => {
    window.location.reload();
  };

  render(): ReactNode {
    const { hasError, error } = this.state;
    const { children, fallback, showDetails } = this.props;
    
    if (hasError) {
      // If a custom fallback is provided, use it
      if (fallback) {
        return fallback;
      }

      // Check if we're in development or production
      const isDev = process.env.NODE_ENV === 'development';
      
      return (
        <div className="flex flex-col items-center justify-center p-6 rounded-lg bg-white shadow-md m-4 text-center">
          <FiAlertTriangle className="text-red-500 w-16 h-16 mb-4" />
          
          <h2 className="text-xl font-bold text-gray-800 mb-2">
            {isDev ? 'Something went wrong' : 'Oops! Something went wrong'}
          </h2>
          
          <p className="text-gray-600 mb-4">
            {isDev 
              ? 'An error occurred in this component.' 
              : 'We encountered an unexpected issue. Our team has been notified.'}
          </p>
          
          {/* Show error details only in development or if explicitly allowed */}
          {(isDev || showDetails) && error && (
            <div className="bg-gray-100 p-4 rounded mb-4 text-left w-full overflow-auto max-h-48">
              <p className="font-mono text-red-600 text-sm">{error.toString()}</p>
              {error.stack && (
                <pre className="mt-2 text-xs text-gray-700 whitespace-pre-wrap">
                  {error.stack}
                </pre>
              )}
            </div>
          )}
          
          <div className="flex space-x-4 mt-2">
            <button
              onClick={this.handleRetry}
              className="flex items-center px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
            >
              <FiRefreshCw className="mr-2" /> Try Again
            </button>
            
            <button
              onClick={this.handleReload}
              className="px-4 py-2 border border-gray-300 rounded hover:bg-gray-100 transition-colors"
            >
              Reload Page
            </button>
          </div>
        </div>
      );
    }

    return children;
  }
}

export default RobustErrorBoundary;
