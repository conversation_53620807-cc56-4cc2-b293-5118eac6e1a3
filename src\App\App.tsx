import * as Sentry from "@sentry/react";
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { TourProvider } from '@reactour/tour';
import dayjs from 'dayjs';
import localizedFormat from 'dayjs/plugin/localizedFormat';
import relativeTime from 'dayjs/plugin/relativeTime';
import { useContext, useEffect, useLayoutEffect, useRef } from 'react';
import { ThemeProvider } from 'react-jss';
import { ReactNotifications } from 'react-notifications-component';
import { useDispatch } from 'react-redux';
import { useLocation } from 'react-router-dom';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { useFullscreen } from 'react-use';
import COLORS from '../common/data/enumColors';
import ChunkErrorBoundary from '../components/ChunkErrorBoundary';
import GlobalErrorHandler from '../components/ErrorBoundary/GlobalErrorHandler';
import RobustErrorBoundary from '../components/ErrorBoundary/RobustErrorBoundary';
import UseEffectErrorHandler from '../components/ErrorBoundary/UseEffectErrorHandler';
import PWAInstallPrompt from '../components/PWAInstallPrompt';
import NetworkStatusMonitor from '../components/NetworkStatusMonitor';
import PWAUpdateManager from '../components/PWAUpdateManager';
// SentryTest removed after successful implementation



import DocumentTitle from '../components/DocumentTitle';
import LiveChatWidget from '../components/LiveChatWidget';
import ThemeContext from '../contexts/themeContext';
import { UserProvider } from '../contexts/userContext';
import { getOS } from '../helpers/helpers';
import useDarkMode from '../hooks/useDarkMode';
import Portal from '../layout/Portal/Portal';
import Wrapper from '../layout/Wrapper/Wrapper';
import { verifyToken } from '../redux/authSlice';
import { AppDispatch } from '../redux/store';
import steps, { styles } from '../steps';
import AppErrorBoundary from './AppErrorBoundary';
import { logErrorToService } from '../utils/errorHandling';

import Navbar from '../pages/presentation/home/<USER>';
import Footer from '../pages/presentation/home/<USER>';

// Global error handler for unhandled promise rejections
if (typeof window !== 'undefined') {
  window.addEventListener('unhandledrejection', (event) => {
    // Log the error to our service
    logErrorToService(event.reason);

    // Show a user-friendly toast notification in production
    if (process.env.NODE_ENV === 'production') {
      toast.error('An unexpected error occurred. Please try again later.', {
        position: 'top-right',
        autoClose: 5000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      });
    } else {
      // In development, show more details
      console.warn('Unhandled Promise Rejection:', event.reason);
    }

    // Prevent the default browser behavior (which would log to console)
    event.preventDefault();
  });
}

// Chunk loading error handling
if (process.env.REACT_APP_CHUNK_RETRY === 'true') {
  const originalRequire = (window as any).__webpack_require__;
  if (originalRequire) {
    (window as any).__webpack_require__ = function (id: string) {
      try {
        return originalRequire(id);
      } catch (error) {
        if (error && (error as any).name === 'ChunkLoadError') {
          window.location.reload();
        }
        throw error;
      }
    };
    (window as any).__webpack_require__.e = function (chunkId: string) {
      return originalRequire.e(chunkId).catch((error: Error) => {
        if (error && (error as any).name === 'ChunkLoadError') {
          window.location.reload();
          return new Promise(() => { });
        }
        throw error;
      });
    };
  }
}

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 1,
    },
  },
});

const App = () => {
  const dispatch = useDispatch<AppDispatch>();
  getOS();
  dayjs.extend(localizedFormat);
  dayjs.extend(relativeTime);
  const token = localStorage.getItem('Token');

  /**
   * Dark Mode
   */
  const { themeStatus, darkModeStatus } = useDarkMode();
  const theme = {
    theme: themeStatus,
    primary: COLORS.PRIMARY.code,
    secondary: COLORS.SECONDARY.code,
    success: COLORS.SUCCESS.code,
    info: COLORS.INFO.code,
    warning: COLORS.WARNING.code,
    danger: COLORS.DANGER.code,
    dark: COLORS.DARK.code,
    light: COLORS.LIGHT.code,
  };

  useEffect(() => {
    if (darkModeStatus) {
      document.documentElement.setAttribute('theme', 'dark');
      document.documentElement.setAttribute('data-bs-theme', 'dark');
    }
    return () => {
      document.documentElement.removeAttribute('theme');
      document.documentElement.removeAttribute('data-bs-theme');
    };
  }, [darkModeStatus]);

  /**
   * Full Screen
   */
  const { fullScreenStatus, setFullScreenStatus } = useContext(ThemeContext);
  const ref = useRef(null);
  useFullscreen(ref, fullScreenStatus, {
    onClose: () => setFullScreenStatus(false),
  });

  /**
   * Modern Design
   */
  useLayoutEffect(() => {
    if (process.env.REACT_APP_MODERN_DESGIN === 'true') {
      document.body.classList.add('modern-design');
    } else {
      document.body.classList.remove('modern-design');
    }
  });
  const location = useLocation();

  const hideNavbarRoutes = [
    '/admin',
    '/login',
    '/signup',
    '/user/dashboard',
    '/admin/dashboard',
    '/cashier/dashboard',
    '/manager/dashboard',
    '/admin/login',
    '/admin/signup',
  ];
  // Footer routes are now handled inside the Footer component

  // List of all valid routes
  const validRoutes = [
    '/',
    '/admin',
    '/login',
    '/signup',
    '/user/dashboard',
    '/admin/dashboard',
    '/cashier/dashboard',
    '/manager/dashboard',
    '/admin/login',
    '/admin/signup',
    '/faq',
    '/contact',
    '/privacy-policy',
    '/terms',
    '/refund-policy',
    '/sweepstakes-rules',
  ];

  // Check if current path is a valid route
  const isValidRoute = validRoutes.some(
    (route) => location.pathname === route || location.pathname.startsWith(route + '/')
  );

  const shouldShowNavbar =
    isValidRoute &&
    !hideNavbarRoutes.some(
      (route) => location.pathname === route || location.pathname.startsWith(route + '/')
    );

  // Footer visibility is now handled inside the Footer component

  useEffect(() => {
    if (token) {
      dispatch(verifyToken());
    }
  }, [dispatch, token]);

  return (
    <UseEffectErrorHandler>
      <GlobalErrorHandler>
        <AppErrorBoundary>
          <ChunkErrorBoundary>
            <RobustErrorBoundary
              componentName="AppRoot"
              suppressConsoleError={true}
              showDetails={process.env.NODE_ENV === 'development'}
            >
              <QueryClientProvider client={queryClient}>
                <ThemeProvider theme={theme}>
                <UserProvider>
                  <DocumentTitle title="America's #1 Online Sweepstakes Casino" />
                  <ToastContainer
                    position="top-right"
                    autoClose={4000}
                    hideProgressBar={false}
                    newestOnTop
                    closeOnClick
                    rtl={false}
                    pauseOnFocusLoss
                    draggable
                    pauseOnHover
                    theme="colored"
                    toastClassName="rounded-lg shadow-lg"
                    bodyClassName="text-sm font-medium"
                    progressClassName="bg-white"
                  />
                  <TourProvider steps={steps} styles={styles}>
                    <div
                      ref={ref}
                      className="app"
                      style={{
                        backgroundColor: fullScreenStatus ? 'var(--bs-body-bg)' : undefined,
                        zIndex: fullScreenStatus ? 1 : undefined,
                        overflow: fullScreenStatus ? 'scroll' : undefined,
                      }}
                    >
                      {shouldShowNavbar && <Navbar />}
                      {/* <AsideRoutes /> */}
                      <Wrapper />
                      {/* Sentry successfully implemented */}
                      <Footer />
                    </div>
                  </TourProvider>
                  <Portal id="portal-notification">
                    <ReactNotifications />
                  </Portal>
                  {/* Live Chat Widget - Only shown for regular users */}
                  {/* <LiveChatWidget /> */}
                  {/* PWA Installation Prompt */}
                  <PWAInstallPrompt />
                  {/* Network Status Monitor */}
                  <NetworkStatusMonitor />
                  {/* PWA Update Manager */}
                  <PWAUpdateManager />
                </UserProvider>
              </ThemeProvider>
              </QueryClientProvider>
            </RobustErrorBoundary>
          </ChunkErrorBoundary>
        </AppErrorBoundary>
      </GlobalErrorHandler>
    </UseEffectErrorHandler>
  );
};

// Initialize Sentry for error tracking with Release Health monitoring
Sentry.init({
  dsn: 'https://<EMAIL>/4509399905861632',
  // Enable PII data collection
  sendDefaultPii: true,
  
  // Release tracking (essential for Release Health)
  release: process.env.REACT_APP_VERSION || '1.0.0',
  environment: process.env.NODE_ENV,
  
  // Performance monitoring (helps track user experience)
  tracesSampleRate: 0.2,
  
  // Session tracking is enabled by default in newer versions
  // The React SDK automatically tracks sessions and errors
});

// Export the Sentry-wrapped App component with performance monitoring
export default Sentry.withProfiler(App, { name: 'MainApp' });
