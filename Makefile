
NODE_ENV ?= development
DOCKER_REGISTRY ?= registry.example.com
APP_NAME = luckshack-ecommerce
VERSION ?= $(shell node -p "require('./package.json').version")

SRC_DIR = ./src
BUILD_DIR = ./build
SCRIPTS_DIR = ./scripts

NPM = npm
DOCKER = docker
ESLINT = npx eslint
PRETTIER = npx prettier
STYLELINT = npx stylelint

.PHONY: help install start build serve test lint format clean docker-build docker-push deploy analyze release

help:
	@echo "LuckShack eCommerce Platform Build System"
	@echo ""
	@echo "Usage:"
	@echo "  make <target>"
	@echo ""
	@echo "Targets:"
	@echo "  help            Display this help message"
	@echo "  install         Install dependencies"
	@echo "  start           Start development server"
	@echo "  build           Build for production"
	@echo "  serve           Serve production build locally"
	@echo "  test            Run tests"
	@echo "  lint            Run all linters"
	@echo "  format          Format code"
	@echo "  clean           Clean build artifacts"
	@echo "  docker-build    Build Docker image"
	@echo "  docker-push     Push Docker image to registry"
	@echo "  deploy          Deploy to specified environment"
	@echo "  analyze         Analyze bundle size"
	@echo "  release         Create and tag a new release"

install:
	@echo "Installing dependencies..."
	$(NPM) install

start:
	@echo "Starting development server..."
	$(NPM) run start

build:
	@echo "Building for production..."
	$(NPM) run build

serve:
	@echo "Serving production build locally..."
	$(NPM) run serve

test:
	@echo "Running tests..."
	$(NPM) run test

lint:
	@echo "Running linters..."
	$(ESLINT) '$(SRC_DIR)/**/*.{js,jsx,ts,tsx}'
	$(STYLELINT) '$(SRC_DIR)/**/*.scss'

format:
	@echo "Formatting code..."
	$(NPM) run format

clean:
	@echo "Cleaning build artifacts..."
	rm -rf $(BUILD_DIR)
	rm -rf node_modules/.cache

docker-build:
	@echo "Building Docker image $(DOCKER_REGISTRY)/$(APP_NAME):$(VERSION)..."
	$(DOCKER) build -t $(DOCKER_REGISTRY)/$(APP_NAME):$(VERSION) .
	$(DOCKER) tag $(DOCKER_REGISTRY)/$(APP_NAME):$(VERSION) $(DOCKER_REGISTRY)/$(APP_NAME):latest

docker-push:
	@echo "Pushing Docker image to registry..."
	$(DOCKER) push $(DOCKER_REGISTRY)/$(APP_NAME):$(VERSION)
	$(DOCKER) push $(DOCKER_REGISTRY)/$(APP_NAME):latest

deploy:
	@echo "Deploying to $(NODE_ENV) environment..."
ifeq ($(NODE_ENV), production)
	@echo "Running production deployment..."
	# Add production deployment commands here
else ifeq ($(NODE_ENV), staging)
	@echo "Running staging deployment..."
	# Add staging deployment commands here
else
	@echo "Running development deployment..."
	# Add development deployment commands here
endif

analyze:
	@echo "Analyzing bundle size..."
	ANALYZE=true $(NPM) run build

release:
	@echo "Creating release $(VERSION)..."
	git checkout main
	git pull
	npm version $(VERSION)
	git push
	git push --tags
	@echo "Release $(VERSION) created successfully"
	@echo "Run 'make docker-build' and 'make docker-push' to publish the release"
