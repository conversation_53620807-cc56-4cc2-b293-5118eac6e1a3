import React, { useState } from 'react';
import { LogoutDialog } from '../../presentation/dashboard/adminDash';
import { Menu } from 'lucide-react';
import {
    logoutSvg,
    MainLogo,
    passwordrest,
    reddemrequestSvg,
    requestSvg,
    setting,
    TransactionSvg,
    dashboardSvg
} from '../admin/logo';
import { Close } from '../../../components/icon/material-icons';
interface SidebarProps {
    activeTab: string;
    setActiveTab: (tab: string) => void;
    isOpen: boolean;
    setIsOpen: (isOpen: boolean) => void;
}

interface Tab {
    name: string;
    icon: string;
}

const adminTabs: Tab[] = [
    { name: 'Dashboard', icon: dashboardSvg },
    { name: 'Transactions', icon: TransactionSvg },
    { name: 'Game Requests', icon: requestSvg },
    { name: 'Redeem Requests', icon: reddemrequestSvg },
    { name: 'Password Resets', icon: passwordrest },
    { name: 'Purchase Requests', icon: TransactionSvg },
    { name: 'Settings', icon: setting },
    { name: 'Log-out', icon: logoutSvg },
];

const Sidebar: React.FC<SidebarProps> = ({ activeTab, setActiveTab, isOpen, setIsOpen }) => {
    const [showLogoutDialog, setShowLogoutDialog] = useState(false);

    const handleTabClick = (tabName: string): void => {
        if (tabName === 'Log-out') {
            setShowLogoutDialog(true);
            return;
        }
        setActiveTab(tabName);
        setIsOpen(false);
    };

    return (
        <>
            {/* Mobile Menu Button */}
            <div className="lg:hidden fixed top-1 left-1 z-30">
                <button onClick={() => setIsOpen(!isOpen)} className="p-2 bg-white rounded-lg shadow-md">
                    {isOpen ? (
                        <Close className="w-6 h-6 text-gray-600" />
                    ) : (
                        <Menu className="w-6 h-6 text-gray-600" />
                    )}
                </button>
            </div>

            {/* Sidebar Container */}
            <div
                className={`fixed top-0 left-0 h-full lg:w-80 w-full bg-white z-20 shadow-lg transition-transform ${isOpen ? 'translate-x-0' : '-translate-x-full'
                    } lg:translate-x-0`}
            >
                {/* Logo */}
                <div className="h-20 flex items-center justify-center">
                    <img src={MainLogo} alt="Company Logo" className="h-20 w-auto" />
                </div>

                {/* Navigation Items */}
                <div className="flex flex-col h-[calc(100%-5rem)] overflow-y-auto px-4 py-6">
                    {adminTabs.map((tab) => (
                        <div
                            key={tab.name}
                            className={`flex items-center space-x-3 p-3 rounded-lg cursor-pointer ${activeTab === tab.name ? 'bg-[#495e26] text-white' : ''
                                }`}
                            onClick={() => handleTabClick(tab.name)}
                        >
                            <img
                                src={tab.icon}
                                alt={tab.name}
                                className={`w-6 h-6 ${activeTab === tab.name ? 'brightness-0 invert' : 'brightness-50'
                                    }`}
                            />
                            <span className="font-medium">{tab.name}</span>
                        </div>
                    ))}
                </div>
            </div>

            {/* Overlay for mobile */}
            {isOpen && (
                <div
                    className="fixed inset-0 bg-black bg-opacity-50 z-10 lg:hidden"
                    onClick={() => setIsOpen(false)}
                />
            )}

            {/* Logout Dialog */}
            {showLogoutDialog && (
                <LogoutDialog isOpen={showLogoutDialog} onClose={() => setShowLogoutDialog(false)} />
            )}
        </>
    );
};

export default Sidebar;
