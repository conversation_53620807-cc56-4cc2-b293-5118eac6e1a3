// ===================================================================
// REPORTS API - RTK QUERY IMPLEMENTATION
// ===================================================================

import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import {
  ReportData,
  SystemTotals,
  GameTransaction,
  GameTransactionsPagination,
  AnalyticsResponse,
  DashboardResponse,
  GameTransactionsResponse,
} from '../../pages/presentation/dashboard/types/reports.types';

// Base query with authentication
const baseQuery = fetchBaseQuery({
  baseUrl: process.env.REACT_APP_API_URL,
  prepareHeaders: (headers) => {
    const token = localStorage.getItem('Token');
    if (token) {
      headers.set('authorization', `Bearer ${token}`);
    }
    headers.set('accept', 'application/json');
    headers.set('content-type', 'application/json');
    return headers;
  },
});

// Reports API slice
export const reportsApi = createApi({
  reducerPath: 'reportsApi',
  baseQuery,
  tagTypes: ['SystemTotals', 'ReportData', 'GameTransactions'],
  endpoints: (builder) => ({
    getSystemTotals: builder.query<SystemTotals, void>({
      query: () => '/reports/dashboard',
      transformResponse: (response: DashboardResponse) => {
        if (response?.success && response?.data) {
          return {
            totalSweepsCoins: response.data.totalSweepsCoins || 0,
            totalGoldCoins: response.data.totalGoldCoins || 0,
            totalGamePurchases: response.data.totalPurchases || 0,
            totalCashRedeems: response.data.totalRedeems || 0,
          };
        }
        return {
          totalSweepsCoins: 0,
          totalGoldCoins: 0,
          totalGamePurchases: 0,
          totalCashRedeems: 0,
        };
      },
      providesTags: ['SystemTotals'],
    }),
    getPurchaseReport: builder.query<GameTransaction[], void>({
      query: () => '/order-purchase',
      transformResponse: (response: any) => {
        if (response?.success && response?.data) {
          return response.data;
        }
        return [];
      },
      providesTags: ['GameTransactions'],
    }),

    getRedeemReport: builder.query<GameTransaction[], void>({
      query: () => '/order-redeemed',
      transformResponse: (response: any) => {
        if (response?.success && response?.data) {
          return response.data;
        }
        return [];
      },
      providesTags: ['GameTransactions'],
    }),

    getGameReport: builder.query<
      {
        data: GameTransaction[];
        totalCount: number;
        totalAmount: number;
        page: number;
        limit: number;
      },
      {
        page?: number;
        limit?: number;
        startDate?: string;
        endDate?: string;
        gameName?: string;
        transactionType?: string;
        status?: string;
        minAmount?: string;
        maxAmount?: string;
      }
    >({
      query: ({ page = 1, limit = 10, startDate, endDate, gameName, transactionType, status, minAmount, maxAmount }) => {
        const params = new URLSearchParams();
        params.append('page', page.toString());
        params.append('limit', limit.toString());

        if (startDate) params.append('startDate', startDate);
        if (endDate) params.append('endDate', endDate);
        if (gameName) params.append('gameName', gameName);
        if (transactionType) params.append('transactionType', transactionType);
        if (status) params.append('status', status);
        if (minAmount) params.append('minAmount', minAmount);
        if (maxAmount) params.append('maxAmount', maxAmount);

        return `/game-report?${params.toString()}`;
      },
      transformResponse: (response: any) => {
        if (response?.success) {
          return {
            data: response.data || [],
            totalCount: response.totalCount || 0,
            totalAmount: response.totalAmount || 0,
            page: response.page || 1,
            limit: response.limit || 10,
          };
        }
        return {
          data: [],
          totalCount: 0,
          totalAmount: 0,
          page: 1,
          limit: 10,
        };
      },
      providesTags: ['GameTransactions'],
    }),
    getAnalyticsData: builder.query<
      ReportData[],
      {
        groupBy: 'daily' | 'weekly' | 'monthly';
        startDate?: string;
        endDate?: string;
        pageNumber?: number;
        limit?: number;
      }
    >({
      query: ({ groupBy, startDate, endDate, pageNumber = 1, limit = 50 }) => {
        const params = new URLSearchParams();
        params.append('pageNumber', pageNumber.toString());
        params.append('limit', limit.toString());
        params.append('groupBy', groupBy);

        if (startDate) {
          params.append('startDate', startDate);
        }
        if (endDate) {
          params.append('endDate', endDate);
        }

        return `/analytics?${params.toString()}`;
      },
      transformResponse: (response: AnalyticsResponse) => {
        if (response?.success && response?.data) {
          return response.data;
        }
        return [];
      },
      providesTags: ['ReportData'],
    }),

    getGameTransactions: builder.query<
      {
        data: GameTransaction[];
        pagination: GameTransactionsPagination;
        summary?: {
          totalTransactions: number;
          totalPurchaseAmount: number;
          totalRedeemAmount: number;
        };
      },
      {
        page?: number;
        limit?: number;
        search?: string;
        gameName?: string;
        userSearch?: string;
        transactionType?: string;
        status?: string;
        startDate?: string;
        endDate?: string;
        minAmount?: string;
        maxAmount?: string;
      }
    >({
      query: ({
        page = 1,
        limit = 25,
        search,
        gameName,
        userSearch,
        transactionType,
        status,
        startDate,
        endDate,
        minAmount,
        maxAmount,
      }) => {
        const params = new URLSearchParams();
        params.append('page', page.toString());
        params.append('limit', limit.toString());

        // Add filters if provided
        if (search?.trim()) {
          params.append('search', search.trim());
        }
        if (gameName?.trim()) {
          params.append('gameName', gameName.trim());
        }
        if (userSearch?.trim()) {
          params.append('userSearch', userSearch.trim());
        }
        if (transactionType) {
          params.append('transactionType', transactionType);
        }
        if (status) {
          params.append('status', status);
        }
        if (startDate) {
          params.append('startDate', startDate);
        }
        if (endDate) {
          params.append('endDate', endDate);
        }
        if (minAmount) {
          params.append('minAmount', minAmount);
        }
        if (maxAmount) {
          params.append('maxAmount', maxAmount);
        }

        return `/api/game-transactions?${params.toString()}`;
      },
      transformResponse: (response: GameTransactionsResponse) => {
        if (response?.success && response?.data) {
          return {
            data: response.data,
            pagination: response.pagination || {
              currentPage: 1,
              totalItems: 0,
              hasMore: false,
            },
            summary: response.summary,
          };
        }
        return {
          data: [],
          pagination: {
            currentPage: 1,
            totalItems: 0,
            hasMore: false,
          },
        };
      },
      providesTags: ['GameTransactions'],
      // Enable manual cache management for pagination
      serializeQueryArgs: ({ queryArgs }) => {
        const { page, ...otherArgs } = queryArgs;
        return otherArgs;
      },
      merge: (currentCache, newItems, { arg }) => {
        if (arg.page === 1) {
          // Replace cache for first page
          return newItems;
        } else {
          // Append to cache for subsequent pages
          return {
            ...newItems,
            data: [...currentCache.data, ...newItems.data],
          };
        }
      },
      forceRefetch({ currentArg, previousArg }) {
        // Force refetch if filters change
        const { page: currentPage, ...currentFilters } = currentArg || {};
        const { page: previousPage, ...previousFilters } = previousArg || {};

        return JSON.stringify(currentFilters) !== JSON.stringify(previousFilters);
      },
    }),
  }),
});

// Export hooks for use in components
export const {
  useGetSystemTotalsQuery,
  useGetAnalyticsDataQuery,
  useGetGameTransactionsQuery,
  useGetRedeemReportQuery,
  useGetPurchaseReportQuery,
  useGetGameReportQuery,
} = reportsApi;
