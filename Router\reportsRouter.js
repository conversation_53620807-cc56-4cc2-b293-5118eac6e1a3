const express = require('express');
const router = express.Router();
const auth = require('../auth/middleware');
const reportsController = require('../Controller/reportsController');

/**
 * Reports Router
 * Handles all analytics and reporting routes
 * All routes require authentication and proper role permissions
 */

/**
 * @route GET /reports/dashboard
 * @desc Get comprehensive dashboard analytics
 * @access Private (Admin, Manage roles only)
 * @query {string} startDate - Start date (YYYY-MM-DD format, optional)
 * @query {string} endDate - End date (YYYY-MM-DD format, optional)
 */
router.get('/dashboard', auth.verifyToken, reportsController.getDashboardAnalytics.bind(reportsController));

/**
 * @route GET /reports/purchases
 * @desc Get purchases analytics with filtering and grouping (card/crypto payments)
 * @access Private (Admin, Manage roles only)
 * @query {string} startDate - Start date (YYYY-MM-DD format, optional)
 * @query {string} endDate - End date (YYYY-MM-DD format, optional)
 * @query {string} groupBy - Group by option: 'date', 'game', 'user' (default: 'date')
 * @query {number} page - Page number for pagination (default: 1)
 * @query {number} limit - Items per page (default: 50, max: 100)
 */
router.get('/purchases', auth.verifyToken, reportsController.getPurchasesReport.bind(reportsController));

/**
 * @route GET /reports/redeems
 * @desc Get redeems analytics with filtering and grouping
 * @access Private (Admin, Manage roles only)
 * @query {string} startDate - Start date (YYYY-MM-DD format, optional)
 * @query {string} endDate - End date (YYYY-MM-DD format, optional)
 * @query {string} groupBy - Group by option: 'date', 'game', 'user' (default: 'date')
 * @query {number} page - Page number for pagination (default: 1)
 * @query {number} limit - Items per page (default: 50, max: 100)
 */
router.get('/redeems', auth.verifyToken, reportsController.getRedeemsReport.bind(reportsController));

/**
 * @route GET /reports/wallet-balances
 * @desc Get comprehensive wallet balances report
 * @access Private (Admin, Manage roles only)
 */
router.get('/wallet-balances', auth.verifyToken, reportsController.getWalletBalancesReport.bind(reportsController));

/**
 * Error handling middleware for reports routes
 */
router.use((error, req, res, next) => {
  console.error('Reports Router Error:', error);

  // Handle validation errors
  if (error.name === 'ValidationError') {
    return res.status(400).json({
      success: false,
      message: 'Validation error',
      error: error.message
    });
  }

  // Handle authentication errors
  if (error.name === 'UnauthorizedError' || error.message === 'jwt malformed') {
    return res.status(401).json({
      success: false,
      message: 'Authentication required',
      error: 'UNAUTHORIZED'
    });
  }

  // Handle permission errors
  if (error.message === 'INSUFFICIENT_PERMISSIONS') {
    return res.status(403).json({
      success: false,
      message: 'Access denied. Reports access requires Manage or Admin role.',
      error: 'INSUFFICIENT_PERMISSIONS'
    });
  }

  // Handle database errors
  if (error.name === 'MongoError' || error.name === 'MongooseError') {
    return res.status(500).json({
      success: false,
      message: 'Database error occurred',
      error: process.env.NODE_ENV === 'development' ? error.message : 'DATABASE_ERROR'
    });
  }

  // Default error response
  res.status(500).json({
    success: false,
    message: 'Internal server error',
    error: process.env.NODE_ENV === 'development' ? error.message : 'INTERNAL_SERVER_ERROR'
  });
});

module.exports = router;
