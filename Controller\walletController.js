const WalletModel = require("../Model/userWalletModal");
const mongoose = require("mongoose"); // mongoose needs to be required

class WalletController {
  async createWallet(req, res) {
    try {
      const { userId } = req.params;
      console.log("Create wallet data:", { userId });

      if (!userId) {
        return res.status(400).send({ error: "UserId is required" });
      }

      const wallet = await WalletModel.createWallet(userId);
      res.status(200).send({ data: wallet, error: "" });
    } catch (error) {
      console.log("Error in creating wallet:", error);
      res.status(500).send({ error: error.message });
    }
  }

  async checkWallet(req, res) {
    try {
      const { userId } = req.params;
      console.log("Check wallet data - userId:", userId);

      if (!userId) {
        return res.status(400).send({ error: "UserId is required" });
      }

      // Convert string userId to ObjectId
      let userObjectId;
      try {
        userObjectId = mongoose.Types.ObjectId.isValid(userId)
          ? new mongoose.Types.ObjectId(userId)
          : userId;
      } catch (error) {
        console.error("Error converting userId to ObjectId:", error);
        return res.status(400).send({ error: "Invalid userId format" });
      }

      const wallet = await WalletModel.findOne({ userId: userObjectId });
      console.log("Found wallet:", wallet);

      if (!wallet) {
        return res.status(404).send({ error: "Wallet not found" });
      }

      const response = {
        data: {
          balance: {
            swipeCoins: wallet.balance.swipeCoins,
            unplayedSwipeCoins: wallet.balance.unplayedSwipeCoins || 0,
            redeemableSwipeCoins: wallet.balance.redeemableSwipeCoins || 0,
            goldCoins: wallet.balance.goldCoins,
          },
        },
        error: "",
      };
      console.log("Sending wallet response:", response);
      res.status(200).send(response);
    } catch (error) {
      console.log("Error in checking wallet:", error);
      res.status(500).send({ error: error.message });
    }
  }

  async addSwipeCoins(req, res) {
    try {
      const {
        userId,
        amount,
        paymentMethod = "Crypto",
        status = "Processed",
        description = "",
      } = req.body;
      console.log("Add Swipe Coins data:", {
        userId,
        amount,
        paymentMethod,
        status,
        description,
      });

      if (!userId || !amount) {
        return res
          .status(400)
          .send({ error: "UserId and amount are required" });
      }

      // Add swipe coins
      const updatedWallet = await WalletModel.addSwipeCoins(userId, amount);
      await WalletModel.addTransaction(
        userId,
        "credit",
        "swipeCoin",
        amount,
        description,
        paymentMethod,
        status
      );

      // If this is a successful purchase, also add the same amount to gold coins (but don't log in transaction history)
      if (status === "Processed" || status === "Success") {
        await WalletModel.addGoldCoins(userId, amount);
        console.log(
          `Added ${amount} gold coins as bonus for swipe coin purchase`
        );
      }

      res.status(200).send({ data: updatedWallet, error: "" });
    } catch (error) {
      console.log("Error in adding swipe coins:", error);
      res.status(500).send({ error: error.message });
    }
  }

  async addSwipeCoinsManual(req, res) {
    try {
      const {
        userId,
        amount,
        status = "Completed",
        paymentMethod = "Manual",
        description,
      } = req.body;
      console.log("Add Gold Coins data:", {
        userId,
        amount,
        paymentMethod,
        status,
        description,
      });

      if (!userId || !amount) {
        return res
          .status(400)
          .send({ error: "UserId and amount are required" });
      }

      // Get user details to include email
      const UsersModel = require("../Model/usersModel");
      let userEmail = "";
      try {
        const userDetails = await UsersModel.findById(userId);
        if (userDetails && userDetails.email) {
          userEmail = userDetails.email;
        }
      } catch (userError) {
        console.log("Error fetching user details:", userError);
        // Continue even if we can't get the email
      }

      // Add gold coins
      await WalletModel.addGoldCoins(userId, amount);
      const updatedWallet = await WalletModel.addSwipeCoins(userId, amount);

      // Log in order database
      const OrderModel = require("../Model/orderModel");
      const orderData = {
        userId: userId,
        status: status,
        price: amount, // Using amount as price
        quantity: 1,
        paymentMethod: description || "Manual", // Use reason as payment method
        item_name: "Gold Coins",
        dateTime: new Date(),
        paymentReferenceNo: `GC-${Date.now()}`,
        paymentId: `MANUAL-${Date.now()}`,
        system: req.user._id,
        emailMatched: true,
        email: userEmail // Include user's email
      };
      
      // Add order record
      const orderResult = await OrderModel.add(orderData);
      console.log(`Added order record for gold coins: ${orderResult._id}`);

      res.status(200).send({ 
        data: {
          wallet: updatedWallet,
          order: orderResult
        }, 
        error: "" 
      });
    } catch (error) {
      console.log("Error in adding gold coins:", error);
      res.status(500).send({ error: error.message });
    }
  }


  async addGoldCoins(req, res) {
    try {
      const {
        userId,
        amount,
        paymentMethod = "Crypto",
        status = "Processed",
        description = "",
      } = req.body;
      console.log("Add Gold Coins data:", {
        userId,
        amount,
        paymentMethod,
        status,
        description,
      });

      if (!userId || !amount) {
        return res
          .status(400)
          .send({ error: "UserId and amount are required" });
      }

      // Get user details to include email
      const UsersModel = require("../Model/usersModel");
      let userEmail = "";
      try {
        const userDetails = await UsersModel.findById(userId);
        if (userDetails && userDetails.email) {
          userEmail = userDetails.email;
        }
      } catch (userError) {
        console.log("Error fetching user details:", userError);
        // Continue even if we can't get the email
      }

      // Add gold coins
      const updatedWallet = await WalletModel.addGoldCoins(userId, amount);

      // If this is a successful purchase, also add the same amount to swipe coins and log the transaction
      if (status === "Processed" || status === "Success") {
        await WalletModel.addSwipeCoins(userId, amount);
        await WalletModel.addTransaction(
          userId,
          "credit",
          "swipeCoin",
          amount,
          `Bonus swipe coins from gold coin purchase: ${description}`,
          paymentMethod,
          status
        );
        console.log(
          `Added ${amount} swipe coins as bonus for gold coin purchase`
        );
      }
      
      // Log in order database
      const OrderModel = require("../Model/orderModel");
      const orderData = {
        userId: userId,
        status: status,
        price: amount, // Using amount as price
        quantity: 1,
        paymentMethod: description || "Manual", // Use reason as payment method
        item_name: "Gold Coins",
        dateTime: new Date(),
        paymentReferenceNo: `GC-${Date.now()}`,
        paymentId: `MANUAL-${Date.now()}`,
        system: req.user._id,
        emailMatched: true,
        email: userEmail // Include user's email
      };
      
      // Add order record
      const orderResult = await OrderModel.add(orderData);
      console.log(`Added order record for gold coins: ${orderResult._id}`);

      res.status(200).send({ 
        data: {
          wallet: updatedWallet,
          order: orderResult
        }, 
        error: "" 
      });
    } catch (error) {
      console.log("Error in adding gold coins:", error);
      res.status(500).send({ error: error.message });
    }
  }

  async removeSwipeCoins(req, res) {
    try {
      const {
        userId,
        amount,
        paymentMethod = "Bank Transfer",
        status = "Processed",
        description = "",
      } = req.body;
      console.log("Remove Swipe Coins data:", {
        userId,
        amount,
        paymentMethod,
        status,
        description,
      });

      if (!userId || !amount) {
        return res
          .status(400)
          .send({ error: "UserId and amount are required" });
      }

      const updatedWallet = await WalletModel.removeSwipeCoins(userId, amount);
      await WalletModel.addTransaction(
        userId,
        "debit",
        "swipeCoin",
        amount,
        description,
        paymentMethod,
        status
      );

      res.status(200).send({ data: updatedWallet, error: "" });
    } catch (error) {
      console.log("Error in removing swipe coins:", error);
      res.status(500).send({ error: error.message });
    }
  }

  async removeGoldCoins(req, res) {
    try {
      const {
        userId,
        amount,
        paymentMethod = "Bank Transfer",
        status = "Processed",
        description = "",
      } = req.body;
      console.log("Remove Gold Coins data:", {
        userId,
        amount,
        paymentMethod,
        status,
        description,
      });

      if (!userId || !amount) {
        return res
          .status(400)
          .send({ error: "UserId and amount are required" });
      }

      const updatedWallet = await WalletModel.removeGoldCoins(userId, amount);
      // Don't log gold coin transactions in the history

      res.status(200).send({ data: updatedWallet, error: "" });
    } catch (error) {
      console.log("Error in removing gold coins:", error);
      res.status(500).send({ error: error.message });
    }
  }

  async getBalance(req, res) {
    try {
      const { userId } = req.params;
      console.log("Get balance data:", { userId });

      if (!userId) {
        return res.status(400).send({ error: "UserId is required" });
      }

      const balance = await WalletModel.getBalance(userId);
      res.status(200).send({ data: balance, error: "" });
    } catch (error) {
      console.log("Error in getting balance:", error);
      res.status(500).send({ error: error.message });
    }
  }

  async getTransactions(req, res) {
    try {
      const { userId } = req.params;
      console.log("Get transactions data:", { userId });

      if (!userId) {
        return res.status(400).send({ error: "UserId is required" });
      }

      const transactions = await WalletModel.getTransactions(userId);
      // Filter to only show sweep coin transactions
      const sweepCoinTransactions = transactions.filter(
        (tx) => tx.coinType === "swipeCoin"
      );

      res.status(200).send({
        data: sweepCoinTransactions,
        error: "",
      });
    } catch (error) {
      console.log("Error in getting transactions:", error);
      res.status(500).send({ error: error.message });
    }
  }

  async handleSpinResult(req, res) {
    try {
      const { userId } = req.params;
      const { spinCost, winAmount } = req.body;
      console.log("Handle spin result:", { userId, spinCost, winAmount });

      if (!userId) {
        return res.status(400).send({ error: "UserId is required" });
      }

      // Convert string userId to ObjectId
      let userObjectId;
      try {
        userObjectId = mongoose.Types.ObjectId.isValid(userId)
          ? new mongoose.Types.ObjectId(userId)
          : userId;
      } catch (error) {
        console.error("Error converting userId to ObjectId:", error);
        return res.status(400).send({ error: "Invalid userId format" });
      }

      // First, check if user has enough gold coins
      const wallet = await WalletModel.findOne({ userId: userObjectId });
      if (!wallet) {
        return res.status(404).send({ error: "Wallet not found" });
      }

      if (wallet.balance.goldCoins < spinCost) {
        return res.status(400).send({ error: "Insufficient gold coins" });
      }

      // Deduct spin cost from gold coins
      const updatedWallet = await WalletModel.removeGoldCoins(
        userObjectId,
        spinCost
      );
      console.log("After deducting spin cost:", updatedWallet);

      // Also deduct from swipe coins and log as a game transaction
      await WalletModel.addTransaction(
        userObjectId,
        "debit",
        "goldCoin",
        spinCost,
        "Game: Spin cost",
        "Game",
        "Processed"
      );

      // Add winnings to gold coins if any
      let finalWallet = updatedWallet;
      if (winAmount > 0) {
        finalWallet = await WalletModel.addGoldCoins(userObjectId, winAmount);
        console.log("After adding winnings:", finalWallet);       
      }

      res.status(200).send({
        data: {
          wallet: finalWallet,
          spinResult: {
            cost: spinCost,
            win: winAmount,
          },
        },
        error: "",
      });
    } catch (error) {
      console.log("Error in handling spin result:", error);
      res.status(500).send({ error: error.message });
    }
  }

  async loadCoinsToGame(req, res) {
    try {
      const { userId, gameId, amount } = req.body;
      console.log("Load coins to game data:", { userId, gameId, amount });

      if (!userId || !gameId || !amount) {
        return res.status(400).send({
          error: "UserId, gameId, and amount are required",
        });
      }

      // Check if amount is a positive number
      if (isNaN(amount) || amount <= 0) {
        return res.status(400).send({
          error: "Amount must be a positive number",
        });
      }

      // Load coins to game
      const updatedWallet = await WalletModel.loadCoinsToGame(
        userId,
        Number(amount)
      );

      // Add transaction record
      await WalletModel.addTransaction(
        userId,
        "debit",
        "swipeCoin",
        Number(amount),
        `Loaded ${amount} Sweep Coins to game ${gameId}`,
        "Game Load",
        "Processed"
      );

      res.status(200).send({
        data: {
          wallet: updatedWallet,
          message: `Successfully loaded ${amount} Sweep Coins to game`,
        },
        error: "",
      });
    } catch (error) {
      console.log("Error in loading coins to game:", error);
      res.status(500).send({ error: error.message });
    }
  }

  async requestRedemption(req, res) {
    try {
      const { userId, gameId, amount } = req.body;
      console.log("Request redemption data:", { userId, gameId, amount });

      if (!userId || !gameId || !amount) {
        return res.status(400).send({
          error: "UserId, gameId, and amount are required",
        });
      }

      // Check if amount is a positive number
      if (isNaN(amount) || amount <= 0) {
        return res.status(400).send({
          error: "Amount must be a positive number",
        });
      }

      // Add transaction record with Pending status
      await WalletModel.addTransaction(
        userId,
        "credit",
        "swipeCoin",
        Number(amount),
        `Redemption request of ${amount} Sweep Coins from game ${gameId}`,
        "Game Redemption",
        "Pending"
      );

      res.status(200).send({
        data: {
          message: `Redemption request of ${amount} Sweep Coins submitted successfully`,
        },
        error: "",
      });
    } catch (error) {
      console.log("Error in requesting redemption:", error);
      res.status(500).send({ error: error.message });
    }
  }

  async approveRedemption(req, res) {
    try {
      const { transactionId, userId, amount } = req.body;
      console.log("Approve redemption data:", {
        transactionId,
        userId,
        amount,
      });

      if (!transactionId || !userId || !amount) {
        return res.status(400).send({
          error: "TransactionId, userId, and amount are required",
        });
      }

      // Check if amount is a positive number
      if (isNaN(amount) || amount <= 0) {
        return res.status(400).send({
          error: "Amount must be a positive number",
        });
      }

      // Update transaction status to Processed
      const transaction = await WalletModel.updateTransactionStatus(
        transactionId,
        "Processed"
      );

      if (!transaction) {
        return res.status(404).send({ error: "Transaction not found" });
      }

      // Add redeemed coins to user's redeemable balance
      const updatedWallet = await WalletModel.addRedeemedCoins(
        userId,
        Number(amount)
      );

      res.status(200).send({
        data: {
          wallet: updatedWallet,
          message: `Successfully approved redemption of ${amount} Sweep Coins`,
        },
        error: "",
      });
    } catch (error) {
      console.log("Error in approving redemption:", error);
      res.status(500).send({ error: error.message });
    }
  }
}

module.exports = new WalletController();
