import { api } from './apiSlice';
import { TAG_TYPES } from './apiConfig';

export const invalidateCache = {
  entity: (type: keyof typeof TAG_TYPES, id: string | number) => {
    return api.util.invalidateTags([{ type, id } as any]);
  },
  
  collection: (type: keyof typeof TAG_TYPES) => {
    return api.util.invalidateTags([{ type, id: 'LIST' } as any]);
  },
  
  collections: (types: Array<keyof typeof TAG_TYPES>) => {
    return api.util.invalidateTags(types.map(type => ({ type, id: 'LIST' } as any)));
  },
  
  all: () => {
    return api.util.invalidateTags(
      Object.values(TAG_TYPES).map(type => ({ type, id: 'LIST' } as any))
    );
  },
  
  byFilter: (type: keyof typeof TAG_TYPES, filterPattern: string) => {
    return api.util.invalidateTags([{ type, id: `filter:${filterPattern}` } as any]);
  }
};

export const preloadData = {
  userOrders: (userId: string, page = 1, limit = 10) => {
    return api.util.prefetch('getUserOrders' as any, { userId, page, limit }, { force: false });
  },
  
  filteredOrders: (userId: string, paymentMethod: string, page = 1, limit = 10) => {
    return api.util.prefetch(
      'getOrdersWithFilter' as any, 
      { userId, paymentMethod, page, limit }, 
      { force: false }
    );
  },
  
  cashAppList: () => {
    return api.util.prefetch('getCashAppList' as any, undefined, { force: false });
  },
  
  analytics: (params: Record<string, any>) => {
    return api.util.prefetch('getAnalytics' as any, params, { force: false });
  }
};

export const useCacheManagement = () => {
  return {
    invalidateCache,
    preloadData,
    
    resetApiState: () => {
      return api.util.resetApiState();
    },
    
    updateCacheData: <T>(
      endpointName: string, 
      args: any, 
      updateFn: (draft: T) => void
    ) => {
      return api.util.updateQueryData(endpointName as any, args, updateFn as any);
    }
  };
};

export default useCacheManagement;
