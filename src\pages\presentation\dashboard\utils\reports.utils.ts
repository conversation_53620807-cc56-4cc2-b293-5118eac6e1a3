// ===================================================================
// REPORTS DASHBOARD UTILITIES
// ===================================================================

import { ReportData } from '../types/reports.types';

/**
 * Format a date string to a human-readable format
 * @param dateString - The date string to format
 * @returns Formatted date string or 'N/A' if invalid
 */
export const formatDate = (dateString: string): string => {
  if (!dateString) return 'N/A';

  try {
    // Use optional chaining to prevent runtime errors
    const date = new Date(dateString);

    // Check if the date is valid before formatting
    if (isNaN(date?.getTime())) {
      return 'Invalid Date';
    }

    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  } catch (error) {
    // Log the error but provide a fallback for the UI
    console.error('Error formatting date:', error);
    return 'Invalid Date';
  }
};

/**
 * Export report data to CSV file
 * Industry best practices:
 * - Proper error handling with user feedback
 * - Proper CSV escaping to handle special characters
 * - Type safety with optional chaining
 * - Descriptive filename with date
 */
export const exportToCSV = (
  reportData: ReportData[], 
  activeTab: string
): void => {
  if (!reportData?.length) {
    alert('No data available to export');
    return;
  }

  try {
    // Define CSV headers
    const headers = [
      'Date',
      'Purchases',
      'Redeems',
      'Total Amount',
      'User Count'
    ];

    // Helper function to escape CSV values properly
    const escapeCSV = (value: string | number): string => {
      const stringValue = String(value);
      // If the value contains commas, quotes, or newlines, wrap it in quotes and escape any quotes
      if (/[",\n\r]/.test(stringValue)) {
        return `"${stringValue.replace(/"/g, '""')}"`;
      }
      return stringValue;
    };

    // Convert data to CSV format
    const csvRows: string[] = [];

    // Add headers
    csvRows.push(headers.map(escapeCSV).join(','));

    // Add data rows with proper escaping
    reportData.forEach(item => {
      const row = [
        escapeCSV(formatDate(item?.date || '')),
        escapeCSV(item?.purchases || 0),
        escapeCSV(item?.redeems || 0),
        escapeCSV(`$${(item?.totalAmount || 0).toFixed(2)}`),
        escapeCSV(item?.userCount || 0)
      ];

      csvRows.push(row.join(','));
    });

    // Create CSV content
    const csvContent = csvRows.join('\n');

    // Create a Blob with the CSV content
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });

    // Generate a descriptive filename with date and time
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0];
    const filename = `reports_${activeTab}_${timestamp}.csv`;

    // Use the browser's download API
    // TypeScript type assertion for IE11 & Edge support
    const nav = window.navigator as any;
    if (nav.msSaveOrOpenBlob) {
      // IE11 & Edge
      nav.msSaveOrOpenBlob(blob, filename);
    } else {
      // Modern browsers
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);

      // Set link properties
      link.setAttribute('href', url);
      link.setAttribute('download', filename);
      link.style.visibility = 'hidden';

      // Append link to document, click it, and remove it
      document.body.appendChild(link);
      link.click();

      // Clean up
      setTimeout(() => {
        document.body.removeChild(link);
        URL.revokeObjectURL(url); // Free up memory by revoking the object URL
      }, 100);
    }
  } catch (error) {
    console.error('Error exporting CSV:', error);
    alert('Failed to export data. Please try again.');
  }
};

/**
 * Calculate summary statistics from report data
 */
export const calculateSummaryStats = (reportData: ReportData[]) => {
  return {
    totalPurchases: reportData.reduce((sum, item) => sum + item.purchases, 0),
    totalRedeems: reportData.reduce((sum, item) => sum + item.redeems, 0),
    totalAmount: reportData.reduce((sum, item) => sum + item.totalAmount, 0),
    totalUsers: reportData.reduce((sum, item) => sum + item.userCount, 0),
    averageAmount: reportData.length > 0 
      ? reportData.reduce((sum, item) => sum + item.totalAmount, 0) / reportData.length 
      : 0
  };
};

/**
 * Format currency values consistently
 */
export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount);
};

/**
 * Format large numbers with commas
 */
export const formatNumber = (num: number): string => {
  return new Intl.NumberFormat('en-US').format(num);
};
