import React, { useEffect } from 'react';
import { logErrorToService } from '../../utils/errorHandling';

/**
 * GlobalErrorHandler component that sets up global error handling
 * to catch uncaught errors that might escape error boundaries
 */
const GlobalErrorHandler: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  useEffect(() => {
    // Store original console methods
    const originalConsoleError = console.error;
    const originalConsoleWarn = console.warn;
    const originalConsoleLog = console.log;

    // Function to handle uncaught errors
    const handleUncaughtError = (event: ErrorEvent) => {
      // Prevent the error from showing in the UI
      event.preventDefault();

      // Check if this is a simulated error from our test components
      const isSimulatedError =
        event.error &&
        typeof event.error.message === 'string' &&
        (event.error.message.includes('Simulated error') ||
         event.error.message.includes('Counter reached 5'));

      if (isSimulatedError && process.env.NODE_ENV !== 'development') {
        // In production, don't show simulated errors to users
        // Just log them silently for monitoring
        logErrorToService(event.error);
      } else {
        // For real errors or in development mode, log them
        console.error('Global error handler caught:', event.error);
        // You could send this to your error tracking service
        logErrorToService(event.error);
      }
    };

    // Function to handle unhandled promise rejections
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      // Prevent the error from showing in the UI
      event.preventDefault();

      // Check if this is a simulated error
      const isSimulatedError =
        event.reason &&
        typeof event.reason.message === 'string' &&
        (event.reason.message.includes('Simulated error') ||
         event.reason.message.includes('Counter reached 5'));

      if (isSimulatedError && process.env.NODE_ENV !== 'development') {
        // In production, don't show simulated errors to users
        logErrorToService(event.reason);
      } else {
        // For real errors or in development mode, log them
        console.error('Global error handler caught unhandled rejection:', event.reason);
        logErrorToService(event.reason);
      }
    };

    // Override console.error in production to prevent React from showing the error overlay
    if (process.env.NODE_ENV !== 'development') {
      console.error = (...args) => {
        // Check if this is a React error or a simulated error
        const isReactError = args.some(arg =>
          typeof arg === 'string' &&
          (arg.includes('React will try to recreate this component tree') ||
           arg.includes('The above error occurred in the') ||
           arg.includes('Consider adding an error boundary'))
        );

        const isSimulatedError = args.some(arg =>
          arg instanceof Error &&
          typeof arg.message === 'string' &&
          (arg.message.includes('Simulated error') ||
           arg.message.includes('Counter reached 5'))
        );

        // If it's a React error or simulated error, suppress it in production
        if (isReactError || isSimulatedError) {
          // Still log it for monitoring, but in a way that doesn't trigger the overlay
          // and isn't visible to users
          logErrorToService(args[0]);
          return;
        }

        // Otherwise, pass through to original console.error
        originalConsoleError.apply(console, args);
      };

      // Also override console.warn for similar reasons
      console.warn = (...args) => {
        // Check if this is related to our simulated errors
        const isSimulatedWarning = args.some(arg =>
          typeof arg === 'string' &&
          (arg.includes('Simulated error') ||
           arg.includes('Counter reached 5'))
        );

        if (isSimulatedWarning) {
          // Suppress simulated warnings in production
          return;
        }

        // Pass through other warnings
        originalConsoleWarn.apply(console, args);
      };
    }

    // Add event listeners for uncaught errors and unhandled rejections
    window.addEventListener('error', handleUncaughtError, { capture: true });
    window.addEventListener('unhandledrejection', handleUnhandledRejection, { capture: true });

    // Clean up event listeners and restore console methods when component unmounts
    return () => {
      window.removeEventListener('error', handleUncaughtError, { capture: true });
      window.removeEventListener('unhandledrejection', handleUnhandledRejection, { capture: true });
      console.error = originalConsoleError;
      console.warn = originalConsoleWarn;
      console.log = originalConsoleLog;
    };
  }, []);

  return <>{children}</>;
};

export default GlobalErrorHandler;
