import React, { SVGProps } from 'react';

const SvgSignalWifiStatusbarConnectedNoInternet1 = (props: SVGProps<SVGSVGElement>) => {
  return (
    <svg viewBox="0 0 24 24" fill="currentColor" className="svg-icon" {...props}>
      <path d="M0 0h24v24H0V0z" fill="none" />
      <path
        d="M17 14.76V8h5.92C19.97 5.51 16.16 4 12 4 7.31 4 3.07 5.9 0 8.98l6.35 6.36A7.95 7.95 0 0112 13c1.89 0 3.63.66 5 1.76z"
        fillOpacity={0.3}
      />
      <path d="M6.35 15.34L12 21l5-5.01v-1.23A7.963 7.963 0 0012 13c-2.21 0-4.2.89-5.65 2.34zM19 18h2v2h-2zM19 10h2v6h-2z" />
    </svg>
  );
};

export default SvgSignalWifiStatusbarConnectedNoInternet1;
