const jwt = require('jsonwebtoken');
require('dotenv').config();

// Generate a test token for testing the reports endpoints
function generateTestToken() {
  const payload = {
    _id: '674371996f19c19dba73a77',
    email: '<EMAIL>',
    role: 'Admin',
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + (60 * 60 * 24) // 24 hours
  };

  const token = jwt.sign(payload, process.env.TOKEN_KEY);
  console.log('Generated test token:');
  console.log(token);
  console.log('\nToken payload:');
  console.log(payload);
  
  return token;
}

// Test token verification
function verifyTestToken(token) {
  try {
    const decoded = jwt.verify(token, process.env.TOKEN_KEY);
    console.log('\nToken verification successful:');
    console.log(decoded);
    return decoded;
  } catch (error) {
    console.log('\nToken verification failed:');
    console.log(error.message);
    return null;
  }
}

if (require.main === module) {
  console.log('🔑 Generating test token...\n');
  const token = generateTestToken();
  verifyTestToken(token);
}

module.exports = { generateTestToken, verifyTestToken };
