import React, { useState } from 'react';
import billingSvg from '../../../assets/billingsvg.svg';

interface CardDetails {
  firstName: string;
  lastName: string;
  cardNumber: string;
  expirationDate: string;
}

interface BillingDetails {
  addressLine1: string;
  addressLine2: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
}

const BillingAddress = () => {
  const [cardDetails, setCardDetails] = useState<CardDetails>({
    firstName: '',
    lastName: '',
    cardNumber: '',
    expirationDate: '',
  });

  const [billingDetails, setBillingDetails] = useState<BillingDetails>({
    addressLine1: '',
    addressLine2: '',
    city: '',
    state: '',
    zipCode: '',
    country: '',
  });

  const handleCardSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
  };

  const handleBillingSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
  };

  const handleCardChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCardDetails({ ...cardDetails, [e.target.name]: e.target.value });
  };

  const handleBillingChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setBillingDetails({ ...billingDetails, [e.target.name]: e.target.value });
  };

  return (
    <div className="w-full bg-white rounded-lg  p-2 px-4 sm:p-4 md:p-6  p-4  lg:ml-[250px] lg:max-w-[calc(100%-250px)] min-h-[500px] flex flex-col h-auto">
      <div className="">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 p-8">
          {/* Illustration Section */}
          <div className="lg:col-span-1 flex items-center justify-center">
            <img
              src={billingSvg}
              alt="Globe illustration"
              className="max-w-full h-auto object-contain"
            />
          </div>

          {/* Forms Section */}
          <div className="lg:col-span-2 grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Card Details Form */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <form onSubmit={handleCardSubmit}>
                <h2 className="text-xl font-bold text-[#3F512C] mb-6">Card Details</h2>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      First Name
                    </label>
                    <input
                      type="text"
                      name="firstName"
                      value={cardDetails.firstName}
                      onChange={handleCardChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3F512C]"
                      placeholder="John"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Last Name
                    </label>
                    <input
                      type="text"
                      name="lastName"
                      value={cardDetails.lastName}
                      onChange={handleCardChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3F512C]"
                      placeholder="Doe"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Card Number
                    </label>
                    <input
                      type="text"
                      name="cardNumber"
                      value={cardDetails.cardNumber}
                      onChange={handleCardChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3F512C]"
                      placeholder="1234 5678 9012 3456"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Expiration Date
                    </label>
                    <input
                      type="text"
                      name="expirationDate"
                      value={cardDetails.expirationDate}
                      onChange={handleCardChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3F512C]"
                      placeholder="MM/YY"
                    />
                  </div>

                  <button
                    type="submit"
                    className="w-full bg-[#3F512C] text-white py-2.5 rounded-md hover:bg-[#2F3D1F] transition duration-300 ease-in-out"
                  >
                    Update Card Details
                  </button>
                </div>
              </form>
            </div>

            {/* Billing Details Form */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <form onSubmit={handleBillingSubmit}>
                <h2 className="text-xl font-bold text-[#3F512C] mb-6">Billing Details</h2>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Address Line 1
                    </label>
                    <input
                      type="text"
                      name="addressLine1"
                      value={billingDetails.addressLine1}
                      onChange={handleBillingChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3F512C]"
                      placeholder="123 Main Street"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Address Line 2
                    </label>
                    <input
                      type="text"
                      name="addressLine2"
                      value={billingDetails.addressLine2}
                      onChange={handleBillingChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3F512C]"
                      placeholder="Apartment, suite, etc. (optional)"
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">City</label>
                      <input
                        type="text"
                        name="city"
                        value={billingDetails.city}
                        onChange={handleBillingChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3F512C]"
                        placeholder="New York"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">State</label>
                      <input
                        type="text"
                        name="state"
                        value={billingDetails.state}
                        onChange={handleBillingChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3F512C]"
                        placeholder="NY"
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Zip Code
                      </label>
                      <input
                        type="text"
                        name="zipCode"
                        value={billingDetails.zipCode}
                        onChange={handleBillingChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3F512C]"
                        placeholder="10001"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Country
                      </label>
                      <input
                        type="text"
                        name="country"
                        value={billingDetails.country}
                        onChange={handleBillingChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3F512C]"
                        placeholder="United States"
                      />
                    </div>
                  </div>
                  <button
                    type="submit"
                    className="w-full bg-[#3F512C] text-white py-2.5 rounded-md hover:bg-[#2F3D1F] transition duration-300 ease-in-out"
                  >
                    Update Billing Details
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
      <div className="col-12">
        <p className="text-muted">
          By clicking &quot;Place order&quot;, you agree to our terms and conditions
        </p>
      </div>
      <div className="col-12">
        <p className="text-muted">
          By clicking &quot;Save changes&quot;, you agree to our terms and conditions
        </p>
      </div>
      <p className="text-muted">
        By clicking &quot;Save card&quot;, you agree to our terms and conditions
      </p>
    </div>
  );
};

export default BillingAddress;
