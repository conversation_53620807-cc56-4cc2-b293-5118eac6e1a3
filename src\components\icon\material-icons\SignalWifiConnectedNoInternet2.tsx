import React, { SVGProps } from 'react';

const SvgSignalWifiConnectedNoInternet2 = (props: SVGProps<SVGSVGElement>) => {
  return (
    <svg viewBox="0 0 24 24" fill="currentColor" className="svg-icon" {...props}>
      <path d="M0 0h24v24H0V0z" fill="none" />
      <path
        d="M18.31 12h2.67L24 8.98A16.88 16.88 0 0012 4C7.31 4 3.07 5.9 0 8.98l4.23 4.24A10.96 10.96 0 0112 10c2.35 0 4.52.74 6.31 2z"
        fillOpacity={0.3}
      />
      <path d="M12 10c-3.03 0-5.78 1.23-7.77 3.22L12 21v-9h6.32c-1.8-1.26-3.97-2-6.32-2zm9 5.41L19.59 14l-2.09 2.09L15.41 14 14 15.41l2.09 2.09L14 19.59 15.41 21l2.09-2.08L19.59 21 21 19.59l-2.08-2.09L21 15.41z" />
    </svg>
  );
};

export default SvgSignalWifiConnectedNoInternet2;
