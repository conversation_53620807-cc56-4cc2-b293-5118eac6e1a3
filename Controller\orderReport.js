const orderSchema = require("../Schema/orderSchema");
const mongoose = require("mongoose");
const Order = mongoose.model("Order", orderSchema);
class OrderReportController {
  async getTotalPurchase(req, res) {
    try {
      const totalPurchases = await Order.aggregate([
        {
          $match: {
            status: "Completed",
            paymentMethod: { $in: ["Crypto", "card"] }, // Filter for specific payment methods
          },
        },
        {
          $group: {
            _id: 0,
            totalPurchaseAmount: {
              $sum: { $toDouble: "$price" }, // Inline conversion of price to number
            },
            totalPurchaseOrders: { $sum: 1 },
          },
        },
      ]);
      const totalPurchasesResult = totalPurchases[0] || {
        totalAmount: 0,
        totalOrders: 0,
      }; // Handle case where no orders match
      return res.status(200).send({
        success: true,
        data: totalPurchasesResult,
      });
    } catch (error) {
      console.error(error);
      res.status(500).send({
        success: false,
        error: "Internal server error",
      });
    }
  }
  async getTotalRedeemed(req, res) {
    try {
      const totalRedeemed = await Order.aggregate([
        {
          $match: {
            status: "Completed",
            paymentMethod: "push-to-card",
          },
        },
        {
          $group: {
            _id: 0,
            totalRedeemedAmount: {
              $sum: { $toDouble: "$price" }, // Inline conversion of price to number
            },
            totalRedeemedOrders: { $sum: 1 },
          },
        },
      ]);
      const totalRedeemedResult = totalRedeemed[0] || {
        totalAmount: 0,
        totalOrders: 0,
      };
      return res.status(200).send({
        success: true,
        data: totalRedeemedResult,
      });
    } catch (error) {
      console.error(error);
      res.status(500).send({
        success: false,
        error: "Internal server error",
      });
    }
  }
}

module.exports = new OrderReportController();
