const { createProxyMiddleware } = require('http-proxy-middleware');

module.exports = function (app) {
  const options = {
    target: process.env.REACT_APP_API_URL || 'https://testapi.luckshack.mobi',
    changeOrigin: true,
    pathRewrite: {
      '^/api': '',
    },
    onProxyRes: function (proxyRes, req, res) {
      const corsHeaders = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, Pragma, Expires',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Credentials': 'true'
      };
      
      Object.entries(corsHeaders).forEach(([key, value]) => {
        proxyRes.headers[key] = value;
      });
    }
  };
  
  app.use('/api', createProxyMiddleware(options));
};
