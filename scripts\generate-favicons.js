const { favicons } = require('favicons');
const fs = require('fs');
const path = require('path');

const source = path.resolve(__dirname, '../src/assets/logo.svg'); // Path to your source image
const configuration = {
  path: '/', // Path for overriding default icons path
  appName: 'America\'s #1 Online Sweepstakes Casino',
  appShortName: 'LuckShack',
  appDescription: 'Experience America\'s #1 Online Sweepstakes Casino',
  developerName: 'LuckShack',
  developerURL: null,
  dir: 'auto',
  lang: 'en-US',
  background: '#fff',
  theme_color: '#fff',
  appleStatusBarStyle: 'black-translucent',
  display: 'standalone',
  orientation: 'any',
  scope: '/',
  start_url: '/',
  version: '1.0',
  logging: true,
  icons: {
    android: true,
    appleIcon: true,
    favicons: true,
    windows: true
  },
};

const callback = function (error, response) {
  if (error) {
    console.log(error.message);
    return;
  }

  const publicPath = path.resolve(__dirname, '../public');

  // Create the public directory if it doesn't exist
  if (!fs.existsSync(publicPath)) {
    fs.mkdirSync(publicPath);
  }

  // Save the files to the public directory
  response.images.forEach((image) => {
    fs.writeFileSync(
      path.resolve(publicPath, image.name),
      image.contents
    );
  });

  response.files.forEach((file) => {
    fs.writeFileSync(
      path.resolve(publicPath, file.name),
      file.contents
    );
  });

  console.log('Favicon generation completed!');
};

favicons(source, configuration, callback);
