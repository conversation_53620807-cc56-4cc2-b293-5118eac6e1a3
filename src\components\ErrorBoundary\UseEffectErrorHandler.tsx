import React, { useEffect } from 'react';

/**
 * A component that patches React's useEffect to prevent errors from showing in the console
 * This is especially useful for test components that intentionally throw errors
 */
const UseEffectErrorHandler: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  useEffect(() => {
    // Only apply in production
    if (process.env.NODE_ENV === 'development') {
      return;
    }

    // Store original console methods
    const originalConsoleError = console.error;
    const originalConsoleWarn = console.warn;

    // List of error messages to suppress - ONLY our test errors
    const errorMessagesToSuppress = [
      'Simulated error',
      'Counter reached 5',
      'Simulated async error',
      'Simulated error in useEffect'
    ];

    // List of React error patterns that should only be suppressed when related to our test errors
    const reactErrorPatterns = [
      'React will try to recreate',
      'The above error occurred',
      'Consider adding an error boundary'
    ];

    // Function to check if an error should be suppressed
    const shouldSuppressError = (message: any): boolean => {
      if (!message) return false;

      // For string messages
      if (typeof message === 'string') {
        // First check for our specific test errors
        const isTestError = errorMessagesToSuppress.some(keyword => message.includes(keyword));
        if (isTestError) return true;

        // Then check if it's a React error related to our test components
        const isReactError = reactErrorPatterns.some(pattern => message.includes(pattern));
        if (isReactError) {
          // Only suppress React errors if they're related to our test components
          return errorMessagesToSuppress.some(testError => message.includes(testError));
        }

        return false;
      }

      // For Error objects
      if (message instanceof Error) {
        // Check the error message
        const isTestError = errorMessagesToSuppress.some(keyword =>
          message.message.includes(keyword)
        );
        if (isTestError) return true;

        // Check the stack trace
        if (message.stack) {
          const isTestErrorInStack = errorMessagesToSuppress.some(keyword =>
            message.stack!.includes(keyword)
          );
          if (isTestErrorInStack) return true;

          // Check for React errors in the stack that are related to our test components
          const isReactError = reactErrorPatterns.some(pattern =>
            message.stack!.includes(pattern)
          );
          if (isReactError) {
            return errorMessagesToSuppress.some(testError =>
              message.stack!.includes(testError)
            );
          }
        }
      }

      return false;
    };

    // Override console.error
    console.error = function suppressedConsoleError(...args) {
      // Check if this is an error we should suppress
      const shouldSuppress = args.some(shouldSuppressError);

      if (shouldSuppress) {
        // Don't log suppressed errors
        return;
      }

      // Pass through other errors
      originalConsoleError.apply(console, args);
    };

    // Override console.warn
    console.warn = function suppressedConsoleWarn(...args) {
      // Check if this is a warning we should suppress
      const shouldSuppress = args.some(shouldSuppressError);

      if (shouldSuppress) {
        // Don't log suppressed warnings
        return;
      }

      // Pass through other warnings
      originalConsoleWarn.apply(console, args);
    };

    // Create a special error handler for useEffect errors
    const handleUseEffectError = (event: ErrorEvent) => {
      // Check if this is one of our test errors
      if (event.error && shouldSuppressError(event.error)) {
        // Prevent the error from showing in the console
        event.preventDefault();
        event.stopPropagation();
        return true;
      }

      // Also check the error message
      if (event.message && shouldSuppressError(event.message)) {
        // Prevent the error from showing in the console
        event.preventDefault();
        event.stopPropagation();
        return true;
      }
    };

    // Add event listener with capture to catch all errors
    window.addEventListener('error', handleUseEffectError, { capture: true });

    // Clean up when component unmounts
    return () => {
      console.error = originalConsoleError;
      console.warn = originalConsoleWarn;
      window.removeEventListener('error', handleUseEffectError, { capture: true });
    };
  }, []);

  return <>{children}</>;
};

export default UseEffectErrorHandler;
