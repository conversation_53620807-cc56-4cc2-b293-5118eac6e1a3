var express = require("express");
var router = express.Router();
const Auth = require("../auth/middleware");
const walletController = require("../Controller/walletController");

// Create a new wallet
router.post(
  "/wallet/create/:userId",
  Auth.verifyToken,
  walletController.createWallet
);

// Check wallet
router.get(
  "/wallet/check/:userId",
  Auth.verifyToken,
  walletController.checkWallet
);

// Add swipe coins to wallet
router.post(
  "/wallet/add-swipe-coins",
  Auth.verifyToken,
  walletController.addSwipeCoins
);

router.post(
  "/wallet/swipecoin/:userId",
  Auth.verifyToken,
  walletController.addSwipeCoinsManual
);
// Add gold coins to wallet

router.post(
  "/wallet/add-gold-coins",
  Auth.verifyToken,
  walletController.addSwipeCoinsManual
);

// Remove swipe coins from wallet
router.post(
  "/wallet/remove-swipe-coins",
  Auth.verifyToken,
  walletController.removeSwipeCoins
);

// Remove gold coins from wallet
router.post(
  "/wallet/remove-gold-coins",
  Auth.verifyToken,
  walletController.removeGoldCoins
);

// Get wallet balance
router.get(
  "/wallet/balance/:userId",
  Auth.verifyToken,
  walletController.getBalance
);

// Get wallet transactions
router.get(
  "/wallet/transactions/:userId",
  Auth.verifyToken,
  walletController.getTransactions
);

// Handle spin results
router.post(
  "/wallet/spin-result/:userId",
  Auth.verifyToken,
  walletController.handleSpinResult.bind(walletController)
);

// Load coins from unplayed balance to a game
router.post(
  "/wallet/load-coins-to-game",
  Auth.verifyToken,
  walletController.loadCoinsToGame
);

// Request redemption from a game
router.post(
  "/wallet/request-redemption",
  Auth.verifyToken,
  walletController.requestRedemption
);

// Approve redemption (admin/cashier only)
router.post(
  "/wallet/approve-redemption",
  Auth.verifyToken,
  walletController.approveRedemption
);

module.exports = router;
