<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <title>LuckShack - Offline</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="theme-color" content="#365314" />
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f8f9fa;
      color: #333;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
    }
    .offline-container {
      text-align: center;
      max-width: 600px;
      padding: 2rem;
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 4px 6px rgba(0,0,0,0.1);
      margin: 1rem;
    }
    .logo {
      width: 120px;
      height: auto;
      margin-bottom: 2rem;
    }
    h1 {
      color: #365314;
      margin-bottom: 1rem;
    }
    p {
      margin-bottom: 1.5rem;
      line-height: 1.5;
    }
    .button {
      background-color: #365314;
      color: white;
      border: none;
      padding: 0.75rem 1.5rem;
      border-radius: 4px;
      font-weight: 600;
      cursor: pointer;
      transition: background-color 0.2s;
    }
    .button:hover {
      background-color: #2d4712;
    }
  </style>
</head>
<body>
  <div class="offline-container">
    <img src="./LuckShack_img.png" alt="LuckShack Logo" class="logo" />
    <h1>You're currently offline</h1>
    <p>
      It looks like you've lost your internet connection. 
      Don't worry - LuckShack has saved some content so you can view 
      previously loaded pages offline.
    </p>
    <p>
      Please check your internet connection and try again.
    </p>
    <button class="button" onclick="window.location.reload()">
      Try Again
    </button>
  </div>
</body>
</html>
