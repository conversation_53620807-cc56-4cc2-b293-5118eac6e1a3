const axios = require("axios");
const mongoose = require("mongoose");
const GameRequestModel = require("../Model/gameRequestModel");
const userModel = require("../Model/usersModel");
const productModel = require("../Model/productModel");
const gameRequestSchema = require("../Schema/gameRequestSchema");
const GameRequest = mongoose.model("GameRequest", gameRequestSchema);

class GameRequestController {
  async add(req, res) {
    try {
      let gameData = req.body;
      console.log("GameRequest data:", gameData);

      // Validate required fields
      if (!gameData.userId || !gameData.productId) {
        return res.status(400).json({ error: 'Missing required fields' });
      }

      const user = await userModel.findById(gameData.userId);
      if (!user) {
        return res.status(404).json({ error: 'User not found' });
      }
      const product = await productModel.findById(gameData.productId);
      if (!product) {
        return res.status(404).json({ error: 'Product not found' });
      }
      console.log("user in gameRequest admin===", user);
      console.log("product in gameRequest admin===", product);

      // Check if the user has already requested this game
      const existingRequest = await GameRequestModel.findOne({
        userId: gameData.userId,
        productId: gameData.productId
      });

      if (existingRequest) {
        return res.status(400).json({
          error: 'You have already requested this game',
          existingRequest
        });
      }

      // Include play link from the product
      gameData.playLink = product.playLink;

      let gameResult = await GameRequestModel.add(gameData);
      console.log("gameResult=====", gameResult);

      const imageUrl = `https://s3.wasabisys.com/productimage/${encodeURIComponent(product.imageName)}`;
      console.log("Product Image URL for email:", imageUrl);

      const userName = user?.firstName && user?.lastName
        ? `${user.firstName} ${user.lastName}`
        : 'User';

      const message = `
  <div style="font-family: Arial, sans-serif; color: #ffffff; text-align: center; background-color: #1a1a1a; padding: 20px; max-width: 500px; margin: 0 auto; border-radius: 8px;">
          <div style="font-size: 16px; color: #9fa3ab; margin-bottom: 10px;">Luckshack</div>
          <div style="font-size: 20px; font-weight: bold; color: #ffffff; margin-bottom: 20px;">
            Hi ${userName},
          </div>
          <div style="font-size: 18px; margin-bottom: 20px; color: #d3d3d3;">
            Thank you for requesting account of  <strong>"${gameData.gameName}"</strong> !
          </div>
          ${gameData.playLink ? `
          <div style="font-size: 16px; margin-bottom: 20px; color: #d3d3d3;">
            You can play the game here: <a href="${gameData.playLink}" style="color: #4CAF50; text-decoration: none;">${gameData.playLink}</a>
          </div>
          ` : ''}
          <div style="font-size: 14px; color: #9fa3ab; margin-top: 20px;">
            Thank you for choosing Luckshack Mobi!
          </div>
        </div>
      `;

      const emailResponse = await GameRequestController.sendGameRequestEmail(user.email, message);
      console.log("emailResponse= from add ====", emailResponse);
      if (emailResponse.error) {
        console.error("Error in sendApproveMessage:", emailResponse.error);
        return res.status(500).json({ error: 'Failed to send approval email' });
      }

      res.status(201).json({ data: gameResult, error: null });

    } catch (error) {
      console.error("Error in saving game request details:", error);
      res.status(500).json({
        error: error.message || 'An error occurred while processing your request',
        details: process.env.NODE_ENV === 'development' ? error.stack : undefined
      });
    }
  }

  async list(req, res) {
    try {
      const {
        requestType,
        page = 1,
        limit = 10,
        search,
        startDate,
        endDate,
        userName,
        userEmail,
        gameName,
        status,
        passwordReset
      } = req.query;
      console.log("requestType from game request list--", requestType);
      console.log("Filters received:", { search, startDate, endDate, userName, userEmail, gameName, status, passwordReset });

      const { games, totalItems, totalPages } = await GameRequestModel.findAll(
        requestType,
        parseInt(page),
        parseInt(limit),
        {
          search,
          startDate,
          endDate,
          userName,
          userEmail,
          gameName,
          status,
          passwordReset
        }
      );
      const pendingPasswordReqCount = await GameRequestModel.count({ request_type: "passwordReset", passwordReset: "Approve" });
      const pendingGameReqCount = await GameRequestModel.count({ request_type: "gameRequest", status: "Pending" });

      res.status(200).send({
        data: games,
        pendingPasswordReqCount,
        pendingGameReqCount,
        pagination: {
          totalItems,
          totalPages,
          currentPage: parseInt(page),
          itemsPerPage: parseInt(limit)
        },
        error: ''
      });
    } catch (error) {
      console.log("Error in fetching game requests:", error);
      res.status(500).send({ error: error.message });
    }
  }
  async approvedGameRequests(req, res) {
    try {
      const { requestType, page = 1, limit = 15 } = req.query;
      const userId = req.user?._id; // Get the authenticated user's ID
      if (!userId) {
        return res.status(401).send({ error: "Authentication required" });
      }

      console.log("requestType from game request list--", requestType);
      console.log("User ID:", userId);

      // Fetch all enabled products first
      const enabledProducts = await productModel.findAll();
      console.log("enabledProducts count:", enabledProducts.length);

      // Create a map of products by ID for faster lookup
      const productsMap = {};
      enabledProducts.forEach(product => {
        productsMap[product._id.toString()] = product;
      });

      const enabledProductIds = enabledProducts.map(product => product._id.toString());

      // We need to get access to the actual mongoose model
      const GameRequest = mongoose.model('GameRequest');

      // Create a custom query for finding approved games
      const customQuery = {
        userId: mongoose.isValidObjectId(userId) ? new mongoose.Types.ObjectId(userId) : userId,
        approved: true,
        productId: { $in: enabledProductIds.map(id =>
          mongoose.isValidObjectId(id) ? new mongoose.Types.ObjectId(id) : id
        )}
      };

      // Add requestType filter if provided
      if (requestType) {
        customQuery.request_type = requestType;
      }

      console.log("Query:", JSON.stringify(customQuery));

      // Get total count for pagination
      const totalItems = await GameRequest.countDocuments(customQuery);
      console.log("Total matching game requests:", totalItems);

      const totalPages = Math.ceil(totalItems / parseInt(limit));
      const skip = (parseInt(page) - 1) * parseInt(limit);

      // Execute the query with pagination directly using the mongoose model
      let enabledApprovedGames = await GameRequest.find(customQuery)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(parseInt(limit))
        .lean();

      console.log("Found game requests:", enabledApprovedGames.length);

      // Manually populate the product information for each game request
      enabledApprovedGames = enabledApprovedGames.map(game => {
        // Get the product ID as a string
        const productId = game.productId ? game.productId.toString() : null;

        // Look up the product in our map
        const product = productId && productsMap[productId] ? productsMap[productId] : null;

        // Add the product information to the game request
        return {
          ...game,
          product: product
        };
      });

      console.log("Processed game requests with products:", enabledApprovedGames.length);

      res.status(200).send({
        data: enabledApprovedGames,
        pagination: {
          totalItems,
          totalPages,
          currentPage: parseInt(page),
          itemsPerPage: parseInt(limit),
        },
        error: "",
      });
    } catch (error) {
      console.log("Error in fetching game requests:", error);
      res.status(500).send({ error: error.message });
    }
  }

  async getgameById(req, res) {
    try {
      const { id } = req.params;
      const game = await GameRequestModel.findById(id);
      console.log("game from getgameById--", game)
      if (!game) {
        console.log("game finside --", game)

        return res.status(404).send({ error: 'Game not found' });
      }

      res.status(200).send({ data: game, error: '' });
    } catch (error) {
      console.log("Error in fetching game:", error);
      res.status(500).send({ error: error.message });
    }
  }
  static async sendApproveMessage(userEmail, message) {
    console.log(`Simulating email to: ${userEmail}, Message: ${message}`);

    const apiKey = 'em_7mgzvnsJWS8FkEkYzQRZbZBV4GMtWYnG'; // Replace with environment variable in production
    const emailData = {
      from: '<EMAIL>',
      to: userEmail,
      subject: 'Your Game Request Approval',
      html: `
            <div style="text-align: center; font-family: Arial, sans-serif; color: #333; line-height: 1.5;">
                <p style="font-size: 20px; margin-bottom: 10px;">Welcome to Luckshack!</p>
                <p style="font-size: 16px; margin-bottom: 20px;">Your game request has been approved.</p>
                <p style="font-size: 24px; font-weight: bold; color: #2b8a3e;">${message}</p>
            </div>
        `,
    };
    console.log("EmailData-----", emailData);

    try {
      const response = await axios.post('https://api.emailit.com/v1/emails', emailData, {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
        },
      });
      console.log("Email sent successfully:", response.data);
      return { success: true };
    } catch (error) {
      console.error('Error sending email:', error);
      return { error: 'Failed to send email' };
    }
  }
  static async sendGameRequestEmail(userEmail, message) {
    console.log(`Simulating email to: ${userEmail}, Message: ${message}`);

    const apiKey = 'em_7mgzvnsJWS8FkEkYzQRZbZBV4GMtWYnG'; // Replace with environment variable in production
    const emailData = {
      from: '<EMAIL>',
      to: userEmail,
      subject: 'Your Game Request has been sent',
      html: `
      <div style="text-align: center; font-family: Arial, sans-serif; color: #333; line-height: 1.5;">
          <p style="font-size: 20px; margin-bottom: 10px;">Welcome to Luckshack!</p>
          <p style="font-size: 16px; margin-bottom: 20px;">Your Game Request has been sent successfully.</p>
          <p style="font-size: 24px; font-weight: bold; color: #0073e6;">${message}</p>
      </div>
  `,
    };
    console.log("EmailData-----", emailData);

    try {
      const response = await axios.post('https://api.emailit.com/v1/emails', emailData, {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
        },
      });
      console.log("Email sent successfully:", response.data);
      return { success: true };
    } catch (error) {
      console.error('Error sending email:', error);
      return { error: 'Failed to send email' };
    }
  }
  async sendResetPasswordRequest(req, res) {
    try {
      const { id } = req.params;
      const gameData = req.body;
      console.log("GameData in update---", gameData);
      if (!gameData || Object.keys(gameData).length === 0) {
        return res.status(400).send({ error: 'No data provided for update' });
      }
      console.log("id in sendResetPasswordRequest--", id);

      // Get the original game request before updating
      const originalGameRequest = await GameRequestModel.findById(id);

      //gameData.approved = true;
      gameData.status = 'Approved';
      gameData.approved = true;
      gameData.passwordReset = "Approve"
      const updatedGameRequest = await GameRequestModel.update(id, gameData);

      console.log("updatedGameRequest in controller---", updatedGameRequest);

      if (!updatedGameRequest) {
        return res.status(404).send({ error: 'Game not found' });
      }

      // Log the password reset approval
      if (req.user) {
        const { logActivity } = require('../utils/logger');
        await logActivity({
          user: req.user,
          action: 'Password reset request approved',
          entityType: 'password_reset',
          entityId: id,
          previousState: {
            passwordReset: originalGameRequest?.passwordReset || 'Pending',
            status: originalGameRequest?.status || 'Pending'
          },
          newState: {
            passwordReset: "Approve",
            status: 'Approved'
          },
          notes: 'Password reset request approved by staff',
          ipAddress: req.ip
        });
      }

      res.status(200).send({ data: updatedGameRequest, error: '' });
    } catch (error) {
      console.log("Error in updating game request:", error);
      res.status(500).send({ error: error.message });
    }
  }
  async update(req, res) {
    try {
      const { id } = req.params;
      const gameData = req.body;
      console.log("GameData in update---", gameData);
      if (!gameData || Object.keys(gameData).length === 0) {
        return res.status(400).send({ error: 'No data provided for update' });
      }
      //  gameData.status = 'Approved';
      //   gameData.approved = true;

      // Get the original game request before updating
      const originalGameRequest = await GameRequestModel.findById(id);

      if (gameData.status === 'Rejected') {
        gameData.approved = false;
      } else if (gameData.status === 'Approved') {
        gameData.approved = true;
      }

      if (gameData.fieldValues) {
        gameData.gameFields = gameData.fieldValues; // Update the gameFields column with submitted JSON
      }
      const updatedGameRequest = await GameRequestModel.update(id, gameData);
      console.log("updatedGameRequest in controller---", updatedGameRequest);

      if (!updatedGameRequest) {
        return res.status(404).send({ error: 'Game not found' });
      }

      // Log the game request status change if status was changed
      if (gameData.status && originalGameRequest && req.user) {
        const { logActivity } = require('../utils/logger');
        await logActivity({
          user: req.user,
          action: `Game request status changed from ${originalGameRequest.status || 'Pending'} to ${gameData.status}`,
          entityType: 'game',
          entityId: id,
          previousState: {
            status: originalGameRequest.status || 'Pending',
            approved: originalGameRequest.approved
          },
          newState: {
            status: gameData.status,
            approved: gameData.approved
          },
          notes: gameData.notes || 'Game request status updated',
          ipAddress: req.ip
        });
      }

      console.log("Game data:", updatedGameRequest);
      if (gameData.approved === true) {
        const gameFieldsHtml = Object.entries(updatedGameRequest.gameFields || {})
          .map(([key, value]) => `
    <p style="margin: 5px 0;">
      ${key}: <strong style="font-size: 18px; color: #ffffff;">${value}</strong>
    </p>
  `)
          .join('');
        console.log("gameFieldsHtml===", gameFieldsHtml);
        const user = await userModel.findOne({ "_id": updatedGameRequest.userId });
        const product = await productModel.findById({ "_id": updatedGameRequest.productId });
        console.log("user in gameRequest admin===", user);
        const imageUrl = `https://s3.wasabisys.com/productimage/${encodeURIComponent(product.imageName)}`;
        console.log("Product Image URL for email:", imageUrl);

        console.log("product in gameRequest admin===", product);
        const userName = user?.firstName && user?.lastName
          ? `${user.firstName} ${user.lastName}`
          : 'User';

        const message = `
  <div style="font-family: Arial, sans-serif; color: #ffffff; text-align: center; background-color: #1a1a1a; padding: 20px; max-width: 400px; margin: 0 auto; border-radius: 8px;">
          <div style="font-size: 16px; color: #9fa3ab; margin-bottom: 10px;">Luckshack</div>
          <div style="font-size: 20px; font-weight: bold; color: #ffffff; margin-bottom: 20px;">
            Hi ${userName},
          </div>
          <div style="font-size: 18px; margin-bottom: 20px; color: #d3d3d3;">
            Your game request for <strong>"${updatedGameRequest.gameName}"</strong> has been approved!
          </div>
          <div style="font-size: 10px; color: #d3d3d3; margin-top: 10px; margin-bottom: 20px;">
            Below are the details:
          </div>
            <div style="font-size: 14px; color: #d3d3d3; text-align: left; margin: 0 auto; display: inline-block;">

      <p style="margin: 5px 0;">
       <strong style="font-size: 18px; color: #ffffff;">${gameFieldsHtml}</strong>
      </p>
    </div>
          <div style="margin: 20px 0;">
            <img src="https://s3.wasabisys.com/productimage/${encodeURIComponent(
          product.imageName
        )}" alt="${updatedGameRequest.gameName}" style="max-width: 100%; height: auto; border-radius: 8px;" />
          </div>
          ${updatedGameRequest.playLink ? `
          <div style="font-size: 16px; margin-bottom: 20px; color: #d3d3d3;">
            You can play the game here: <a href="${updatedGameRequest.playLink}" style="color: #4CAF50; text-decoration: none;">${updatedGameRequest.playLink}</a>
          </div>
          ` : ''}
          <div style="font-size: 14px; color: #9fa3ab; margin-top: 20px;">
            Thank you for choosing Luckshack Mobi!
          </div>
        </div>
      `;

        const emailResponse = await GameRequestController.sendApproveMessage(user.email, message);

        if (emailResponse.error) {
          console.error("Error in sendApproveMessage:", emailResponse.error);
          return res.status(500).send({ error: 'Failed to send approval email' });
        }
      }

      res.status(200).send({ data: updatedGameRequest, error: '' });
    } catch (error) {
      console.log("Error in updating game request:", error);
      res.status(500).send({ error: error.message });
    }
  }
  async getGameFieldsByUserIdAndGameId(req, res) {
  try {
    const { userId, gameId } = req.params;
    console.log("UserId and GameId in getGameFieldsByUserIdAndGameId---", userId, gameId);
    if (!userId || !gameId) {
      return res.status(400).json({ error: 'User ID and Game ID are required' });
    }
    console.log(GameRequestModel)

    const gameRequest = await GameRequestModel.findOne({
      // userId,
      _id: gameId
    });


    if (!gameRequest) {
      return res.status(404).json({ error: 'Game request not found' });
    }

    const gameFields = gameRequest.gameFields;
    res.status(200).json({ data: gameFields, error: null });
  } catch (error) {
    console.error("Error in getting game fields by user ID and game ID:", error);
    res.status(500).json({ error: error.message });
  }
}
  async sendResetPasswordEmail(req, res) {
    try {
      const { id } = req.params;
      if (!id) {
        return res.status(400).json({ error: "ID is required." });
      }
      const gameData = req.body;
      console.log("GameData in update---", gameData);
      if (!gameData || Object.keys(gameData).length === 0) {
        return res.status(400).send({ error: 'No data provided for update' });
      }
      gameData.approved = true;
      gameData.passwordReset = "Requested";
      // Reset the passwordRequest field to null so the user can request again
      gameData.passwordRequest = null;

      const updatedGameRequest = await GameRequestModel.update(id, gameData);
      console.log("updatedGameRequest in controller---", updatedGameRequest);

      if (!updatedGameRequest) {
        return res.status(404).send({ error: 'Game not found' });
      }
      console.log("Game data:", updatedGameRequest);
      if (gameData.approved === true) {
        const user = await userModel.findOne({ "_id": updatedGameRequest.userId });
        //const product = await productModel.findById({ "_id": updatedGameRequest.productId });
        console.log("user in gameRequest admin===", user);
        //console.log("product in gameRequest admin===", product);

        const message = `
  <div style="font-family: Arial, sans-serif; color: #ffffff; text-align: left; background-color: #000000; padding: 20px; max-width: 400px; margin: 0 auto; border-radius: 8px;">
    <div style="font-size: 16px; font-weight: bold; color: #ffffff; margin-bottom: 20px;">
      Update!
    </div>
    <div style="font-size: 16px; line-height: 1.5; color: #d3d3d3; margin-bottom: 20px;">
      Your password reset for <strong>"${updatedGameRequest.gameName}"</strong> has been successfully updated!
    </div>
    <div style="font-size: 14px; line-height: 1.5; color: #d3d3d3; margin-bottom: 10px;">
      User Id: <strong style="color: #ffffff;">${updatedGameRequest.gameUserId}</strong>
    </div>
    <div style="font-size: 14px; line-height: 1.5; color: #d3d3d3; margin-bottom: 20px;">
     Game Password:
         <strong style="font-size: 18px; color: #ffffff;">${updatedGameRequest.gamePassword}</strong>
    </div>

    <div style="font-size: 14px; color: #d3d3d3; margin-top: 20px;">
      Thank you for choosing <strong style="color: #ffffff;">Luckshack Mobi</strong>!
    </div>
  </div>
      `;

        const emailResponse = await GameRequestController.sendApproveMessage(user.email, message);

        if (emailResponse.error) {
          console.error("Error in sendApproveMessage:", emailResponse.error);
          return res.status(500).send({ error: 'Failed to send approval email' });
        }
      }

      res.status(200).send({ data: updatedGameRequest, error: '' });
    } catch (error) {
      console.log("Error in updating game request:", error);
      res.status(500).send({ error: error.message });
    }
  }

  async getgameRequestById(req, res) {
    try {
      const { id } = req.params;
      console.log("id in game request:", id);
      console.log("req.params:", req.params);

      if (!id) {
        return res.status(400).send({ error: "ID parameter is required" });
      }

      let query;
      if (!isNaN(id)) {
        query = { requestId: Number(id) };
      } else if (mongoose.Types.ObjectId.isValid(id)) {
        query = { _id: id };
      } else {
        return res.status(400).send({ error: "Invalid ID format" });
      }

      const game = await GameRequestModel.findOne(query);

      if (!game) {
        return res.status(404).send({ error: "Game not found" });
      }

      res.status(200).send({ data: game, error: "" });
    } catch (error) {
      console.error("Error in fetching game:", error);
      res.status(500).send({ error: "Internal Server Error" });
    }
  }

  // Freeze a game for a specific user
  async freezeGameForUser(req, res) {
    try {
      const { gameId, userId, sendEmail } = req.body;

      if (!gameId || !userId) {
        return res.status(400).json({ error: 'Game ID and User ID are required' });
      }

      // Validate that the game exists
      const game = await GameRequestModel.findById(gameId);
      if (!game) {
        return res.status(404).json({ error: 'Game not found' });
      }

      // Validate that the user exists
      const user = await userModel.findById(userId);
      if (!user) {
        return res.status(404).json({ error: 'User not found' });
      }

      // Check if the game is already frozen for this user
      if (game.frozenForUsers && game.frozenForUsers.includes(userId)) {
        return res.status(400).json({ error: 'Game is already frozen for this user' });
      }

      // Add the user to the frozenForUsers array
      const updatedGame = await GameRequestModel.update(gameId, {
        $addToSet: { frozenForUsers: userId }
      });

      // Send email notification if requested
      if (sendEmail) {
        const userName = user?.firstName && user?.lastName
          ? `${user.firstName} ${user.lastName}`
          : 'User';

        const message = `
        <div style="font-family: Arial, sans-serif; color: #ffffff; text-align: center; background-color: #1a1a1a; padding: 20px; max-width: 500px; margin: 0 auto; border-radius: 8px;">
          <div style="font-size: 16px; color: #9fa3ab; margin-bottom: 10px;">Luckshack</div>
          <div style="font-size: 20px; font-weight: bold; color: #ffffff; margin-bottom: 20px;">
            Hi ${userName},
          </div>
          <div style="font-size: 18px; margin-bottom: 20px; color: #d3d3d3;">
            Your access to <strong>"${game.gameName}"</strong> has been temporarily frozen.
          </div>
          <div style="font-size: 16px; margin-bottom: 20px; color: #d3d3d3;">
            If you have any questions, please contact our support team.
          </div>
          <div style="font-size: 14px; color: #9fa3ab; margin-top: 20px;">
            Thank you for choosing Luckshack Mobi!
          </div>
        </div>
        `;

        try {
          const emailResponse = await GameRequestController.sendApproveMessage(user.email, message);
          console.log("Email notification for game freeze sent:", emailResponse);
        } catch (emailError) {
          console.error("Error sending freeze notification email:", emailError);
          // We don't want to fail the whole request if just the email fails
        }
      }

      return res.status(200).json({
        data: updatedGame,
        message: `Game ${game.gameName} has been frozen for user ${user.firstName} ${user.lastName}`,
        error: null
      });
    } catch (error) {
      console.error("Error freezing game for user:", error);
      return res.status(500).json({
        error: error.message || 'An error occurred while processing your request'
      });
    }
  }

  // Unfreeze a game for a specific user
  async unfreezeGameForUser(req, res) {
    try {
      const { gameId, userId, sendEmail } = req.body;

      if (!gameId || !userId) {
        return res.status(400).json({ error: 'Game ID and User ID are required' });
      }

      // Validate that the game exists
      const game = await GameRequestModel.findById(gameId);
      if (!game) {
        return res.status(404).json({ error: 'Game not found' });
      }

      // Validate that the user exists
      const user = await userModel.findById(userId);
      if (!user) {
        return res.status(404).json({ error: 'User not found' });
      }

      // Check if the game is not frozen for this user
      if (!game.frozenForUsers || !game.frozenForUsers.includes(userId)) {
        return res.status(400).json({ error: 'Game is not frozen for this user' });
      }

      // Remove the user from the frozenForUsers array
      const updatedGame = await GameRequestModel.update(gameId, {
        $pull: { frozenForUsers: userId }
      });

      // Send email notification if requested
      if (sendEmail) {
        const userName = user?.firstName && user?.lastName
          ? `${user.firstName} ${user.lastName}`
          : 'User';

        const message = `
        <div style="font-family: Arial, sans-serif; color: #ffffff; text-align: center; background-color: #1a1a1a; padding: 20px; max-width: 500px; margin: 0 auto; border-radius: 8px;">
          <div style="font-size: 16px; color: #9fa3ab; margin-bottom: 10px;">Luckshack</div>
          <div style="font-size: 20px; font-weight: bold; color: #ffffff; margin-bottom: 20px;">
            Hi ${userName},
          </div>
          <div style="font-size: 18px; margin-bottom: 20px; color: #d3d3d3;">
            Good news! Your access to <strong>"${game.gameName}"</strong> has been restored.
          </div>
          <div style="font-size: 16px; margin-bottom: 20px; color: #d3d3d3;">
            You can now access and play the game again.
          </div>
          ${game.playLink ? `
          <div style="font-size: 16px; margin-bottom: 20px; color: #d3d3d3;">
            You can play the game here: <a href="${game.playLink}" style="color: #4CAF50; text-decoration: none;">${game.playLink}</a>
          </div>
          ` : ''}
          <div style="font-size: 14px; color: #9fa3ab; margin-top: 20px;">
            Thank you for choosing Luckshack Mobi!
          </div>
        </div>
        `;

        try {
          const emailResponse = await GameRequestController.sendApproveMessage(user.email, message);
          console.log("Email notification for game unfreeze sent:", emailResponse);
        } catch (emailError) {
          console.error("Error sending unfreeze notification email:", emailError);
          // We don't want to fail the whole request if just the email fails
        }
      }

      return res.status(200).json({
        data: updatedGame,
        message: `Game ${game.gameName} has been unfrozen for user ${user.firstName} ${user.lastName}`,
        error: null
      });
    } catch (error) {
      console.error("Error unfreezing game for user:", error);
      return res.status(500).json({
        error: error.message || 'An error occurred while processing your request'
      });
    }
  }

  // Get users for whom a game is frozen
  async getFrozenUsersForGame(req, res) {
    try {
      const { gameId } = req.params;

      if (!gameId) {
        return res.status(400).json({ error: 'Game ID is required' });
      }

      // Validate that the game exists
      const game = await GameRequestModel.findById(gameId);
      if (!game) {
        return res.status(404).json({ error: 'Game not found' });
      }

      // If no users have the game frozen, return an empty array
      if (!game.frozenForUsers || game.frozenForUsers.length === 0) {
        return res.status(200).json({
          data: [],
          error: null
        });
      }

      // Get user details for each user ID in the frozenForUsers array
      const userPromises = game.frozenForUsers.map(userId => userModel.findById(userId));
      const users = await Promise.all(userPromises);

      // Filter out any null values (in case a user was deleted)
      const validUsers = users.filter(user => user !== null).map(user => ({
        _id: user._id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        phone: user.phone
      }));

      return res.status(200).json({
        data: validUsers,
        error: null
      });
    } catch (error) {
      console.error("Error getting frozen users for game:", error);
      return res.status(500).json({
        error: error.message || 'An error occurred while processing your request'
      });
    }
  }
}
module.exports = new GameRequestController();
