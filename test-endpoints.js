const axios = require('axios');

// Test the reports endpoints
async function testEndpoints() {
  const baseURL = 'http://localhost:9000';

  // Valid test token generated for testing
  const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************.yv5y9EHsnl8c00Gg56ILPuMzBpvZFt1VLMXG1qrVUVw';

  const headers = {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  };

  console.log('🧪 Testing Reports API Endpoints...\n');

  // Test 1: Health check
  try {
    console.log('1. Testing health check...');
    const response = await axios.get(`${baseURL}/`);
    console.log('✅ Health check:', response.data);
  } catch (error) {
    console.log('❌ Health check failed:', error.message);
  }

  // Test 2: Dashboard endpoint
  try {
    console.log('\n2. Testing dashboard endpoint...');
    const response = await axios.get(`${baseURL}/reports/dashboard`, { headers });
    console.log('✅ Dashboard endpoint:', response.status, response.data?.success ? 'SUCCESS' : 'FAILED');
  } catch (error) {
    console.log('❌ Dashboard endpoint failed:', error.response?.status, error.response?.data || error.message);
  }

  // Test 3: Purchases endpoint
  try {
    console.log('\n3. Testing purchases endpoint...');
    const response = await axios.get(`${baseURL}/reports/purchases?groupBy=date&page=1&limit=10`, { headers });
    console.log('✅ Purchases endpoint:', response.status, response.data?.success ? 'SUCCESS' : 'FAILED');
  } catch (error) {
    console.log('❌ Purchases endpoint failed:', error.response?.status, error.response?.data || error.message);
  }

  // Test 4: Redeems endpoint
  try {
    console.log('\n4. Testing redeems endpoint...');
    const response = await axios.get(`${baseURL}/reports/redeems?groupBy=date&page=1&limit=10`, { headers });
    console.log('✅ Redeems endpoint:', response.status, response.data?.success ? 'SUCCESS' : 'FAILED');
  } catch (error) {
    console.log('❌ Redeems endpoint failed:', error.response?.status, error.response?.data || error.message);
  }

  // Test 5: Wallet balances endpoint
  try {
    console.log('\n5. Testing wallet balances endpoint...');
    const response = await axios.get(`${baseURL}/reports/wallet-balances`, { headers });
    console.log('✅ Wallet balances endpoint:', response.status, response.data?.success ? 'SUCCESS' : 'FAILED');
  } catch (error) {
    console.log('❌ Wallet balances endpoint failed:', error.response?.status, error.response?.data || error.message);
  }

  // Test 6: Test without authentication
  try {
    console.log('\n6. Testing without authentication...');
    const response = await axios.get(`${baseURL}/reports/dashboard`);
    console.log('❌ Should have failed but got:', response.status);
  } catch (error) {
    if (error.response?.status === 401) {
      console.log('✅ Correctly rejected unauthorized request:', error.response.status);
    } else {
      console.log('❌ Unexpected error:', error.response?.status, error.message);
    }
  }

  console.log('\n🏁 Test completed!');
}

// Run the tests
testEndpoints().catch(console.error);
