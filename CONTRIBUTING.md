# Contributing to LuckShack eCommerce Platform

Thank you for considering contributing to the LuckShack eCommerce Platform. This document outlines the process for contributing to the project and the standards we expect from contributors.

## Table of Contents

- [Code of Conduct](#code-of-conduct)
- [Getting Started](#getting-started)
- [Development Process](#development-process)
- [Pull Request Process](#pull-request-process)
- [Coding Standards](#coding-standards)
- [Testing](#testing)
- [Documentation](#documentation)
- [Security](#security)
- [Release Process](#release-process)
- [Communication](#communication)

## Code of Conduct

Our team is committed to providing a welcoming and inspiring community for all. We expect all contributors to adhere to our [Code of Conduct](CODE_OF_CONDUCT.md).

## Getting Started

### Prerequisites

- Node.js 16.x or higher
- npm 7.x or higher (or yarn 1.22.x)
- Git

### Setting Up Your Development Environment

1. Fork the repository on GitHub
2. Clone your fork locally:
   ```bash
   git clone https://github.com/YOUR-USERNAME/luckshack-ecommerce.git
   cd luckshack-ecommerce
   ```
3. Add the original repository as an upstream remote:
   ```bash
   git remote add upstream https://github.com/aarishcs2/luckshack-ecommerce.git
   ```
4. Install dependencies:
   ```bash
   npm install
   # or
   make install
   ```
5. Set up environment variables:
   ```bash
   cp .env.example .env.local
   ```
6. Start the development server:
   ```bash
   npm start
   # or
   make start
   ```

## Development Process

### Branching Strategy

We follow a trunk-based development workflow:

- `main` - Production-ready code
- `develop` - Integration branch for features
- `feature/*` - Feature branches
- `hotfix/*` - Urgent fixes for production
- `release/*` - Release candidate branches

### Workflow

1. Ensure you have the latest changes from the upstream repository:
   ```bash
   git checkout develop
   git pull upstream develop
   ```
2. Create a feature branch:
   ```bash
   git checkout -b feature/your-feature-name
   ```
3. Make your changes, following our coding standards
4. Commit your changes using [Conventional Commits](https://www.conventionalcommits.org/):
   ```bash
   git commit -m "feat(component): add new feature"
   ```
5. Push to your fork:
   ```bash
   git push origin feature/your-feature-name
   ```
6. Create a Pull Request against the `develop` branch

## Pull Request Process

1. Ensure your PR addresses a specific issue. If an issue doesn't exist, create one first.
2. Update the documentation to reflect any changes.
3. Add tests for new functionality.
4. Ensure the test suite passes and the application builds without errors.
5. Have at least one team member review and approve your changes.
6. Once approved, a maintainer will merge your PR.

### PR Title Format

Follow the [Conventional Commits](https://www.conventionalcommits.org/) format for PR titles:

```
<type>(<scope>): <description>
```

For example:
- `feat(orders): add filter by payment method`
- `fix(auth): resolve token refresh issue`
- `docs(api): update cache invalidation documentation`

## Coding Standards

### TypeScript/JavaScript

- Follow the [Airbnb JavaScript Style Guide](https://github.com/airbnb/javascript)
- Use TypeScript for all new code
- Maintain strict typing and avoid `any` except where absolutely necessary
- Document complex functions and components with JSDoc comments

### React Components

- Use functional components with hooks instead of class components
- Follow the container/presentational component pattern
- Keep components small and focused on a single responsibility
- Use React.memo for performance optimization where appropriate

### CSS/SCSS

- Follow the BEM (Block Element Modifier) methodology
- Use variables for colors, spacing, and other design tokens
- Keep selectors simple and avoid deep nesting

### Code Formatting

Your code should be formatted using our linting tools:

```bash
# Format code
npm run format

# Run linters
npm run lint:scss
```

## Testing

### Testing Requirements

All new features should include:

1. Unit tests for utility functions and small components
2. Integration tests for complex components
3. End-to-end tests for critical user flows

### Running Tests

```bash
# Run all tests
npm test

# Run tests for a specific file
npm test -- src/components/YourComponent.test.tsx

# Run tests with coverage
npm test -- --coverage
```

### Test Naming Conventions

- Test files should be named `*.test.tsx` or `*.test.ts`
- Test suites should describe the component or function being tested
- Test cases should describe the expected behavior

## Documentation

### Code Documentation

- Add JSDoc comments to all functions and components
- Include param and return type documentation
- Document complex logic with inline comments

### Feature Documentation

For significant features:

1. Update README-ENTERPRISE.md with an overview
2. Add detailed documentation in the `/docs` directory
3. Include usage examples and API documentation

## Security

### Security Considerations

- Never commit API keys, passwords, or other secrets
- Validate all user inputs
- Follow secure coding practices
- Report security vulnerabilities directly to the security <NAME_EMAIL>

## Release Process

1. Version bumps follow [Semantic Versioning](https://semver.org/)
2. Release notes are generated from commit messages
3. All releases require sign-off from the tech lead and QA

## Communication

- Use JIRA for tracking issues and features
- Discuss architectural changes in design documents
- Technical discussions should happen in pull request comments
- Use Slack for day-to-day communication

## Additional Resources

- [Architecture Overview](docs/architecture.md)
- [API Documentation](docs/api.md)
- [Style Guide](docs/style-guide.md)

Thank you for contributing to the LuckShack eCommerce Platform!

---

© 2025 SC Craft. All rights reserved.
