import React, { useState } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '../../../redux/store';
import { OrderDetails, Setting } from '../dashboard/adminDash';
import WalletSection from '../dashboard/wallet';
import OrderHistory from '../home/<USER>';
import AdminDashboard from './dashboard';
import MyGames from './myGame';
import Sidebar from './sidebar';
import SpinWheel from './wheel';

const UserDashboard: React.FC = () => {
  const { user } = useSelector((state: RootState) => state.auth);
  const [isSidebarOpen, setIsSidebarOpen] = useState<boolean>(false);
  const [userData, setUserData] = useState<any>(user);
  const [selectedOrderId, setSelectedOrderId] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<string>(
    localStorage.getItem('persistentActiveTab') || 'Dashboard'
  );
  const updateUserData = (newData: any) => {
    setUserData((prevState: any) => ({
      ...prevState,
      ...newData,
    }));
  };
  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
    localStorage.setItem('persistentActiveTab', tab);
  };

  const renderContent = (): React.ReactNode => {
    if (selectedOrderId) {
      return <OrderDetails orderId={selectedOrderId} onBack={() => setSelectedOrderId(null)} />;
    }
    switch (activeTab) {
      case 'Dashboard':
        return <AdminDashboard />;
      // case 'Request System':
      //   return <RequestGame />;
      case 'Wallet':
        return <WalletSection />;
      // case 'Billing':
      //   return <BillingAddress />;
      // case 'Order System':
      //   return <PurchaseCreditsForm />;
      // case 'Redeem':
      //   return <RedeemCreditForm />;
      case 'Transaction History':
        return <OrderHistory />;
      case 'Gold Coin Wheel':
        return <SpinWheel />;
      case 'My Games':
        return <MyGames />;
      case 'Settings':
        return <Setting userData={userData} updateUserData={updateUserData} />;
      default:
        return <AdminDashboard />; // Default to dashboard if tab not found
    }
  };

  return (
    <div className="flex flex-col lg:flex-row min-h-[100%] bg-white p-2">
      <Sidebar
        activeTab={activeTab}
        setActiveTab={handleTabChange}
        isOpen={isSidebarOpen}
        setIsOpen={setIsSidebarOpen}
      />
      <div className="flex-1 p-4 lg:p-6 lg:mt-0">{renderContent()}</div>
    </div>
  );
};

export default UserDashboard;
