import React, { useRef } from 'react';
import { DateRangePicker } from 'react-date-range';
import ErrorBoundary from '../../../../components/ErrorBoundary';
import { DateRange } from '../types/reports.types';

interface ReportsHeaderProps {
  activeTab: 'daily' | 'weekly' | 'monthly';
  dateRange: { startDate: Date; endDate: Date };
  showDatePicker: boolean;
  onTabChange: (tab: 'daily' | 'weekly' | 'monthly') => void;
  onDateRangeChange: (ranges: any) => void;
  onToggleDatePicker: () => void;
  datePickerButtonRef: React.RefObject<HTMLButtonElement>;
  datePickerRef: React.RefObject<HTMLDivElement>;
  datePickerStyles: string;
}

const ReportsHeader: React.FC<ReportsHeaderProps> = ({
  activeTab,
  dateRange,
  showDatePicker,
  onTabChange,
  onDateRangeChange,
  onToggleDatePicker,
  datePickerButtonRef,
  datePickerRef,
  datePickerStyles
}) => {
  return (
    <>
      {/* Add custom styles for date picker */}
      <style>{datePickerStyles}</style>

      {/* Header Section */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <h2 className="text-xl sm:text-2xl font-bold text-gray-800">Reports Dashboard</h2>

        {/* Filter Controls - Responsive layout */}
        <div className="flex flex-col w-full md:w-auto gap-3 bg-white p-3 rounded-lg shadow-sm">
          {/* Time period filters */}
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => onTabChange('daily')}
              className={`flex-1 md:flex-none px-3 py-2 text-sm rounded-lg ${
                activeTab === 'daily'
                  ? 'bg-[#495e26] text-white'
                  : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
              } transition-colors`}
            >
              Daily
            </button>
            <button
              onClick={() => onTabChange('weekly')}
              className={`flex-1 md:flex-none px-3 py-2 text-sm rounded-lg ${
                activeTab === 'weekly'
                  ? 'bg-[#495e26] text-white'
                  : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
              } transition-colors`}
            >
              Weekly
            </button>
            <button
              onClick={() => onTabChange('monthly')}
              className={`flex-1 md:flex-none px-3 py-2 text-sm rounded-lg ${
                activeTab === 'monthly'
                  ? 'bg-[#495e26] text-white'
                  : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
              } transition-colors`}
            >
              Monthly
            </button>
            <div className="relative flex-1 md:flex-none">
              <button
                onClick={onToggleDatePicker}
                ref={datePickerButtonRef}
                data-testid="date-picker-button"
                aria-label="Select date range"
                aria-expanded={showDatePicker}
                className="w-full md:w-auto px-3 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors flex items-center justify-center md:justify-start gap-2"
              >
                <svg className="w-4 h-4 text-[#495e26]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                <span className="text-sm">
                  {dateRange.startDate.toLocaleDateString()} - {dateRange.endDate.toLocaleDateString()}
                </span>
              </button>
              {showDatePicker && (
                <div className="absolute left-0 mt-2 z-20 bg-white shadow-md rounded-lg border border-gray-100 w-full sm:w-auto">
                  {/* Custom styling for mobile date picker */}
                  <div ref={datePickerRef} className="date-picker-container w-full max-w-[calc(100vw-20px)] sm:w-[280px] overflow-x-auto" role="dialog" aria-modal="true" aria-label="Date range picker">
                    {/* Wrap DateRangePicker in error boundary */}
                    <ErrorBoundary>
                      <DateRangePicker
                        ranges={[{
                          startDate: dateRange.startDate,
                          endDate: dateRange.endDate,
                          key: 'selection'
                        }]}
                        onChange={onDateRangeChange}
                        months={1}
                        direction="horizontal"
                        className="custom-date-picker"
                        weekdayDisplayFormat="E"
                        weekStartsOn={1}
                        showMonthAndYearPickers={true}
                        showDateDisplay={false}
                        monthDisplayFormat="MMM yyyy"
                        dayDisplayFormat="d"
                      />
                    </ErrorBoundary>
                  </div>
                  <div className="p-3 flex justify-end">
                    <button
                      onClick={onToggleDatePicker}
                      className="px-3 py-2 bg-[#495e26] text-white text-sm rounded-lg"
                    >
                      Apply
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ReportsHeader;
