import React, { useState, useEffect } from 'react';
import axios from 'axios';
import {
  FaHistory,
  FaCog,
  FaSignOutAlt,
  FaChevronLeft,
  FaChevronRight,
  FaBars,
  FaTimes,
} from 'react-icons/fa';
interface Order {
  _id: string;
  userId: {
    firstName: string;
    lastName: string;
  };
  price: string;
  dateTime: string | null;
  createdAt: string;
  productId: string | null;
  quantity: number;
  status: boolean;
}
interface OrderHistoryProps {
  onViewDetails: (orderId: string) => void;
}

const OrderHistory: React.FC<OrderHistoryProps> = ({ onViewDetails }) => {
  const [orderData, setOrderData] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const ordersPerPage: number = 5;

  const userId = localStorage.getItem('userId');

  const fetchOrderDetails = async () => {
    try {
      const token = localStorage.getItem('Token');
      const response = await axios.get(`${process.env.REACT_APP_API_URL}/orders/user/${userId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
      });
      setOrderData(response.data.data);
      setError(null);
    } catch (error: any) {
      console.error('Error fetching orders:', error);
      if (error.response?.data?.error === 'No orders found for this user') {
        setOrderData([]);
      } else {
        setError('Failed to load orders');
      }
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchOrderDetails();
  }, []);

  const totalPages: number = Math.ceil(orderData.length / ordersPerPage);
  const indexOfLastOrder: number = currentPage * ordersPerPage;
  const indexOfFirstOrder: number = indexOfLastOrder - ordersPerPage;
  const currentOrders: Order[] = orderData.slice(indexOfFirstOrder, indexOfLastOrder);

  const paginate = (pageNumber: number): void => setCurrentPage(pageNumber);

  const pageNumbers: number[] = Array.from({ length: totalPages }, (_, i) => i + 1);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#4D774E]"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center text-red-600 p-4">
        {error}
        <button onClick={fetchOrderDetails} className="ml-2 text-[#4D774E] hover:underline">
          Retry
        </button>
      </div>
    );
  }
  return (
    <div className="bg-white rounded-lg shadow-md p-4 lg:p-6 overflow-x-auto">
      <h2 className="text-xl lg:text-2xl mb-4 lg:mb-6">ORDER HISTORY</h2>
      {orderData.length === 0 ? (
        <div className="text-gray-500 text-center">Order not available</div>
      ) : (
        <>
          <table className="w-full min-w-[640px]">
            <thead>
              <tr className="border-b">
                <th className="text-left py-2">ORDER ID</th>
                <th className="text-left py-2">USER NAME</th>
                <th className="text-left py-2">DATE</th>
                <th className="text-left py-2">TOTAL</th>
                <th className="text-left py-2">ACTION</th>
              </tr>
            </thead>
            <tbody>
              {orderData.map((order, index) => (
                <tr key={index} className="border-b">
                  <td className="py-2">{order._id}</td>
                  {/*} <td className="py-2">{`${order.userId.firstName} ${order.userId.lastName}`}</td> */}
                  <td className="py-2">
                    {order.userId && (order.userId.firstName || order.userId.lastName)
                      ? `${order.userId.firstName || ''} ${order.userId.lastName || ''}`.trim()
                      : ''}
                  </td>

                  <td className="py-2">
                    {order.dateTime ? new Date(order.dateTime).toLocaleDateString() : 'N/A'}
                  </td>
                  <td className="py-2">{order.price} </td>
                  <td className="py-2">
                    <button
                      onClick={() => onViewDetails(order._id)}
                      className="text-blue-500 hover:underline"
                    >
                      View Details →
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </>
      )}
      <div className="flex justify-center items-center mt-4 lg:mt-6 space-x-2">
        <button
          className="p-2 rounded-full bg-gray-200 hover:bg-gray-300 disabled:opacity-50"
          onClick={() => paginate(currentPage > 1 ? currentPage - 1 : 1)}
          disabled={currentPage === 1}
        >
          <FaChevronLeft size={20} />
        </button>
        {pageNumbers.map((number) => (
          <button
            key={number}
            onClick={() => paginate(number)}
            className={`w-8 h-8 rounded-full ${
              currentPage === number ? 'bg-[#4D774E] text-white' : 'bg-gray-200 hover:bg-gray-300'
            } flex items-center justify-center`}
          >
            {String(number).padStart(2, '0')}
          </button>
        ))}
        <button
          className="p-2 rounded-full bg-gray-200 hover:bg-gray-300 disabled:opacity-50"
          onClick={() => paginate(currentPage < totalPages ? currentPage + 1 : totalPages)}
          disabled={currentPage === totalPages}
        >
          <FaChevronRight size={20} />
        </button>
      </div>
    </div>
  );
};
export default OrderHistory;
