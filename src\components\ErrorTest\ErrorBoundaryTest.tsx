import React, { useState, useEffect } from 'react';
import ReactErrorBoundary from '../ErrorBoundary/ReactErrorBoundary';
import useErrorHandler from '../../hooks/useErrorHandler';
import { useSafeEffect } from '../../utils/errorHandling';

// Component that will throw an error
const BuggyCounter: React.FC = () => {
  const [counter, setCounter] = useState(0);
  const { handleError, throwError, hasError } = useErrorHandler();
  const errorThrown = React.useRef(false);

  const handleClick = () => {
    try {
      // Increment counter
      const newCounter = counter + 1;

      // Check if counter would reach 5
      if (newCounter === 5) {
        // Use our error handler to handle the error
        handleError(new Error('Simulated error: Counter reached 5!'));
        errorThrown.current = true;
      } else {
        setCounter(newCounter);
      }
    } catch (error) {
      // This will catch any other errors that might occur
      handleError(error);
      errorThrown.current = true;
    }
  };

  // If there's an error, throw it during render so error boundaries can catch it
  // But only throw it once to prevent infinite loops
  if (hasError && !errorThrown.current) {
    errorThrown.current = true;
    throwError();
  }

  return (
    <div className="p-4 border rounded-md">
      <p className="mb-2">Counter: {counter}</p>
      <button
        onClick={handleClick}
        className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
      >
        Increment Counter
      </button>
      <p className="mt-2 text-sm text-gray-600">
        (This component will crash when counter reaches 5)
      </p>
    </div>
  );
};

// Component that will throw an error during rendering
const RenderErrorComponent: React.FC = () => {
  // Use a ref to track if we've already thrown an error
  const hasThrown = React.useRef(false);
  const { handleError, hasError } = useErrorHandler();

  // Only throw the error once to prevent infinite loops
  if (!hasThrown.current && !hasError) {
    hasThrown.current = true;
    try {
      // This will cause an error immediately during rendering
      throw new Error('Simulated error during component rendering!');
    } catch (error) {
      // Handle the error in a controlled way
      handleError(error);
    }
  }

  // If we've already handled the error, just render null
  return null;
};

// Component that will throw an error in useEffect
const EffectErrorComponent: React.FC = () => {
  const { handleError, throwError } = useErrorHandler();
  const [hasError, setHasError] = useState(false);

  // Use our safe effect hook to handle errors properly
  useSafeEffect(() => {
    // This will cause an error after component mounts
    throw new Error('Simulated error in useEffect!');
  }, []);

  // Also use a regular effect to handle the error for our demo
  useEffect(() => {
    try {
      // Simulate the error again, but catch it this time
      throw new Error('Simulated error in useEffect!');
    } catch (error) {
      // Handle the error in a controlled way
      handleError(error);
      setHasError(true);
    }
  }, [handleError]);

  // If there's an error, throw it during render
  if (hasError) {
    throwError();
  }

  return <div>This component throws an error in useEffect</div>;
};

// Component that will throw an async error
const AsyncErrorComponent: React.FC = () => {
  const { handleError, throwError } = useErrorHandler();
  const [isLoading, setIsLoading] = useState(true);
  const errorProcessed = React.useRef(false);

  // Use our safe effect hook to handle the async error
  useSafeEffect(() => {
    // Prevent the error from being processed multiple times
    if (errorProcessed.current) return;

    // Override console.error specifically for this component
    const originalConsoleError = console.error;
    console.error = (...args) => {
      // Check if this is our simulated async error
      const isAsyncError = args.some(arg =>
        (arg instanceof Error && arg.message.includes('Simulated async error')) ||
        (typeof arg === 'string' && arg.includes('Simulated async error'))
      );

      if (isAsyncError) {
        // Don't log the error to console
        return;
      }

      // Pass through other errors
      originalConsoleError.apply(console, args);
    };

    // Simulate an async operation that fails
    const fetchData = async () => {
      try {
        await new Promise(resolve => setTimeout(resolve, 1000));
        throw new Error('Simulated async error!');
      } catch (error) {
        // Mark the error as processed
        errorProcessed.current = true;
        // Use our error handler to handle the error
        handleError(error);
      } finally {
        setIsLoading(false);
        // Restore console.error
        console.error = originalConsoleError;
      }
    };

    fetchData();

    // Cleanup function
    return () => {
      console.error = originalConsoleError;
    };
  }, [handleError]);

  // If there's an error, throw it during render so error boundaries can catch it
  // But only do this once we're done loading to prevent premature errors
  if (!isLoading) {
    throwError();
  }

  return (
    <div>
      {isLoading ? 'Loading...' : 'This component throws an async error'}
    </div>
  );
};

// Main test component that allows testing different error scenarios
const ErrorBoundaryTest: React.FC = () => {
  const [showRenderError, setShowRenderError] = useState(false);
  const [showEffectError, setShowEffectError] = useState(false);
  const [showAsyncError, setShowAsyncError] = useState(false);

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Error Boundary Testing</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="p-4 border rounded-md">
          <h2 className="text-xl font-semibold mb-4">Test 1: Counter Error</h2>
          <p className="mb-4 text-gray-700">
            This test demonstrates an error that occurs during a state update.
            The error boundary should catch this and show a fallback UI.
          </p>
          <ReactErrorBoundary
            componentName="BuggyCounter"
            showDetails={process.env.NODE_ENV === 'development'}
          >
            <BuggyCounter />
          </ReactErrorBoundary>
        </div>

        <div className="p-4 border rounded-md">
          <h2 className="text-xl font-semibold mb-4">Test 2: Render Error</h2>
          <p className="mb-4 text-gray-700">
            This test demonstrates an error that occurs during initial rendering.
            The error boundary should catch this and show a fallback UI.
          </p>
          <button
            onClick={() => setShowRenderError(true)}
            className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
          >
            Show Component with Render Error
          </button>
          {showRenderError && (
            <ReactErrorBoundary
              componentName="RenderErrorComponent"
              showDetails={process.env.NODE_ENV === 'development'}
            >
              <RenderErrorComponent />
            </ReactErrorBoundary>
          )}
        </div>

        <div className="p-4 border rounded-md">
          <h2 className="text-xl font-semibold mb-4">Test 3: Effect Error</h2>
          <p className="mb-4 text-gray-700">
            This test demonstrates an error that occurs in a useEffect hook.
            The error boundary should catch this and show a fallback UI.
          </p>
          <button
            onClick={() => setShowEffectError(true)}
            className="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600"
          >
            Show Component with Effect Error
          </button>
          {showEffectError && (
            <ReactErrorBoundary
              componentName="EffectErrorComponent"
              showDetails={process.env.NODE_ENV === 'development'}
            >
              <EffectErrorComponent />
            </ReactErrorBoundary>
          )}
        </div>

        <div className="p-4 border rounded-md">
          <h2 className="text-xl font-semibold mb-4">Test 4: Async Error</h2>
          <p className="mb-4 text-gray-700">
            This test demonstrates an error that occurs in an async operation.
            Note: React error boundaries don't catch these by default!
          </p>
          <button
            onClick={() => setShowAsyncError(true)}
            className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600"
          >
            Show Component with Async Error
          </button>
          {showAsyncError && (
            <ReactErrorBoundary
              componentName="AsyncErrorComponent"
              showDetails={process.env.NODE_ENV === 'development'}
            >
              <AsyncErrorComponent />
            </ReactErrorBoundary>
          )}
        </div>
      </div>
    </div>
  );
};

export default ErrorBoundaryTest;
