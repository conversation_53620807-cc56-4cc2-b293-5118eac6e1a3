import React, { useState, useEffect } from 'react';
import previous from '../../../assets/prv.svg';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';

interface Product {
  _id: string;
  gameName: string;
  /* description: string;
  imageName: string;
  playLink: string;
  fields: string[]; */
}

const PurchaseCreditsForm = () => {
  const userId = localStorage.getItem('userId');
  const navigate = useNavigate();
  const [products, setProducts] = useState<Product[]>([]);
  const [requestedSystemIds, setRequestedSystemIds] = useState<string[]>([]);
  const [formValues, setFormValues] = useState({
    email: '',
    system: '',
    price: '',
    gameRequestId: '',
  });
  const [errors, setErrors] = useState({
    email: '',
    price: '',
    system: '',
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;

    setErrors((prevErrors) => ({
      ...prevErrors,
      [name]: '', // Clear the error message for the current field
    }));

    setFormValues({ ...formValues, [name]: value });
  };

  const validateForm = () => {
    const newErrors: any = {};

    Object.keys(formValues).forEach((field) => {
      if (!formValues[field as keyof typeof formValues]) {
        newErrors[field] = 'This field is required';
      }
    });

    if (parseFloat(formValues.price) < 10) {
      newErrors.price = 'Price must be at least $10';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const checkKYCStatus = async () => {
    try {
      const response = await axios.get(`${process.env.REACT_APP_API_URL}/user/kyc-status/${userId}`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('Token')}`,
        },
      });
      return response.data.isVerified;
    } catch (error) {
      console.error('Error checking KYC status:', error);
      return false;
    }
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      // Check KYC status before proceeding
      const isKYCVerified = await checkKYCStatus();

      if (!isKYCVerified) {
        // Store current form values in localStorage to restore after KYC
        localStorage.setItem('pendingCreditPurchase', JSON.stringify(formValues));
        navigate('/kyc-verify');
        return;
      }

      const orderData = {
        userId: userId,
        email: formValues.email,
        price: formValues.price,
        system: formValues.system,
        date: new Date().toISOString(),
        gameRequestId: formValues.gameRequestId,
        paymentMethod: 'ForumPay',
        //  item_name: "Test_Item"
      };


      const response = await axios.post(`${process.env.REACT_APP_API_URL}/order/add`, orderData, {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          Authorization: `Bearer ${localStorage.getItem('Token')}`,
        },
      });

      localStorage.setItem('email', formValues.email);

      if (response.data?.access_url) {
        const accessUrl = response.data.access_url;
        window.location.href = accessUrl;
      } else {
        alert('Payment URL not available. Please try again.');
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      alert('Error submitting form. Please try again.');
    }
  };

  const fetchProduct = async () => {
    try {
      const response = await axios.get(`${process.env.REACT_APP_API_URL}/game-requests`, {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          Authorization: `Bearer ${localStorage.getItem('Token')}`,
        },
      });
      setProducts(response.data.data);
    } catch (error: any) {
      console.error('Error fetching product :', error);
    }
  };

  useEffect(() => {
    const fetchUserEmail = async () => {
      if (userId) {
        try {
          const response = await axios.get(`${process.env.REACT_APP_API_URL}/user/${userId}`, {
            headers: {
              Accept: 'application/json',
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*',
              Authorization: `Bearer ${localStorage.getItem('Token')}`,
            },
          });
          const email = response.data.data.email;

          setFormValues((prevValues) => ({
            ...prevValues,
            email,
          }));
        } catch (error) {
          console.error('Error fetching user email:', error);
        }
      }
    };
    fetchUserEmail();
    fetchProduct();
  }, [userId]);

  return (
    <div className="w-full h-full flex items-center justify-center bg-white rounded-lg  p-4">
      <div className="w-full max-w-md">
        {/* Header Section */}
        <div className="flex items-center mb-6">
          <button className="mr-4" onClick={() => window.history.back()}>
            <img src={previous} alt="Previous" className="w-10 h-10" />
          </button>
          <h2 className="text-2xl font-bold text-gray-900 leading-tight">
            HOW TO PURCHASE CREDITS
          </h2>
        </div>

        {/* Form Section */}
        <form onSubmit={handleSubmit} className="w-full h-full">
          {/* Email */}
          <div className="mb-4">
            <label className="block text-xl font-bold text-red-600" htmlFor="email">
              Email : Please use this email id at web checkout
            </label>
            <input
              type="email"
              id="email"
              name="email"
              value={formValues.email}
              disabled
              className="mt-2 w-full border rounded-md px-3 py-2 text-xl bg-[#c9cfbe] text-lime-900 shadow-sm border-gray-300"
            />
          </div>

          {/* System */}
          <div className="mb-4">
            <label className="block text-xl font-bold text-red-600" htmlFor="system">
              System : Only requested games are shown in the dropdown.
            </label>
            <select
              id="system"
              name="system"
              value={formValues.system}
              //  onChange={handleChange}
              onChange={(e) => {
                const selectedOption = products.find(
                  (product) => product.gameName === e.target.value
                );
                setFormValues({
                  ...formValues,
                  system: e.target.value,
                  gameRequestId: selectedOption?._id || '',
                });
              }}
              className="mt-2 w-full border rounded-md px-3 py-2 text-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-lime-900 bg-white border-gray-300"
            >
              <option value="">Select a system...</option>
              {products
                //  .filter((product) => requestedSystemIds.includes(product._id))
                ?.map((product: any) => (
                  <option key={product.id} value={product.name}>
                    {product.gameName}
                  </option>
                ))}
            </select>
            {errors.system && <p className="text-red-500 mt-1">{errors.system}</p>}
          </div>

          {/* Select Price */}
          <div className="mb-4">
            <label className="block text-xl font-normal text-lime-900" htmlFor="price">
              Select Price
            </label>
            <input
              type="number"
              id="price"
              name="price"
              value={formValues.price}
              onChange={handleChange}
              placeholder="Enter price"
              className="mt-2 w-full border rounded-md px-3 py-2 text-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-lime-900 border-gray-300"
            />
            {errors.price && <p className="text-red-500 mt-1">{errors.price}</p>}
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            className="w-full rounded-md bg-lime-900 py-3 text-xl font-normal text-white shadow-lg hover:bg-lime-800 focus:outline-none focus:ring-2 focus:ring-lime-900"
          >
            SUBMIT
          </button>
        </form>
      </div>
    </div>
  );
};

export default PurchaseCreditsForm;
