import React, { FC, useState, useEffect, useRef } from 'react';
import Select from 'react-select';

import axios from 'axios';
import { useNavigate, useSearchParams } from 'react-router-dom';
import moment from 'moment';
import classNames from 'classnames';
import Pagination, { PaginationItem } from '../../../components/bootstrap/Pagination';
import dayjs from 'dayjs';
import { FormikHelpers, useFormik } from 'formik';
import Modal, {
  ModalBody,
  ModalFooter,
  ModalHeader,
  ModalTitle,
} from '../../../components/bootstrap/Modal';
import Card, {
  CardActions,
  CardBody,
  CardHeader,
  CardLabel,
  CardTitle,
} from '../../../components/bootstrap/Card';
import Button from '../../../components/bootstrap/Button';
import { priceFormat } from '../../../helpers/helpers';
import Dropdown, {
  DropdownItem,
  DropdownMenu,
  DropdownToggle,
} from '../../../components/bootstrap/Dropdown';
//import Dropdown, { DropdownToggle, DropdownMenu, DropdownItem } from '../../../components/bootstrap/Dropdown'; // replace 'reactstrap' with your actual dropdown library
import Icon from '../../../components/icon/Icon';
import OffCanvas, {
  OffCanvasBody,
  OffCanvasHeader,
  OffCanvasTitle,
} from '../../../components/bootstrap/OffCanvas';
import FormGroup from '../../../components/bootstrap/forms/FormGroup';
import Input from '../../../components/bootstrap/forms/Input';
import Textarea from '../../../components/bootstrap/forms/Textarea';
import Checks from '../../../components/bootstrap/forms/Checks';
import Popovers from '../../../components/bootstrap/Popovers';
import data from '../../../common/data/dummyEventsData';
import USERS from '../../../common/data/userDummyData';
import EVENT_STATUS from '../../../common/data/enumEventStatus';
import Avatar from '../../../components/Avatar';
import PaginationButtons, {
  dataPagination,
  PER_COUNT,
} from '../../../components/PaginationButtons';
import SubHeader, { SubHeaderLeft, SubHeaderRight } from '../../../layout/SubHeader/SubHeader';
import CommonStoryBtn from '../../../common/other/CommonStoryBtn';
import useSortableData from '../../../hooks/useSortableData';
import useDarkMode from '../../../hooks/useDarkMode';
import { useParams } from 'react-router-dom';
import { ifError } from 'assert';
import { Margin } from '../../../components/icon/material-icons';
import { left } from '@popperjs/core';
import style from 'react-syntax-highlighter/dist/esm/styles/hljs/a11y-dark';
import { ColorButton } from '../../../stories/components/bootstrap/Button/ButtonUseIsActive.stories';
import { color } from 'framer-motion';
import { TModalFullScreen, TModalSize } from '../../../type/modal-type';
import PageWrapper from '../../../layout/PageWrapper/PageWrapper';
import 'bootstrap/dist/css/bootstrap.min.css';
import { relative } from 'path';
import { userInfo } from 'os';

interface ICommonUpcomingEventsProps {
  isFluid?: boolean;
}
const Transactions: FC<ICommonUpcomingEventsProps> = ({ isFluid }) => {
  const { themeStatus, darkModeStatus } = useDarkMode();
  // BEGIN :: Upcoming Events

  const [upcomingEventsInfoOffcanvas, setUpcomingEventsInfoOffcanvas] = useState(false);
  const [statusLogOffcanvas, setStatusLogOffcanvas] = useState(false);

  const [transaction, setTransaction] = useState<any>({});
  const [statusLog, setStatusLog] = useState<any>({});

  const handleUpcomingDetails = (transaction: any) => {
    setTransaction(transaction);
    setUpcomingEventsInfoOffcanvas(!upcomingEventsInfoOffcanvas);
  };
  const [upcomingEventsEditOffcanvas, setUpcomingEventsEditOffcanvas] = useState(false);
  const handleUpcomingEdit = () => {
    setUpcomingEventsEditOffcanvas(!upcomingEventsEditOffcanvas);
  };
  // END :: Upcoming Events

  const navigate = useNavigate();

  const { items, requestSort, getClassNamesFor } = useSortableData(data);
  const [transactionData, setTransactionData] = useState<any[]>([]);
  const [page, setPage] = useState(1);
  const loaderRef = useRef<HTMLDivElement>(null);
  const [loading, setLoading] = useState(false);
  const [transactions, setTransactions] = useState<any[]>([]);
  const [hasMore, setHasMore] = useState(true);
  const [updateStatus, setUpdateStatus] = useState<any[]>([]);
  const [transactionStatus, setTransactionStatus] = useState<any>([]);
  const [newStatus, setNewStatus] = useState<any>();
  const [state, setState] = useState(false);
  const [staticBackdropStatus, setStaticBackdropStatus] = useState(false);
  const [scrollableStatus, setScrollableStatus] = useState(false);
  const [centeredStatus, setCenteredStatus] = useState(false);
  const [sizeStatus, setSizeStatus] = useState<TModalSize>(null);
  const [fullScreenStatus, setFullScreenStatus] = useState<TModalFullScreen | undefined>(undefined);
  const [animationStatus, setAnimationStatus] = useState(true);
  const [headerCloseStatus, setHeaderCloseStatus] = useState(true);
  const [sortingOrder, setSortingOrder] = useState('desc'); // Default: newest first
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearch, setIsSearch] = useState(false);
  const [isSortingChanged, setIsSortingChanged] = useState(false);
  const [activeTab, setActiveTab] = useState('Pending');
  const [token, setToken] = useState('');
  const [selectedOption, setSelectedOption] = useState<any>([]);
  const [expiredEmail, setExpiredEmail] = useState<any[]>([]);
  const [systemError, setSystemError] = useState('');
  const [averageProcessedTime, setAverageProcessedTime] = useState('');
  const [statusReason, setStatusReason] = useState({
    status_reason: '',
  });
  const [statusReasonError, setStatusReasonError] = useState({
    status_reason: '',
    system: '',
  });
  const statusLogDetails = (statusLog: any) => {
    setStatusLog(statusLog);
    setStatusLogOffcanvas(!statusLogOffcanvas);
  };
  const openCloseModal = async (
    status: any,
    id: any,
    modelStatus: boolean,
    newStatus: any,
    optionValue: any
  ) => {
    setState(modelStatus);
    if (id !== null) {
      setTransactionStatus({
        id: id,
        status: status,
        newStatus: newStatus,
        system: optionValue,
      });
    } else {
      setTransactionStatus({});
    }
  };
  const handleSearchInputChange = (e: any) => {
    e.preventDefault();
    setSearchQuery(e.target.value);
  };
  const handleSearch = async () => {
    const trimmedQuery = searchQuery.trim();
    setPage(1);
    if (trimmedQuery != '') {
      setSearchQuery(searchQuery);
      setIsSearch(true);
    } else {
      setSearchQuery('');
      setIsSearch(false);
    }
    setIsSortingChanged(true);
  };
  const handleChange = (e: any) => {
    const name = e.target.name;
    const value = e.target.value;
    setStatusReason({ ...statusReason, [name]: value });
    setStatusReasonError({
      ...statusReasonError,
      [name]: '',
    });
  };
  const statusUpdate = async (e: any) => {
    e.preventDefault();
    let status = transactionStatus.status;
    let id = transactionStatus.id;
    const errors: any = {};
    if (status !== 'Pending' && !statusReason.status_reason) {
      errors.status_reason = 'Reason is required';
    }
    if (!transactionStatus.system) {
      errors.system = 'System is required.';
    }
    if (Object.keys(errors).length > 0) {
      setStatusReasonError(errors);
      return;
    }
    try {
      let statusData = {
        status: transactionStatus.newStatus,
        reason: statusReason.status_reason,
        system: transactionStatus.system,
        userName: transactionStatus.userName,
      };
      console.log('statusData==', statusData);

      const response = await axios.put(
        process.env.REACT_APP_API_URL + '/transaction/' + id,
        statusData,
        {
          headers: {
            Accept: 'application/json',
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            Authorization: `Bearer ${token}`,
          },
        }
      );
      const newResponse = response.data.data;

      if (newResponse) {
        setPage(1);
        setIsSortingChanged(true);
        setStatusReason({ status_reason: '' });
      }
      setState(false);
      setTransactionStatus({});
    } catch (error) {
    }
  };
  const changeDateFormat = (dateObject: Date) => {
    const targetTimezone = 'America/Chicago';

    const utcDate = new Date(dateObject);
    const localDate = new Date(utcDate.toLocaleString('en-US', { timeZone: targetTimezone }));

    const formattedDate = localDate.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
    });
    return formattedDate;
  };
  const handleSortButton = async () => {
    const newSortingOrder = sortingOrder === 'asc' ? 'desc' : 'asc';
    setHasMore(true);
    setSortingOrder(newSortingOrder);
    setIsSortingChanged(true);
    setPage(1);
  };
  const handleTabChange = (tab: any) => {
    setLoading(false);
    setTransactionData([]);
    setActiveTab(tab);
    setPage(1);
    setIsSortingChanged(true);
  };

  const handleSelectedOption = (option: any) => {
    setSelectedOption({ option: option });
    setTransactionStatus({ ...transactionStatus, system: option });
    setStatusReasonError({ ...statusReasonError, system: '' });
  };
  const fetchData = async () => {
    try {
      if (!loading) {
        setLoading(true);
        const token = localStorage.getItem('Token') || '';
        setToken(token);
        let url =
          process.env.REACT_APP_API_URL +
          `/transactions?transactionType=Received&pageNumber=${page}&sortingOrder=${sortingOrder}`;
        url += `&status=${activeTab}`;
        if (isSearch === true) {
          url = url + `&keyWord=${searchQuery}`;
        }
        const response = await axios.get(url, {
          headers: {
            Accept: 'application/json',
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            Authorization: `Bearer ${token}`,
          },
        });
        const newData = response.data.data;
        console.log('newData=====', newData);
        const expiredTokens = response.data.tokens;
        if (expiredTokens && Array.isArray(expiredTokens)) {
          const emails = expiredTokens
            .filter((token: any) => token !== null && token.email)
            .map((token: any) => token.email);
          setExpiredEmail(emails);
        }

        if (newData.length === 0) {
          setHasMore(false);
          if (isSortingChanged === true) {
            setTransactionData([]);
          }
        } else {
          if (newData.length >= 10) {
            setHasMore(true);
          }
          if (isSortingChanged == true) {
            setTransactionData(([]) => [...newData]);
          } else {
            setTransactionData((prevData) => [...prevData, ...newData]);
          }
          setPage((prevPage) => prevPage + 1);
        }
      }
    } catch (error: any) {
      console.error('Error fetching data:', error);
      if (error.response && error.response.status === 401) {
        navigate('/');
      }
      /*  const statusCode = error.response.status;
              if (statusCode === 401) {
                  navigate('/');
              }*/
    } finally {
      setTimeout(() => setLoading(false), 1000);
    }
  };
  const fetchAverageProcessedTime = async () => {
    console.log('insiht fetchAveragePros');
    try {
      const response = await axios.get(
        `${process.env.REACT_APP_API_URL}/transaction/average-processed-time`,
        {
          headers: {
            Accept: 'application/json',
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`,
          },
        }
      );
      console.log('Response-----', response.data.averageProcessedTimeLast30Days);
      setAverageProcessedTime(response.data.averageProcessedTimeLast30Days);

      //    console.log("Response-----", response.data.averageProcessedTime);
      //    setAverageProcessedTime(response.data.averageProcessedTime);
    } catch (error) {
      console.error('Error fetching average processed time:', error);
    }
  };
  useEffect(() => {
    fetchAverageProcessedTime();
  }, []);
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && !loading && hasMore) {
          fetchData();
        }
      },
      { threshold: 1 }
    );
    if (loaderRef.current) {
      observer.observe(loaderRef.current);
    }
    if (isSortingChanged === true) {
      fetchData();
      setIsSortingChanged(false);
    }
    return () => observer.disconnect();
  }, [page, loading, hasMore, activeTab, isSortingChanged]);
  useEffect(() => {
    const intervalId = setInterval(() => {
      console.log('Activetab---', activeTab);
      if (activeTab === 'Pending') {
        setTransactionData([]);
        fetchData();
      }
    }, 60000);
    return () => clearInterval(intervalId);
  }, [activeTab]);

  const tabStyle = {
    fontWeight: 'bold',
    flex: '1',
    padding: '10px',
    cursor: 'pointer',
    border: '1px solid black',
    borderRadius: '5px 5px 0 0',
  };

  const activeTabStyle = {
    ...tabStyle,
    backgroundColor: 'black',
    color: 'white',
  };

  const inactiveTabStyle = {
    ...tabStyle,
    backgroundColor: '#f0f0f0',
    color: 'black',
  };
  const getColorForStatus = (status: any) => {
    switch (status) {
      case 'Pending':
        return 'warning';
      case 'Processed':
        return 'success';
      case 'Refunded':
        return 'info';
      case 'Canceled':
        return 'danger';
      default:
        return 'danger';
    }
  };
  const scrollingTextStyle: React.CSSProperties = {
    display: 'inline-block',
    whiteSpace: 'nowrap',
    animation: 'scroll-left 10s linear infinite',
  };

  const keyframes = `
    @keyframes scroll-left {
        0% { transform: translateX(0); }
        100% { transform: translateX(-100%); }
    }
    `;

  const styleSheet = document.createElement('style');
  styleSheet.type = 'text/css';
  styleSheet.innerText = keyframes;
  document.head.appendChild(styleSheet);
  return (
    <PageWrapper>
      <SubHeader>
        <SubHeaderLeft>
          <label className="border-0 bg-transparent cursor-pointer me-0" htmlFor="searchInput">
            <Icon icon="Search" size="2x" color="primary" />
          </label>
          <Input
            id="searchInput"
            type="search"
            className="border-0 shadow-none bg-transparent"
            placeholder="Search..."
            value={searchQuery}
            onChange={handleSearchInputChange}
            onBlur={handleSearch}
            onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
          />
        </SubHeaderLeft>
      </SubHeader>
      <div style={{ color: 'red' }}>
        <h5>Expired Gmails Tokens : </h5>
        {expiredEmail.length > 0 ? (
          <div className="scrolling-container">
            <h5 style={scrollingTextStyle}>{expiredEmail.join(',')}</h5>
          </div>
        ) : (
          <div>Not found</div>
        )}
      </div>
      <Card style={{ position: 'relative', zIndex: '1' }}>
        <CardHeader borderSize={1}>
          <CardLabel>
            <CardTitle tag="div">
              <span style={{ fontSize: '22px' }} className="h5">
                Transactions
              </span>
              <span
                style={{
                  marginLeft: '30px',
                  fontSize: '1 rem',
                  fontWeight: 'normal',
                }}
              >
                Average Processed Time : {averageProcessedTime} minutes
              </span>
            </CardTitle>
          </CardLabel>
          <Button icon="Sort" onClick={handleSortButton}>
            {sortingOrder === 'asc' ? 'Sort by Newest' : 'Sort by Oldest'}
          </Button>
        </CardHeader>

        <div style={{ display: 'flex', width: '100%' }}>
          <div style={{ display: 'flex', width: '100%' }}>
            <div
              style={{
                ...(activeTab === 'Pending' ? activeTabStyle : inactiveTabStyle),
              }}
              onClick={() => handleTabChange('Pending')}
            >
              Pending Transactions
            </div>
            <div
              style={{
                ...(activeTab === 'Processed' ? activeTabStyle : inactiveTabStyle),
              }}
              onClick={() => handleTabChange('Processed')}
            >
              Processed Transactions
            </div>
            <div
              style={{
                ...(activeTab === 'Refunded' ? activeTabStyle : inactiveTabStyle),
              }}
              onClick={() => handleTabChange('Refunded')}
            >
              Refunded Transactions
            </div>
            <div
              style={{
                ...(activeTab === 'Canceled' ? activeTabStyle : inactiveTabStyle),
              }}
              onClick={() => handleTabChange('Canceled')}
            >
              Canceled Transactions
            </div>
          </div>
        </div>
        <CardBody className="table-responsive" isScrollable={isFluid}>
          <table className="table table-modern">
            <thead>
              <tr>
                <th>Sr.no.</th>
                <td aria-labelledby="Image" style={{ width: 60 }} />
                <th className="col-auto">Name</th>
                <th onClick={() => requestSort('date')}>
                  Date / Time
                  <Icon size="lg" className={getClassNamesFor('date')} icon="FilterList" />
                </th>
                <th>Identifier</th>
                <th>Amount</th>
                <th>Tag</th>
                {activeTab === 'Pending' ? '' : <th>System</th>}

                <th>From Cashapp</th>
                <th style={{ width: 150 }}>Status</th>
                <th className="col-auto"></th>
              </tr>
            </thead>
            <tbody>
              {transactionData.map((data: any) => (
                <tr key={data.id}>
                  <td>{data.serial_number}</td>
                  <td
                    style={{
                      paddingLeft: '5px',
                    }}
                  >
                    <Button
                      isOutline={!darkModeStatus}
                      color="dark"
                      isLight={darkModeStatus}
                      className={classNames({
                        'border-light': !darkModeStatus,
                      })}
                      icon="Info"
                      onClick={() => handleUpcomingDetails(data)}
                      aria-label="Detailed information"
                    />
                  </td>
                  <td>
                    <div className="d-flex">
                      <div className="flex-grow-1 ms-3 d-flex align-items-center text-nowrap">
                        {`${data.firstName} ${data.lastName}`}
                      </div>
                    </div>
                  </td>
                  <td>
                    <div className="d-flex align-items-center">
                      <span className="text-nowrap">{changeDateFormat(data.date)}</span>
                    </div>
                  </td>
                  <td>{data.transaction_id}</td>
                  <td>${data.amount}</td>
                  <td>{data.tag}</td>
                  {activeTab === 'Pending' ? '' : <td>{data.system}</td>}
                  <td>{data.paymentTag}</td>
                  <td>
                    <div className="col-auto" style={{ position: 'absolute' }}>
                      <Dropdown>
                        <DropdownToggle>
                          <Button color={getColorForStatus(data.status)}>{data.status}</Button>
                        </DropdownToggle>
                        <DropdownMenu size="lg">
                          <DropdownItem
                            onClick={() =>
                              openCloseModal(data.status, data._id, true, 'Pending', data.system)
                            }
                          >
                            Pending
                          </DropdownItem>
                          <DropdownItem
                            onClick={() =>
                              openCloseModal(data.status, data._id, true, 'Processed', data.system)
                            }
                          >
                            Processed
                          </DropdownItem>
                          <DropdownItem
                            onClick={() =>
                              openCloseModal(data.status, data._id, true, 'Refunded', data.system)
                            }
                          >
                            Refunded
                          </DropdownItem>
                          <DropdownItem
                            onClick={() =>
                              openCloseModal(data.status, data._id, true, 'Canceled', data.system)
                            }
                          >
                            Canceled
                          </DropdownItem>
                        </DropdownMenu>
                      </Dropdown>
                    </div>
                  </td>
                  <td>
                    <Button
                      isOutline={!darkModeStatus}
                      color="dark"
                      style={{ marginLeft: '35px' }}
                      isLight={darkModeStatus}
                      className={classNames({
                        'border-light': !darkModeStatus,
                      })}
                      icon="Info"
                      onClick={() => statusLogDetails(data)}
                      aria-label="Detailed information"
                    />
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </CardBody>
        <div ref={loaderRef} style={{ height: '10px', background: 'transparent' }}></div>
        {loading && <p style={{ fontSize: '17px', marginLeft: '10px' }}>Loading...</p>}
      </Card>
      <Modal
        isOpen={state}
        setIsOpen={setState}
        titleId="exampleModalLabel"
        isStaticBackdrop={staticBackdropStatus}
        isScrollable={scrollableStatus}
        isCentered={centeredStatus}
        size={sizeStatus}
        fullScreen={fullScreenStatus}
        isAnimation={animationStatus}
      >
        <ModalHeader setIsOpen={headerCloseStatus ? setState : undefined}>
          <ModalTitle id="exampleModalLabel">Confirm Transaction</ModalTitle>
        </ModalHeader>
        <ModalBody>
          {transactionStatus.status === 'Pending' ? (
            <p>Do you want process the transaction ?</p>
          ) : (
            <p>
              <div>Why do you want to undo this trasaction ?</div>
              <div style={{ marginTop: '20px', marginBottom: '20px' }}>
                <label>Reason:</label>
                <Input
                  autoComplete="status_reason"
                  onChange={handleChange}
                  value={statusReason.status_reason}
                  name="status_reason"
                />
                {transactionStatus.status !== 'Pending' && (
                  <div style={{ color: 'red' }}>{statusReasonError.status_reason}</div>
                )}
              </div>
            </p>
          )}
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <label style={{ marginRight: '10px' }}>System:</label>
            <Dropdown>
              <DropdownToggle>
                <Button>{transactionStatus.system ? transactionStatus.system : null}</Button>
              </DropdownToggle>
              <DropdownMenu size="md">
                {[
                  'GD',
                  'RIV',
                  'FK',
                  'MS',
                  'TOL',
                  'TC',
                  'MW',
                  'DS',
                  'OS',
                  'GK',
                  'GT',
                  'MC',
                  'PM',
                ].map((option) => (
                  <DropdownItem
                    onChange={handleChange}
                    key={option}
                    onClick={() => handleSelectedOption(option)}
                    style={{
                      backgroundColor:
                        transactionStatus.system === option ? '#007BFF' : 'transparent',
                      color: '#FFFFFF',
                    }}
                  >
                    {option}
                  </DropdownItem>
                ))}
              </DropdownMenu>
            </Dropdown>
            <div style={{ marginLeft: '20px', color: 'red' }}>{statusReasonError.system}</div>
            {/*      {systemError && <div style={{ color: 'red' }}>{systemError}</div>}*/}
          </div>
        </ModalBody>
        <ModalFooter>
          <Button
            color="info"
            isOutline
            className="border-0"
            onClick={() => openCloseModal(null, null, false, null, null)}
          >
            Cancel
          </Button>
          <Button onClick={statusUpdate} type="submit" color="info" icon="Save">
            Confirm
          </Button>
        </ModalFooter>
      </Modal>
      <OffCanvas
        setOpen={setStatusLogOffcanvas}
        isOpen={statusLogOffcanvas}
        titleId="statusLogDetails"
        placement="end"
      >
        <OffCanvasHeader setOpen={setStatusLogOffcanvas}>
          <OffCanvasTitle id="statusLogDetails"> </OffCanvasTitle>
        </OffCanvasHeader>
        <OffCanvasBody>
          {statusLog.statusLogs != undefined
            ? [...statusLog.statusLogs].reverse().map((data: any) => (
                //statusLog.statusLogs.reverse().map((data: any) => (
                <div className="row g-8">
                  <div className="col-lg-10">
                    <p>Username: {data.userName}</p>
                    <p>Status : {data.status}</p>
                    <p>Reason: {data.reason}</p>
                    <p>System: {data.system}</p>
                    <p>Time: {changeDateFormat(data.timestamp)}</p>
                  </div>
                  <hr></hr>
                </div>
              ))
            : null}
        </OffCanvasBody>
      </OffCanvas>
      <OffCanvas
        setOpen={setUpcomingEventsInfoOffcanvas}
        isOpen={upcomingEventsInfoOffcanvas}
        titleId="upcomingDetails"
        placement="end"
      >
        <OffCanvasHeader setOpen={setUpcomingEventsInfoOffcanvas}>
          <OffCanvasTitle id="upcomingDetails">{`${transaction.firstName} ${transaction.lastName}`}</OffCanvasTitle>
        </OffCanvasHeader>
        <OffCanvasBody>
          <div className="row g-8">
            <div className="col-lg-10">
              <p>Email From : {transaction.fromEmail}</p>
              <p>To: {transaction.toEmail}</p>
              <p>To: {transaction.to}</p>
              <p>Date : {changeDateFormat(transaction.date)}</p>
              <p>Tag: {transaction.tag}</p>
              <p>Subject : {transaction.subject}</p>
              <p>Amount: {transaction.amount}</p>
              <p>Destination: {transaction.destination}</p>
              <p>Identifier: {transaction.transaction_id}</p>
              <p>Payment Tag: {transaction.paymentTag}</p>
            </div>
          </div>
        </OffCanvasBody>
      </OffCanvas>
    </PageWrapper>
  );
};
Transactions.defaultProps = {
  isFluid: false,
};

export default Transactions;
