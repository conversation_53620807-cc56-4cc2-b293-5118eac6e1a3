import React, { useState, useEffect, useRef, useCallback } from 'react';
import axios from 'axios';
import { toast } from 'react-toastify';
import ProductTable from './productTable'; // Import the new component

interface Game {
  _id: string;
  userId: string;
  productId: string;
  gameName: string;
  requestId: number;
  status: string;
  passwordReset: string;
  playLink: string;
  userPassword: string;
  product: {
    _id: string;
    name: string;
    imageName: string;
    status: boolean;
    description: string;
  };
  passwordRequest?: 'Pending' | 'Sent';
  sweepstakesBalance?: number;
}

const MyGames: React.FC = () => {
  const [gameData, setGameData] = useState<Game[]>([]);
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(true);
  const [hasMore, setHasMore] = useState(true);
  const [transferStates, setTransferStates] = useState<{ [key: string]: { targetGame: string, amount: string } }>({});
  const [isTransferring, setIsTransferring] = useState(false);
  const observer = useRef<IntersectionObserver>();
  const loadingRef = useRef<HTMLDivElement>(null);

  const [isConfirmationVisible, setIsConfirmationVisible] = useState(false);
  const [gameIdToReset, setGameIdToReset] = useState<string>('');
  const [popupOpen, setPopupOpen] = useState(false);
  const [selectedGame, setSelectedGame] = useState<any | null>(null);
  const [isResettingPassword, setIsResettingPassword] = useState(false);

  const [showAmountPopup, setShowAmountPopup] = useState(false);
  const [selectedAction, setSelectedAction] = useState<'purchase' | 'redeem' | null>(null);
  const [selectedGameId, setSelectedGameId] = useState<string | null>(null);
  const [amount, setAmount] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Function to safely open game links
  const handleOpenGameLink = (playLink: string) => {

    // Show a toast notification
    toast.info('Opening game in a new tab...');

    // Try to open the link in a new tab
    try {
      const newWindow = window.open(playLink, '_blank', 'noopener,noreferrer');

      // Check if popup was blocked
      if (!newWindow || newWindow.closed || typeof newWindow.closed === 'undefined') {
        console.warn('Popup blocked or failed to open');
        // toast.warning('Game link may have been blocked by your browser. Please check your popup settings.');
      }
    } catch (error) {
      console.error('Error opening game link:', error);
      toast.error('Failed to open game. Please try again or check your browser settings.');
    }
  };
  const [walletBalance, setWalletBalance] = useState({
    redeemableSwipeCoins: 0,
    swipeCoins: 0
  });

  const lastGameElementRef = useCallback(
    (node: HTMLTableRowElement | null) => {
      if (loading) return;
      if (observer.current) observer.current.disconnect();

      observer.current = new IntersectionObserver((entries) => {
        if (entries[0].isIntersecting && hasMore) {
          setPage((prevPage) => prevPage + 1);
        }
      });

      if (node) observer.current.observe(node);
    },
    [loading, hasMore]
  );

  const handleResetPassword = async (gameId: string) => {
    if (!gameId) {
      console.error('Game ID is missing. Cannot reset the password.');
      return;
    }

    try {
      const response = await axios.put(
        `${process.env.REACT_APP_API_URL}/game-request/send-resetpassword-request/${gameId}`,
        { passwordRequest: 'Sent', request_type: 'passwordReset' },
        {
          headers: {
            Accept: 'application/json',
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            Authorization: `Bearer ${localStorage.getItem('Token')}`,
          },
        }
      );

      if (response.status === 200) {
        // Update the passwordRequest status in the local state
        const updatedPasswordRequest = response.data.data.passwordRequest || 'Sent';
        setGameData((prevData) =>
          prevData.map((g) =>
            g._id === gameId ? { ...g, passwordRequest: updatedPasswordRequest } : g
          )
        );
        toast.success('Password reset request sent successfully');
      }
    } catch (error: any) {
      console.error('Error resetting password:', error.message);
      toast.error('Failed to reset password');
    }
  };

  const fetchSingleGameRequest = async (gameId: string) => {
    setPopupOpen(true);
    try {
      const response = await axios.get(`${process.env.REACT_APP_API_URL}/game-request/${gameId}`, {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          Authorization: `Bearer ${localStorage.getItem('Token')}`,
        },
      });
      setSelectedGame(response.data.data);
    } catch (error: any) {
      console.error('Error fetching single game request:', error.message);
      toast.error('Failed to fetch game request');
    }
  };

  const fetchGameRequests = async (pageNum: number) => {
    try {
      setLoading(true);
      const response = await axios.get(
        `${process.env.REACT_APP_API_URL}/approved-game-requests?page=${pageNum}&limit=5`,
        {
          headers: {
            Accept: 'application/json',
            'Content-Type': 'application/json',
            Authorization: `Bearer ${localStorage.getItem('Token')}`,
          },
        }
      );
      const newGames = response.data.data || [];

      // Filter out games with disabled products
      const filteredGames = newGames.filter((game: Game) => game.product?.status !== false);

      // Ensure passwordRequest is properly handled (null or undefined should be treated as not requested)
      const processedGames = filteredGames.map((game: Game) => ({
        ...game,
        passwordRequest: game.passwordRequest || null
      }));

      setGameData((prevGames) => (pageNum === 1 ? processedGames : [...prevGames, ...processedGames]));
      setHasMore(newGames.length > 0);
    } catch (error: any) {
      console.error('Error fetching game requests:', error.message);
      toast.error('Failed to load games');
    } finally {
      setLoading(false);
    }
  };

  const handlePurchase = (gameId: string) => {
    const game = gameData.find(g => g._id === gameId);
    if (!game) {
      toast.error('Game not found');
      return;
    }
    setSelectedGameId(gameId);
    setSelectedAction('purchase');
    setAmount('');
    setShowAmountPopup(true);
  };

  const handleRedeem = (gameId: string) => {
    const game = gameData.find(g => g._id === gameId);
    if (!game) {
      toast.error('Game not found');
      return;
    }
    setSelectedGameId(gameId);
    setSelectedAction('redeem');
    setAmount('');
    setShowAmountPopup(true);
  };

  const fetchWalletBalance = async () => {
    try {
      const userId = localStorage.getItem('userId');
      if (!userId) {
        toast.error('User not authenticated');
        return;
      }

      const response = await axios.get(
        `${process.env.REACT_APP_API_URL}/wallet/check/${userId}`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('Token')}`,
          },
        }
      );

      if (response.data.data && response.data.data.balance) {
        setWalletBalance({
          swipeCoins: response.data.data.balance.swipeCoins,
          redeemableSwipeCoins: response.data.data.balance.redeemableSwipeCoins || 0
        });
      }
    } catch (error) {
      console.error('Error fetching wallet balance:', error);
      toast.error('Failed to fetch wallet balance');
    }
  };

  useEffect(() => {
    fetchWalletBalance();
  }, []);

  const handleAmountSubmit = async () => {
    if (!selectedGameId || !selectedAction || !amount) return;

    // Validate amount is a whole number
    if (!Number.isInteger(Number(amount))) {
      toast.error('Please enter a whole number amount (no decimals)');
      return;
    }

    const game = gameData.find(g => g._id === selectedGameId);
    if (!game) {
      toast.error('Game not found');
      return;
    }

    // Additional validation for purchase
    if (selectedAction === 'purchase' && walletBalance.swipeCoins < Number(amount)) {
      toast.error('Insufficient Sweeps Coins in wallet');
      return;
    }

    try {
      setIsSubmitting(true);
      const userId = localStorage.getItem('userId');
      if (!userId) {
        toast.error('User not authenticated');
        setIsSubmitting(false);
        return;
      }

      const response = await axios.post(
        `${process.env.REACT_APP_API_URL}/game-request/${selectedAction}`,
        {
          userId: userId.toString(),
          gameId: selectedGameId.toString(),
          gameName: game.gameName,
          amount: Number(amount),
          gameInfo: {
            playLink: game.playLink,
            productId: game.productId ? game.productId.toString() : null
          }
        },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('Token')}`,
          },
        }
      );

      if (response.status === 200) {
        // Update the game balance in the local state
        const updatedBalance = response.data.data.gameBalance;
        setGameData(prevGames =>
          prevGames.map(g =>
            g._id === selectedGameId
              ? { ...g, sweepstakesBalance: updatedBalance }
              : g
          )
        );

        // Always fetch updated wallet balance after any transaction
        await fetchWalletBalance();

        toast.success(`${selectedAction === 'purchase' ? 'Purchase' : 'Redeem'} Requested`);
        setShowAmountPopup(false);
        setAmount('');
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.error || `Failed to ${selectedAction}. Please try again.`;
      toast.error(errorMessage);
      console.error('Transaction error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  useEffect(() => {
    fetchGameRequests(page);
  }, [page]);

  const confirmResetPassword = (gameId: string) => {
    setGameIdToReset(gameId);
    setIsConfirmationVisible(true);
  };

  const handleConfirmSubmit = async () => {
    setIsResettingPassword(true);
    try {
      await handleResetPassword(gameIdToReset);
    } finally {
      setIsResettingPassword(false);
      setIsConfirmationVisible(false);
    }
  };

  const handleCancelSubmit = () => {
    setIsConfirmationVisible(false);
  };

  const GameTable = () => {
    return (
      <div className="rounded-lg overflow-hidden bg-white">

        {/* Desktop View - Visible on medium screens and up */}
        <div className="hidden md:!block lg:!block xl:!block 2xl:!block">
          <div className="overflow-auto" style={{ maxHeight: 'calc(100vh - 350px)' }}>
            <div className="min-w-full inline-block align-middle">
              <div className="border border-gray-200 rounded-lg">
                <table className="min-w-full divide-y divide-gray-200 border-collapse table-fixed">
                  <thead className="bg-gray-50 sticky top-0 z-10">
                    <tr>
                      <th className="px-2 sm:px-4 py-3 text-left text-sm sm:text-lg md:text-xl font-semibold text-gray-600 w-[20%]">
                        GAMES
                      </th>
                      <th className="px-2 sm:px-4 py-3 text-left text-sm sm:text-lg md:text-xl font-semibold text-gray-600 w-[20%]">
                        GAMES DETAILS
                      </th>
                      <th className="px-2 sm:px-4 py-3 text-left text-sm sm:text-lg md:text-xl font-semibold text-gray-600 w-[20%]">
                        RESET PASSWORD
                      </th>
                      <th className="px-2 sm:px-4 py-3 text-left text-sm sm:text-lg md:text-xl font-semibold text-gray-600 w-[15%]">
                        PLAY LINK
                      </th>
                      <th className="px-2 sm:px-4 py-3 text-left text-sm sm:text-lg md:text-xl font-semibold text-gray-600 w-[25%]">
                        ACTIONS
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {gameData.map((game, index) => (
                      <tr
                        key={game._id}
                        ref={index === gameData.length - 1 ? lastGameElementRef : null}
                        className="hover:bg-gray-50 h-16"
                      >
                        <td className="px-2 sm:px-4 py-3 text-sm sm:text-lg md:text-xl text-gray-900 truncate">
                          {game.gameName}
                        </td>
                        <td className="px-2 sm:px-4 py-3 text-sm sm:text-lg md:text-xl text-gray-900">
                          <button
                            className="bg-gray-500 text-white px-3 py-1.5 rounded text-sm sm:text-base hover:bg-lime-900 transition-colors"
                            onClick={() => fetchSingleGameRequest(game._id)}
                          >
                            SYSTEM DETAILS
                          </button>
                        </td>
                        <td className="px-2 sm:px-4 py-3">
                          {game.passwordRequest === 'Sent' ? (
                            <button
                              className="bg-red-500 text-white px-3 py-1.5 rounded text-sm sm:text-base cursor-not-allowed"
                              disabled
                            >
                              Requested
                            </button>
                          ) : (
                            <button
                              onClick={() => confirmResetPassword(game._id)}
                              className="bg-[#4B5E2F] text-white px-3 py-1.5 rounded text-sm sm:text-base hover:bg-[#3e4d26] transition-colors"
                            >
                              Reset Password
                            </button>
                          )}
                        </td>
                        <td className="px-2 sm:px-4 py-3">
                          {game.product?.status !== false ? (
                            <a href={game.playLink} target="_blank" rel="noopener noreferrer">
                            <button
                              // onClick={() => handleOpenGameLink(game.playLink)}
                              className="inline-block px-3 py-1.5 bg-green-500 text-white font-medium rounded-md hover:bg-green-600 transition-colors duration-200 shadow-sm text-sm sm:text-base"
                            >
                              Play
                            </button>
                            </a>
                          ) : (
                            <span className="text-gray-400">Unavailable</span>
                          )}
                        </td>
                        <td className="px-2 sm:px-4 py-3">
                          <div className="flex items-center space-x-2">
                            <button
                              onClick={() => handlePurchase(game._id)}
                              className={`px-3 py-1.5 rounded text-sm font-medium transition-colors duration-200 ${game.product?.status !== false
                                ? 'bg-lime-900 hover:bg-lime-800 text-white'
                                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                }`}
                              disabled={game.product?.status === false}
                            >
                              Purchase
                            </button>
                            <button
                              onClick={() => handleRedeem(game._id)}
                              className={`px-3 py-1.5 rounded text-sm font-medium transition-colors duration-200 ${game.product?.status !== false
                                ? 'bg-lime-900 hover:bg-lime-800 text-white'
                                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                }`}
                              disabled={game.product?.status === false}
                            >
                              Redeem
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                    {gameData.length === 0 && !loading && (
                      <tr>
                        <td colSpan={5} className="px-2 sm:px-4 py-8 text-center text-gray-500">
                          No games available. Request a system below.
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>

        {/* Mobile View - Only visible on small screens */}
        <div className="md:hidden">
          <div className="overflow-y-auto pb-4" style={{ maxHeight: 'calc(100vh - 350px)' }}>
            {gameData.length === 0 && !loading ? (
              <div className="p-4 text-center text-gray-500">
                No games available. Request a system below.
              </div>
            ) : (
              <div className="space-y-4 p-2">
                {gameData.map((game, index) => (
                  <div
                    key={game._id}
                    ref={index === gameData.length - 1 ? lastGameElementRef : null}
                    className="bg-white rounded-lg border border-gray-200 p-4 shadow-sm min-h-[180px] flex flex-col"
                  >
                    <div className="flex justify-between items-start mb-3">
                      <h3 className="text-lg font-semibold text-gray-900 pr-2 truncate">{game.gameName}</h3>
                      {game.product?.status !== false ? (
                        <button
                          onClick={() => handleOpenGameLink(game.playLink)}
                          className="inline-block px-3 py-1.5 bg-green-500 text-white font-medium rounded-md hover:bg-green-600 transition-colors duration-200 shadow-sm text-sm flex-shrink-0"
                        >
                          Play
                        </button>
                      ) : (
                        <span className="text-sm text-gray-400 flex-shrink-0">Unavailable</span>
                      )}
                    </div>

                    <div className="grid grid-cols-2 gap-2 mb-3">
                      <button
                        className="bg-gray-500 text-white px-2 py-1.5 rounded text-sm hover:bg-lime-900 transition-colors w-full h-10 flex items-center justify-center"
                        onClick={() => fetchSingleGameRequest(game._id)}
                      >
                        SYSTEM DETAILS
                      </button>

                      {game.passwordRequest === 'Sent' ? (
                        <button
                          className="bg-red-500 text-white px-2 py-1.5 rounded text-sm cursor-not-allowed w-full h-10 flex items-center justify-center"
                          disabled
                        >
                          Requested
                        </button>
                      ) : (
                        <button
                          onClick={() => confirmResetPassword(game._id)}
                          className="bg-[#4B5E2F] text-white px-2 py-1.5 rounded text-sm hover:bg-[#3e4d26] transition-colors w-full h-10 flex items-center justify-center"
                        >
                          Reset Password
                        </button>
                      )}
                    </div>

                    <div className="grid grid-cols-2 gap-2 mt-auto">
                      <button
                        onClick={() => handlePurchase(game._id)}
                        className={`px-2 py-1.5 rounded text-sm font-medium transition-colors duration-200 w-full h-10 flex items-center justify-center ${
                          game.product?.status !== false
                            ? 'bg-lime-900 hover:bg-lime-800 text-white'
                            : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                        }`}
                        disabled={game.product?.status === false}
                      >
                        Purchase
                      </button>
                      <button
                        onClick={() => handleRedeem(game._id)}
                        className={`px-2 py-1.5 rounded text-sm font-medium transition-colors duration-200 w-full h-10 flex items-center justify-center ${
                          game.product?.status !== false
                            ? 'bg-lime-900 hover:bg-lime-800 text-white'
                            : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                        }`}
                        disabled={game.product?.status === false}
                      >
                        Redeem
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {loading && (
          <div className="flex justify-center items-center p-4 border-t border-gray-200" ref={loadingRef}>
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            <span className="ml-3 text-gray-500">Loading games...</span>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="w-full bg-white rounded-lg p-2 sm:p-4 md:p-6 lg:ml-[250px] lg:max-w-[calc(100%-250px)] min-h-[500px] flex flex-col pb-20">
      <div className="mb-4 sm:mb-6">
        <div className="rounded-lg overflow-hidden bg-white shadow-sm border border-gray-100">
          <div className="p-2 sm:p-3 border-b bg-gray-50">
            <h2 className="text-xl sm:text-2xl font-bold">MY GAMES</h2>
          </div>
          <div className="p-2">
            <GameTable />
          </div>
        </div>
      </div>
      <div className="mt-2 md:mt-4 mb-6">
        <div className="rounded-lg overflow-hidden bg-white shadow-sm border border-gray-100">
          <div className="p-2 sm:p-3 border-b bg-gray-50">
            <h2 className="text-xl sm:text-2xl font-bold">REQUEST NEW GAMES</h2>
          </div>
          <div className="p-2">
            <ProductTable />
          </div>
        </div>
      </div>
      {popupOpen && selectedGame && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white p-4 sm:p-6 rounded-lg shadow-lg w-full max-w-[600px] relative max-h-[90vh] overflow-y-auto">
            {/* Close Button */}
            <button
              className="absolute top-2 right-2 sm:top-4 sm:right-4 text-red-600 text-2xl sm:text-3xl font-bold hover:text-red-800 transition-colors"
              onClick={() => setPopupOpen(false)}
              aria-label="Close"
            >
              ×
            </button>

            {/* Popup Title */}
            <h2 className="text-xl sm:text-2xl font-semibold mb-4 sm:mb-6 border-b pb-2">System Details</h2>

            {/* System Details */}
            <div className="text-left space-y-3 sm:space-y-4 text-[#4B5E2F]">
              {/* System Name */}
              <div className="flex flex-col">
                <div className="font-semibold text-base sm:text-xl mb-1">
                  {selectedGame.gameName}
                </div>
                <div className="text-sm sm:text-base text-gray-600">
                  User ID: {selectedGame.userId}
                </div>
                <div className="text-sm sm:text-base text-gray-600 mt-2">
                  {selectedGame.product?.description || "Request Games Now"}
                </div>
              </div>

              {/* Loop through gameFields */}
              <div className="mt-2">
                {selectedGame.gameFields && Object.keys(selectedGame.gameFields).length > 0 ? (
                  <div className="grid grid-cols-1 gap-2">
                    {Object.entries(selectedGame.gameFields).map(([label, value], index) => (
                      <div key={index} className="border-b border-gray-100 pb-2">
                        <span className="font-medium text-sm sm:text-base">{label}: </span>
                        <span className="text-sm sm:text-base break-words">{String(value)}</span>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-sm sm:text-base">No additional fields available for this system.</div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {isConfirmationVisible && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white p-4 sm:p-6 md:p-8 rounded-lg shadow-lg w-full max-w-[500px] text-center">
            <h2 className="text-xl sm:text-2xl font-semibold mb-4 sm:mb-6">Confirm Password Reset</h2>
            <p className="text-base sm:text-lg md:text-xl text-gray-600 mb-6 sm:mb-8">
              Are you sure you want to reset the password for this game?
            </p>
            <div className="flex flex-col sm:flex-row justify-between gap-3 sm:gap-4">
              <button
                className="bg-gray-300 px-4 py-2 sm:py-3 rounded-md text-base sm:text-lg hover:bg-gray-400 w-full sm:w-[48%] order-2 sm:order-1"
                onClick={handleCancelSubmit}
                disabled={isResettingPassword}
              >
                Cancel
              </button>
              <button
                className="bg-lime-900 text-white px-4 py-2 sm:py-3 rounded-md text-base sm:text-lg hover:bg-lime-800 w-full sm:w-[48%] flex items-center justify-center order-1 sm:order-2"
                onClick={handleConfirmSubmit}
                disabled={isResettingPassword}
              >
                {isResettingPassword ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 sm:h-5 sm:w-5 border-b-2 border-white mr-2"></div>
                    Processing...
                  </>
                ) : (
                  'OK'
                )}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Amount Input Popup */}
      {showAmountPopup && (
        <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl shadow-2xl w-full max-w-md transform transition-all max-h-[90vh] overflow-y-auto">
            {/* Header */}
            <div className="p-4 sm:p-6 border-b border-gray-200">
              <div className="flex justify-between items-center">
                <h2 className="text-xl sm:text-2xl font-bold text-lime-900">
                  {selectedAction === 'purchase' ? 'Purchase' : 'Redeem'}
                </h2>
                <button
                  onClick={() => {
                    setShowAmountPopup(false);
                    setAmount('');
                  }}
                  className="text-gray-400 hover:text-lime-900 transition-colors"
                  aria-label="Close"
                >
                  <svg className="w-5 h-5 sm:w-6 sm:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>

            {/* Content */}
            <div className="p-4 sm:p-6 space-y-4 sm:space-y-6">
              {/* Wallet Balance Card */}
              <div className="bg-lime-50 rounded-lg p-3 sm:p-4 border border-lime-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2 sm:space-x-3">
                    <div className="p-1.5 sm:p-2 bg-lime-100 rounded-full">
                      <svg className="w-5 h-5 sm:w-6 sm:h-6 text-lime-900" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <p className="text-xs sm:text-sm text-lime-900">Available Balance</p>
                      <p className="text-base sm:text-xl font-bold text-lime-900">
                        {selectedAction === "purchase" ? walletBalance.swipeCoins : selectedAction === "redeem" ? walletBalance.swipeCoins : "NA"}
                        <span className="text-xs sm:text-sm font-normal text-lime-700"> Coins</span>
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Amount Input */}
              <div className="space-y-1 sm:space-y-2">
                <label htmlFor="amount" className="block text-xs sm:text-sm font-medium text-lime-900">
                  Enter Amount
                </label>
                <div className="mt-1 relative rounded-md shadow-sm">
                  <input
                    type="number"
                    name="amount"
                    id="amount"
                    value={amount}
                    onChange={(e) => setAmount(e.target.value)}
                    className="block w-full pr-10 pl-3 sm:pl-4 py-2 sm:py-3 border-lime-300 rounded-lg focus:ring-2 focus:ring-lime-500 focus:border-lime-500 text-base sm:text-lg"
                    placeholder="0"
                    min="1"
                    step="1"
                    onKeyDown={(e) => {
                      if (e.key === '.' || e.key === 'e' || e.key === '-' || e.key === '+') {
                        e.preventDefault();
                      }
                    }}
                  />
                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                    <span className="text-lime-700 text-xs sm:text-sm">coins</span>
                  </div>
                </div>
              </div>

              {/* Warning Note for Redeem */}
              {selectedAction === 'redeem' && (
                <div className="bg-yellow-50 rounded-lg p-3 sm:p-4 border border-yellow-200">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg className="h-4 w-4 sm:h-5 sm:w-5 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                      </svg>
                    </div>
                    <div className="ml-2 sm:ml-3">
                      <h3 className="text-xs sm:text-sm font-medium text-yellow-800">Important Note</h3>
                      <div className="mt-1 sm:mt-2 text-xs sm:text-sm text-yellow-700">
                        Make sure to logout of the game system so we can process your redeem request.
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Transaction Details */}
              <div className="bg-lime-50 rounded-lg p-3 sm:p-4 space-y-2 sm:space-y-3 border border-lime-200">
                <div className="flex justify-between text-xs sm:text-sm">
                  <span className="text-lime-700">Transaction Type</span>
                  <span className="font-medium text-lime-900 capitalize">{selectedAction}</span>
                </div>
                {selectedAction === 'purchase' ? (
                  <div className="flex justify-between text-xs sm:text-sm">
                    <span className="text-lime-700">Remaining Balance After Purchase</span>
                    <span className="font-medium text-lime-900">
                      {Math.max(0, walletBalance.swipeCoins - (Number(amount) || 0))} coins
                    </span>
                  </div>
                ) : (
                  <div className="flex justify-between text-xs sm:text-sm">
                    <span className="text-lime-700">Balance After Redeem Approval</span>
                    <span className="font-medium text-lime-900">
                      {Math.max(0, walletBalance.swipeCoins + (Number(amount) || 0))} coins
                    </span>
                  </div>
                )}
              </div>
            </div>

            {/* Footer */}
            <div className="p-4 sm:p-6 border-t border-gray-200 flex flex-col sm:flex-row sm:justify-end gap-2 sm:gap-3">
              <button
                onClick={() => {
                  setShowAmountPopup(false);
                  setAmount('');
                }}
                className="px-4 py-2 border border-lime-300 rounded-lg text-lime-900 hover:bg-lime-50 transition-colors font-medium w-full sm:w-auto order-2 sm:order-1"
              >
                Cancel
              </button>
              <button
                onClick={handleAmountSubmit}
                disabled={isSubmitting}
                className="px-4 sm:px-6 py-2 bg-lime-900 text-white rounded-lg hover:bg-lime-800 transition-colors font-medium flex items-center justify-center w-full sm:w-auto order-1 sm:order-2"
              >
                {isSubmitting ? (
                  <>
                    <span>Processing </span>
                    <div className="ml-2 animate-spin rounded-full h-4 w-4 sm:h-5 sm:w-5 border-b-2 border-white"></div>
                  </>
                ) : (
                  <>
                    <span>Request {selectedAction} </span>
                    <svg className="w-4 h-4 sm:w-5 sm:h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MyGames;