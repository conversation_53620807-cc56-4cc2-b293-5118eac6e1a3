// Types for Reports Dashboard

export interface DateRange {
  startDate: Date | null;
  endDate: Date | null;
  key: string;
}

export interface GameFilters {
  search: string;
  gameName: string;
  userSearch: string;
  status: string;
  dateFilter: {
    startDate: Date | null;
    endDate: Date | null;
  };
  amountFilter: {
    min: string;
    max: string;
  };
}

export interface GameTransaction {
  _id: string;
  userId: string;
  userName: string;
  gameName: string;
  amount: number;
  transactionType: 'purchase' | 'redeem';
  status: 'pending' | 'completed' | 'failed';
  createdAt: string;
  updatedAt: string;
}

export interface GameTransactionsResponse {
  success: boolean;
  data: GameTransaction[];
  pagination: {
    currentPage: number;
    totalItems: number;
    hasMore: boolean;
  };
}

export interface SystemTotals {
  totalSweepsCoins: number;
  totalGoldCoins: number;
  totalGamePurchases: number;
  totalCashRedeems: number;
}

export interface ReportData {
  date: string;
  purchases: number;
  redeems: number;
  totalAmount: number;
  userCount: number;
}

export interface AnalyticsResponse {
  success: boolean;
  data: ReportData[];
}

export interface DashboardResponse {
  success: boolean;
  data: {
    totalUsers: number;
    totalSweepsCoins: number;
    totalGoldCoins: number;
    totalPurchases: number;
    totalRedeems: number;
  };
}

export interface ApiError {
  message: string;
  status?: number;
}
