// ===================================================================
// REPORTS DASHBOARD TYPES - CENTRALIZED TYPE DEFINITIONS
// ===================================================================

// ===================================================================
// DATE AND TIME TYPES
// ===================================================================

export interface DateRange {
  startDate: Date;
  endDate: Date;
  key: string;
}

export interface DateFilter {
  startDate: Date | null;
  endDate: Date | null;
}

// ===================================================================
// GAME TRANSACTION TYPES
// ===================================================================

export interface GameTransaction {
  _id: string;
  userId: string;
  userName?: string;
  gameId?: string;
  gameName: string;
  amount: number;
  transactionType: 'purchase' | 'redeem';
  status: 'pending' | 'completed' | 'failed';
  gameInfo?: any;
  createdAt: string;
  updatedAt?: string;
}

export interface GameFilters {
  search: string;
  gameName: string;
  userSearch: string;
  status: string;
  dateFilter: DateFilter;
  amountFilter: {
    min: string;
    max: string;
  };
}

export interface GameTransactionsPagination {
  currentPage: number;
  totalItems: number;
  hasMore: boolean;
}

// ===================================================================
// SYSTEM TOTALS AND ANALYTICS TYPES
// ===================================================================

export interface SystemTotals {
  totalSweepsCoins: number;
  totalGoldCoins: number;
  totalGamePurchases: number;
  totalCashRedeems: number;
}

export interface ReportData {
  date: string;
  purchases: number;
  redeems: number;
  totalAmount: number;
  userCount: number;
  // Legacy fields for backward compatibility
  newCustomers?: number;
  totalCustomers?: number;
  averageProcessedTime?: number;
  totalSales?: number;
  transactionCount?: number;
  gamePurchases?: number;
  gameRedeems?: number;
  goldCoinPurchases?: number;
  cashRedeems?: number;
  approvalTime?: string;
}

// ===================================================================
// API RESPONSE TYPES
// ===================================================================

export interface GameTransactionsResponse {
  success: boolean;
  data: GameTransaction[];
  pagination: GameTransactionsPagination;
  summary?: {
    totalTransactions: number;
    totalPurchaseAmount: number;
    totalRedeemAmount: number;
  };
}

export interface AnalyticsResponse {
  success: boolean;
  data: ReportData[];
}

export interface DashboardResponse {
  success: boolean;
  data: {
    totalUsers: number;
    totalSweepsCoins: number;
    totalGoldCoins: number;
    totalPurchases: number;
    totalRedeems: number;
  };
}

export interface ApiError {
  message: string;
  status?: number;
}

// ===================================================================
// COMPONENT PROP TYPES
// ===================================================================

export interface ReportsHeaderProps {
  activeTab: 'daily' | 'weekly' | 'monthly';
  dateRange: DateRange;
  showDatePicker: boolean;
  onTabChange: (tab: 'daily' | 'weekly' | 'monthly') => void;
  onDateRangeChange: (ranges: any) => void;
  onToggleDatePicker: () => void;
  datePickerButtonRef: React.RefObject<HTMLButtonElement>;
  datePickerRef: React.RefObject<HTMLDivElement>;
  datePickerStyles: string;
}

export interface OrderAnalyticsSectionProps {
  systemTotals: SystemTotals;
  reportData: ReportData[];
  loading: boolean;
}

export interface GameTransactionsSectionProps {
  gameFilters: GameFilters;
  onFilterChange: (key: keyof GameFilters, value: any) => void;
  totalTransactions: number;
  totalPurchaseAmount: number;
  totalRedeemAmount: number;
}

export interface TransactionDataTableProps {
  transactions: GameTransaction[];
  loading: boolean;
  pagination: GameTransactionsPagination;
  onPageChange: (page: number) => void;
}

export interface DashboardCardProps {
  label: string;
  value: string | number;
  icon: React.ReactNode;
  bgColor: string;
}

// ===================================================================
// HOOK RETURN TYPES
// ===================================================================

export interface UseReportsDataReturn {
  // State
  activeTab: string;
  dateRange: DateRange[];
  gameFilters: GameFilters;
  gameTransactions: GameTransaction[];
  gameTransactionsPagination: GameTransactionsPagination;
  systemTotals: SystemTotals;
  reportData: ReportData[];
  loading: boolean;
  gameTransactionsLoading: boolean;

  // Actions
  handleGameFilterChange: (key: keyof GameFilters, value: any) => void;
  handleDateRangeChange: (ranges: any) => void;
  handleTabChange: (tab: string) => void;
  handlePageChange: (page: number) => void;
  fetchReportData: () => Promise<void>;
  fetchSystemTotals: () => Promise<void>;
  fetchGameTransactions: (page?: number) => Promise<void>;
}
