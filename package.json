{"name": "facit", "version": "4.4.1", "sideEffects": false, "private": true, "scripts": {"start": "cross-env DISABLE_ESLINT_PLUGIN=true REACT_APP_CHUNK_RETRY=true react-scripts start", "build": "npm run generate-favicons && cross-env CI=false DISABLE_ESLINT_PLUGIN=true REACT_APP_CHUNK_RETRY=true GENERATE_SOURCEMAP=true WDS_SOCKET_PORT=0 react-scripts build && npm run sentry:sourcemaps:if-token", "serve": "serve -s build", "test": "react-scripts test", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,scss}\"", "lint:scss": "stylelint **/*.scss", "lint:fix-scss": "stylelint --fix **/*.scss", "eject": "react-scripts eject", "generate-favicons": "node scripts/generate-favicons.js", "sentry:sourcemaps": "sentry-cli sourcemaps inject --org scraft-4g --project luckshack-ecommerce ./build && sentry-cli sourcemaps upload --org scraft-4g --project luckshack-ecommerce ./build", "sentry:sourcemaps:if-token": "if [ -n \"$SENTRY_AUTH_TOKEN\" ]; then npm run sentry:sourcemaps; else echo \"Skipping Sentry sourcemap upload (no auth token)\"; fi"}, "jest": {"transformIgnorePatterns": ["<rootDir>/node_modules/(?!pascalcase|prismjs|@omtanke/react-use-event-outside/.*)"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 4 version", ">0.2%"]}, "dependencies": {"@ant-design/icons": "5.2.6", "@hello-pangea/dnd": "16.3.0", "@jridgewell/sourcemap-codec": "^1.4.15", "@omtanke/react-use-event-outside": "1.0.1", "@popperjs/core": "2.11.8", "@reactour/tour": "3.6.1", "@reduxjs/toolkit": "1.9.7", "@sentry/cli": "^2.46.0", "@sentry/react": "^9.22.0", "@stripe/react-stripe-js": "2.4.0", "@stripe/stripe-js": "2.2.2", "@tanstack/react-query": "^5.76.1", "@testing-library/jest-dom": "5.17.0", "@testing-library/react": "13.4.0", "@types/node": "16.18.68", "@types/react-modal": "^3.16.3", "apexcharts": "3.45.1", "async-mutex": "^0.5.0", "aws-sdk": "2.1525.0", "axios": "1.6.2", "classnames": "2.3.2", "core-js": "^3.33.0", "date-fns": "2.30.0", "dayjs": "1.11.10", "font-awesome": "4.7.0", "formik": "2.4.5", "framer-motion": "10.16.16", "i18next": "23.7.11", "i18next-browser-languagedetector": "7.2.0", "i18next-http-backend": "2.4.2", "js-sha3": "^0.9.3", "jsx-to-string": "^1.4.0", "lucide-react": "0.294.0", "moment": "2.29.4", "moment-timezone": "0.5.43", "pascalcase": "2.0.0", "prismjs": "1.29.0", "prop-types": "15.8.1", "react": "^18.2.0", "react-apexcharts": "1.4.1", "react-big-calendar": "1.8.5", "react-calendar": "4.7.0", "react-credit-cards-2": "1.0.1", "react-date-range": "^1.4.0", "react-datepicker": "4.25.0", "react-dom": "^18.2.0", "react-i18next": "13.5.0", "react-icons": "4.12.0", "react-input-mask": "2.0.4", "react-jss": "10.10.0", "react-modal": "^3.16.3", "react-modern-calendar-datepicker": "3.1.6", "react-notifications-component": "4.0.1", "react-number-format": "5.3.1", "react-paginate": "8.2.0", "react-popper": "2.3.0", "react-redux": "8.1.3", "react-router-dom": "6.21.1", "react-router-hash-link": "2.4.3", "react-scrollspy": "3.4.3", "react-select": "5.8.0", "react-syntax-highlighter": "15.5.0", "react-toastify": "9.1.3", "react-transition-group": "4.4.5", "react-use": "17.4.2", "react-virtualized-auto-sizer": "^1.0.26", "react-window": "^1.8.11", "styled-components": "6.1.3", "svgo": "^3.0.2", "typescript": "4.9.5", "workbox-background-sync": "^7.0.0", "workbox-cacheable-response": "^7.0.0", "workbox-core": "^6.5.4", "workbox-expiration": "^6.5.4", "workbox-google-analytics": "^7.0.0", "workbox-precaching": "^6.5.4", "workbox-routing": "^6.5.4", "workbox-strategies": "^6.5.4"}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@types/pascalcase": "1.0.3", "@types/payment": "2.1.7", "@types/prismjs": "1.26.3", "@types/react": "18.2.45", "@types/react-big-calendar": "1.8.7", "@types/react-date-range": "^1.4.9", "@types/react-datepicker": "^6.2.0", "@types/react-dom": "18.2.18", "@types/react-input-mask": "3.0.5", "@types/react-redux": "7.1.33", "@types/react-router-hash-link": "2.4.9", "@types/react-scrollspy": "3.3.9", "@types/react-syntax-highlighter": "15.5.11", "@types/react-transition-group": "4.4.10", "@types/react-window": "^1.8.8", "animate.css": "4.1.1", "bootstrap": "5.3.2", "compression-webpack-plugin": "^10.0.0", "cross-env": "^7.0.3", "customize-cra": "^1.0.0", "favicons": "^7.2.0", "http-proxy-middleware": "^3.0.5", "prettier": "^3.1.1", "react-app-rewired": "^2.2.1", "react-scripts": "^5.0.1", "sass": "1.69.5", "stylelint": "^15.11.0", "stylelint-config-prettier-scss": "^1.0.0", "stylelint-config-standard-scss": "^11.1.0", "typescript": "4.9.5", "webpack": "^5.98.0", "webpack-dev-server": "^5.2.0"}, "resolutions": {"core-js": "^3.33.0", "sourcemap-codec": "^1.4.15", "svgo": "^3.0.2", "workbox-cacheable-response": "^7.0.0", "workbox-google-analytics": "^7.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "typescript": "4.9.5", "glob-parent": ">=5.1.2", "jackspeak": "2.1.1", "json5": ">=2.2.2", "nth-check": ">=2.1.1", "postcss": ">=8.4.31", "trim": "^1.0.1"}, "overrides": {"@types/react": "18.2.45", "@types/react-dom": "18.2.18", "typescript": "4.9.5", "glob-parent": ">=5.1.2", "jackspeak": "2.1.1", "json5": ">=2.2.2", "nth-check": ">=2.1.1", "postcss": ">=8.4.31", "trim": "^1.0.1"}}