import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import axios from 'axios';

interface AuthState {
  user: any | null;
  token: string | null;
  loading: boolean;
  error: any | null;
  userData: any;
  isAuthenticated: boolean;
}

const initialState: AuthState = {
  user: null,
  token: localStorage.getItem('Token') || null,
  loading: false,
  error: null,
  userData: {},
  isAuthenticated: !!localStorage.getItem('Token'),
};

// Async Thunks for Login and Verification
export const loginUser = createAsyncThunk(
  'auth/login',
  async (credentials: { email: string; password: string }, { rejectWithValue }) => {
    try {
      const response = await axios.post('https://api.example.com/auth/login', credentials);
      const { token, user } = response.data;
      localStorage.setItem('token', token);
      return { token, user };
    } catch (error) {
      return rejectWithValue('Invalid credentials');
    }
  }
);

export const verifyToken = createAsyncThunk('auth/verify', async (_, { rejectWithValue }) => {
  try {
    const token = localStorage.getItem('Token');
    if (!token) throw new Error('No token found');

    const response = await axios.get(`${process.env.REACT_APP_API_URL}/verifyToken`, {
      headers: { Authorization: `Bearer ${token}` },
    });

    return response.data.user;
  } catch (error) {
    // localStorage.removeItem('Token');
    return rejectWithValue('Invalid Token');
  }
});

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    logout(state) {
      state.user = null;
      state.token = null;
      state.loading = false;
      state.error = null;
      localStorage.removeItem('Token');
    },
  },
  extraReducers: (builder) => {
    builder
      // Login cases
      .addCase(loginUser.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(loginUser.fulfilled, (state, action) => {
        state.loading = false;
        state.user = action.payload.user;
        state.token = action.payload.token;
        state.error = null;
      })
      .addCase(loginUser.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Verify token cases
      .addCase(verifyToken.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(verifyToken.fulfilled, (state, action) => {
        state.loading = false;
        state.user = action.payload;
        state.error = null;
      })
      .addCase(verifyToken.rejected, (state, action) => {
        state.loading = false;
        state.user = null;
        state.token = null;
        state.error = action.payload as string;
      });
  },
});

export const { logout } = authSlice.actions;
export default authSlice.reducer;
