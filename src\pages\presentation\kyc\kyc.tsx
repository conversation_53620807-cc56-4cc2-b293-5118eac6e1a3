import React, { useState, useEffect, useRef } from 'react';
import logo from '../../../assets/logo.svg';
import { useSelector } from 'react-redux';
import { RootState } from '../../../redux/store';
import axios from 'axios';
import { toast } from 'react-toastify';
import { useNavigate } from 'react-router-dom';
import Modal from 'react-modal';

const KYCVerification: React.FC = () => {
  const [kycUrl, setKycUrl] = useState('');
  const [kycStatus, setKycStatus] = useState<'pending' | 'completed' | 'failed' | 'rejected'>('pending');
  const [isPolling, setIsPolling] = useState(false);
  const [redirectCountdown, setRedirectCountdown] = useState<number | null>(null);
  const [showRejectionModal, setShowRejectionModal] = useState(false);
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const countdownIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const { user, loading }: any = useSelector((state: RootState) => state.auth);
  const navigate = useNavigate();

  // Function to check KYC status
  const checkKycStatus = async () => {
    try {
      if (user?._id) {
        const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/kyc/verify?user_id=${user._id}`);
        
        // Check if the user's KYC is rejected
        if (response.data.is_rejected === true) {
          setKycStatus('rejected');
          // Stop polling if verification is rejected
          if (pollingIntervalRef.current) {
            clearInterval(pollingIntervalRef.current);
            setIsPolling(false);
          }
          
          // Show rejection modal
          setShowRejectionModal(true);
          
          return false;
        }
        
        // Check if the user is verified based on the is_verified field
        if (response.data.is_verified === true) {
          setKycStatus('completed');
          // Stop polling if verification is completed
          if (pollingIntervalRef.current) {
            clearInterval(pollingIntervalRef.current);
            setIsPolling(false);
          }
          
          // Start redirect countdown
          startRedirectCountdown();
          
          return true;
        }
      }
      return false;
    } catch (error) {
      console.error('Error checking KYC status:', error);
      return false;
    }
  };

  // Function to start the redirect countdown
  const startRedirectCountdown = () => {
    // Only start if not already counting down
    if (redirectCountdown !== null) return;
    
    // Start with 5 seconds
    setRedirectCountdown(5);
    
    // Create interval to count down
    countdownIntervalRef.current = setInterval(() => {
      setRedirectCountdown(prev => {
        if (prev === null || prev <= 1) {
          // Time to redirect
          if (countdownIntervalRef.current) {
            clearInterval(countdownIntervalRef.current);
          }
          // Redirect to dashboard
          navigate('/user/dashboard');
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  useEffect(() => {
    // Get user data
    const email = user?.email;
    const userId = user?._id;

    // Create the URL with parameters
    const baseUrl = 'https://form.argosidentity.com';
    const params = new URLSearchParams({
      pid: 'ekkyxe2qde', // Your project ID
      userid: userId, // This will be received as userid in webhook
      return_url: `${window.location.origin}/user/dashboard`, // Redirect after completion
      cf_email: email || '', // Pre-fill email if available
    });

    const fullUrl = `${baseUrl}?${params.toString()}`;
    setKycUrl(fullUrl);

    // Initial check for KYC status
    checkKycStatus();

    // Clean up any existing intervals when component unmounts
    return () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
      }
      if (countdownIntervalRef.current) {
        clearInterval(countdownIntervalRef.current);
      }
    };
  }, [user, navigate]);

  // Listen for window focus events to check status when user returns from KYC tab
  useEffect(() => {
    const handleWindowFocus = () => {
      // Check status when user returns to this tab
      checkKycStatus();
    };

    window.addEventListener('focus', handleWindowFocus);
    
    return () => {
      window.removeEventListener('focus', handleWindowFocus);
    };
  }, [user]);

  const handleStartKyc = () => {
    // Open KYC URL in a new tab
    window.open(kycUrl, '_blank', 'noopener,noreferrer');
    
    // Start polling for status updates
    startStatusPolling();
    
    // Update local status
    toast.info('KYC verification process started. This page will automatically update when verification is complete.', {
      autoClose: 5000,
    });
  };

  const startStatusPolling = () => {
    // Don't start a new polling interval if one is already running
    if (isPolling) return;
    
    setIsPolling(true);
    
    // Poll every 5 seconds for more responsive updates
    pollingIntervalRef.current = setInterval(async () => {
      const isComplete = await checkKycStatus();
      
      if (isComplete) {
        toast.success('KYC verification completed successfully! Redirecting to dashboard...', {
          autoClose: 5000,
        });
      }
    }, 5000); // Check every 5 seconds for more responsive updates
  };

  const handleCheckStatus = async () => {
    const isComplete = await checkKycStatus();
    
    if (isComplete) {
      toast.success('KYC verification completed successfully! Redirecting to dashboard...', {
        autoClose: 5000,
      });
    } else {
      toast.info('KYC verification is still pending. Please complete the process.', {
        autoClose: 5000,
      });
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#4D774E]" />
      </div>
    );
  }

  // Modal styles
  const customModalStyles = {
    content: {
      top: '50%',
      left: '50%',
      right: 'auto',
      bottom: 'auto',
      marginRight: '-50%',
      transform: 'translate(-50%, -50%)',
      maxWidth: '500px',
      width: '90%',
      padding: '2rem',
      borderRadius: '0.5rem',
    },
    overlay: {
      backgroundColor: 'rgba(0, 0, 0, 0.75)'
    }
  };

  // Close modal function
  const closeRejectionModal = () => {
    setShowRejectionModal(false);
  };

  return (
    <div className="min-h-screen w-full bg-gray-50">
      {/* KYC Rejection Modal */}
      <Modal
        isOpen={showRejectionModal}
        onRequestClose={closeRejectionModal}
        style={customModalStyles}
        contentLabel="KYC Rejection Modal"
        ariaHideApp={false}
      >
        <div className="text-center">
          <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-4">
            <svg className="h-10 w-10 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </div>
          <h2 className="text-2xl font-semibold text-gray-900 mb-4">KYC Verification Rejected</h2>
          <p className="text-gray-600 mb-6">
            Your KYC verification has been rejected. Please contact our support team at <a href="mailto:<EMAIL>" className="text-lime-700 font-medium"><EMAIL></a> for assistance.
          </p>
          <button
            onClick={closeRejectionModal}
            className="bg-lime-700 text-white py-2 px-6 rounded-md hover:bg-lime-800 transition-colors"
          >
            Close
          </button>
        </div>
      </Modal>

      <div className="w-full">
        <div className="flex justify-center py-8 bg-white border-b">
          <img src={logo} alt="Company Logo" className="h-24" />
        </div>
        
        <div className="max-w-4xl mx-auto p-8">
          <div className="bg-white rounded-lg shadow-lg p-8">
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold text-gray-900 mb-4">KYC Verification</h1>
              <p className="text-lg text-gray-600">
                To comply with regulations and ensure the security of your account, we need to verify your identity.
              </p>
            </div>

            {kycStatus === 'rejected' ? (
              <div className="text-center py-8">
                <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-4">
                  <svg className="h-10 w-10 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </div>
                <h2 className="text-2xl font-semibold text-gray-900 mb-2">Verification Rejected</h2>
                <p className="text-gray-600 mb-6">
                  Your KYC verification has been rejected. Please contact our support team for assistance.
                </p>
                <button
                  onClick={() => setShowRejectionModal(true)}
                  className="bg-lime-700 text-white py-3 px-6 rounded-md hover:bg-lime-800 transition-colors mr-4"
                >
                  View Details
                </button>
                <button
                  onClick={() => navigate('/user/dashboard')}
                  className="border border-lime-700 text-lime-700 py-3 px-6 rounded-md hover:bg-lime-50 transition-colors"
                >
                  Return to Dashboard
                </button>
              </div>
            ) : kycStatus === 'completed' ? (
              <div className="text-center py-8">
                <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-4">
                  <svg className="h-10 w-10 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <h2 className="text-2xl font-semibold text-gray-900 mb-2">Verification Complete</h2>
                <p className="text-gray-600 mb-6">
                  Thank you for completing your KYC verification. Your account is now fully verified.
                </p>
                {redirectCountdown !== null && (
                  <p className="text-sm text-gray-500 mb-4">
                    Redirecting to dashboard in {redirectCountdown} seconds...
                  </p>
                )}
                <button
                  onClick={() => navigate('/user/dashboard')}
                  className="bg-lime-700 text-white py-3 px-6 rounded-md hover:bg-lime-800 transition-colors"
                >
                  Go to Dashboard Now
                </button>
              </div>
            ) : kycStatus === 'pending' ? (
              <div className="space-y-8">
                <div className="border border-gray-200 rounded-lg p-6 bg-gray-50">
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">What You'll Need:</h3>
                  <ul className="list-disc pl-6 space-y-2 text-gray-600">
                    <li>A valid government-issued photo ID (passport, driver's license, etc.)</li>
                    <li>Access to your device's camera</li>
                    <li>Good lighting conditions</li>
                    <li>Approximately 5 minutes to complete the process</li>
                  </ul>
                </div>

                <div className="text-center space-y-6">
                  <button
                    onClick={handleStartKyc}
                    className="bg-lime-700 text-white py-3 px-8 rounded-md hover:bg-lime-800 transition-colors text-lg w-full sm:w-auto"
                  >
                    Start Verification Process
                  </button>
                  
                  {isPolling && (
                    <div className="flex flex-col items-center justify-center gap-2 text-gray-600 mt-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
                      <div className="flex items-center gap-2">
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-lime-700" />
                        <span className="font-medium">Checking verification status in real-time...</span>
                      </div>
                      <p className="text-sm mt-1">This page will automatically update when verification is complete.</p>
                    </div>
                  )}
                  
                  <div className="pt-4 border-t border-gray-200">
                    <p className="text-gray-600 mb-4">
                      Already completed verification in another tab?
                    </p>
                    <button
                      onClick={handleCheckStatus}
                      className="text-lime-700 border border-lime-700 py-2 px-6 rounded-md hover:bg-lime-50 transition-colors"
                    >
                      Check Verification Status
                    </button>
                  </div>
                </div>
              </div>
            ) : null}
          </div>
        </div>
      </div>
    </div>
  );
};

export default KYCVerification;