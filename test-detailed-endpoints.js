const axios = require('axios');

// Test the reports endpoints with detailed output
async function testDetailedEndpoints() {
  const baseURL = 'http://localhost:9000';
  
  // Valid test token generated for testing
  const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************.yv5y9EHsnl8c00Gg56ILPuMzBpvZFt1VLMXG1qrVUVw';
  
  const headers = {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  };

  console.log('🧪 Testing Reports API Endpoints with Detailed Output...\n');

  // Test 1: Dashboard endpoint
  try {
    console.log('1. Testing dashboard endpoint...');
    const response = await axios.get(`${baseURL}/reports/dashboard`, { headers });
    console.log('✅ Dashboard endpoint response:');
    console.log(JSON.stringify(response.data, null, 2));
    console.log('\n' + '='.repeat(80) + '\n');
  } catch (error) {
    console.log('❌ Dashboard endpoint failed:', error.response?.status, error.response?.data || error.message);
  }

  // Test 2: Purchases endpoint
  try {
    console.log('2. Testing purchases endpoint...');
    const response = await axios.get(`${baseURL}/reports/purchases?groupBy=date&page=1&limit=5`, { headers });
    console.log('✅ Purchases endpoint response:');
    console.log(JSON.stringify(response.data, null, 2));
    console.log('\n' + '='.repeat(80) + '\n');
  } catch (error) {
    console.log('❌ Purchases endpoint failed:', error.response?.status, error.response?.data || error.message);
  }

  // Test 3: Redeems endpoint
  try {
    console.log('3. Testing redeems endpoint...');
    const response = await axios.get(`${baseURL}/reports/redeems?groupBy=date&page=1&limit=5`, { headers });
    console.log('✅ Redeems endpoint response:');
    console.log(JSON.stringify(response.data, null, 2));
    console.log('\n' + '='.repeat(80) + '\n');
  } catch (error) {
    console.log('❌ Redeems endpoint failed:', error.response?.status, error.response?.data || error.message);
  }

  // Test 4: Wallet balances endpoint
  try {
    console.log('4. Testing wallet balances endpoint...');
    const response = await axios.get(`${baseURL}/reports/wallet-balances`, { headers });
    console.log('✅ Wallet balances endpoint response:');
    console.log(JSON.stringify(response.data, null, 2));
    console.log('\n' + '='.repeat(80) + '\n');
  } catch (error) {
    console.log('❌ Wallet balances endpoint failed:', error.response?.status, error.response?.data || error.message);
  }

  // Test 5: Test different groupBy options
  try {
    console.log('5. Testing purchases grouped by user...');
    const response = await axios.get(`${baseURL}/reports/purchases?groupBy=user&page=1&limit=3`, { headers });
    console.log('✅ Purchases by user response:');
    console.log(JSON.stringify(response.data, null, 2));
    console.log('\n' + '='.repeat(80) + '\n');
  } catch (error) {
    console.log('❌ Purchases by user failed:', error.response?.status, error.response?.data || error.message);
  }

  console.log('🏁 Detailed test completed!');
}

// Run the tests
testDetailedEndpoints().catch(console.error);
