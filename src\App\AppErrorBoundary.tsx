import React, { Component, ErrorInfo } from 'react';
import { Fi<PERSON>lertTriangle, FiRefreshCw, FiHome } from 'react-icons/fi';
import RobustErrorBoundary from '../components/ErrorBoundary/RobustErrorBoundary';
import GlobalErrorHandler from '../components/ErrorBoundary/GlobalErrorHandler';

interface Props {
  children: React.ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

/**
 * App-level Error Boundary that catches errors in the entire application
 * and provides a user-friendly fallback UI in production.
 */
class AppErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log to error reporting service
    this.logErrorToService(error, errorInfo);

    this.setState({
      error,
      errorInfo
    });

    // Prevent the error from propagating to the console in production
    if (process.env.NODE_ENV !== 'development') {
      // This prevents the red error screen in production
      if (window && window.console && window.console.error) {
        const originalConsoleError = window.console.error;
        window.console.error = (...args) => {
          // Check if this is a React error
          const isReactError = args.some(arg =>
            typeof arg === 'string' &&
            (arg.includes('React will try to recreate this component tree') ||
             arg.includes('The above error occurred in the') ||
             arg.includes('Consider adding an error boundary'))
          );

          if (isReactError || args[0] === error ||
              (typeof args[0] === 'string' && args[0].includes(error.message))) {
            // Suppress this specific error from showing in console
            return;
          }
          originalConsoleError.apply(window.console, args);
        };

        // Restore original after a short delay
        setTimeout(() => {
          window.console.error = originalConsoleError;
        }, 1000);
      }
    }
  }

  logErrorToService(error: Error, errorInfo: ErrorInfo): void {
    // In a real app, you would send this to your error tracking service
    // like Sentry, LogRocket, etc.
    console.error("App-level error caught:", {
      error: error.toString(),
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      url: window.location.href,
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString()
    });

    // You could also log to your backend API
    // Example:
    // fetch('/api/log-error', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify({
    //     error: error.toString(),
    //     stack: error.stack,
    //     componentStack: errorInfo.componentStack,
    //     url: window.location.href,
    //     userAgent: navigator.userAgent,
    //     timestamp: new Date().toISOString()
    //   })
    // }).catch(console.error);
  }

  handleReload = (): void => {
    window.location.reload();
  };

  handleGoHome = (): void => {
    window.location.href = '/';
  };

  render() {
    return (
      <GlobalErrorHandler>
        <RobustErrorBoundary
          componentName="AppRoot"
          suppressConsoleError={true}
          showDetails={process.env.NODE_ENV === 'development'}
        >
          {this.props.children}
        </RobustErrorBoundary>
      </GlobalErrorHandler>
    );
  }
}

export default AppErrorBoundary;
