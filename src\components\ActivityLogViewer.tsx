import React, { useState, useEffect, ReactNode } from 'react';
import axios from 'axios';
import { useSelector } from 'react-redux';
import { RootState } from '../redux/store';
import { FaHistory, FaUserClock, FaCalendarAlt, FaInfoCircle } from 'react-icons/fa';

interface ActivityLog {
  _id: string;
  userId: {
    _id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  userRole: string;
  action: string;
  entityType: string;
  entityId: string;
  previousState: any;
  newState: any;
  timestamp: string;
  notes: string;
}

interface ActivityLogViewerProps {
  entityType: 'transaction' | 'transaction_purchase' | 'transaction_redeem' | 'game' | 'user' | 'wallet' | 'kyc' | 'password_reset' | 'postal_code' | 'other';
  entityId: string;
  buttonLabel?: string;
  buttonClassName?: string;
  children?: ReactNode;
}

const ActivityLogViewer: React.FC<ActivityLogViewerProps> = ({
  entityType,
  entityId,
  buttonLabel = 'View History',
  buttonClassName = 'px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600',
  children
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [logs, setLogs] = useState<ActivityLog[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { token } = useSelector((state: RootState) => state.auth);

  const fetchLogs = async () => {
    // Get the latest token from localStorage in case it was updated after component mounted
    const currentToken = token || localStorage.getItem('Token');
    
    if (!currentToken) {
      console.error('No authentication token available');
      setError('Authentication required');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Updated API endpoint to match the backend route configuration in app.js
      const response = await axios.get(
        `${process.env.REACT_APP_API_URL}/logs/entity/${entityType}/${entityId}`,
        {
          headers: { Authorization: `Bearer ${currentToken}` }
        }
      );

      if (response.data.success) {
        setLogs(response.data.data);
      } else {
        setError(response.data.message || 'Failed to fetch logs');
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || err.message || 'An error occurred';
      setError(errorMessage);
      console.error('Error fetching logs:', errorMessage, err);
    } finally {
      setLoading(false);
    }
  };

  // Handle tab changes in the admin dashboard
  const handleTabChange = React.useCallback(() => {
    if (isOpen) {
      fetchLogs();
    }
  }, [isOpen, fetchLogs]);

  // Listen for tab changes
  useEffect(() => {
    window.addEventListener('adminTabChanged', handleTabChange);
    
    return () => {
      window.removeEventListener('adminTabChanged', handleTabChange);
    };
  }, [handleTabChange]);

  // Initialize component and pre-fetch data when needed
  useEffect(() => {
    // Pre-fetch data when component mounts to have it ready
    // This ensures data is available immediately when modal opens
    if (entityId && entityType) {
      fetchLogs();
    }
    
    // Also fetch when modal opens or dependencies change
    if (isOpen) {
      fetchLogs();
    }
  }, [isOpen, entityId, entityType, token]);
  
  // Initialize on login
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'Token' && e.newValue) {
        fetchLogs();
      }
    };
    
    window.addEventListener('storage', handleStorageChange);
    
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [fetchLogs]);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  const formatState = (state: any) => {
    if (!state) return 'N/A';

    return (
      <div className="text-sm">
        {Object.entries(state).map(([key, value]) => (
          <div key={key} className="mb-1">
            <span className="font-semibold">{key}: </span>
            <span>{JSON.stringify(value)}</span>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div>
      <button
        className={buttonClassName}
        onClick={() => setIsOpen(true)}
      >
        {children || (
          <>
            <FaHistory className="inline mr-2" />
            {buttonLabel}
          </>
        )}
      </button>

      {isOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-4xl max-h-[90vh] overflow-hidden">
            <div className="p-4 border-b flex justify-between items-center bg-gray-50">
              <h2 className="text-xl font-semibold">
                <FaHistory className="inline mr-2" />
                Activity History
              </h2>
              <button
                onClick={() => setIsOpen(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                &times;
              </button>
            </div>

            <div className="p-4 overflow-y-auto max-h-[calc(90vh-8rem)]">
              {loading ? (
                <div className="flex justify-center items-center h-40">
                  <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-gray-900"></div>
                </div>
              ) : error ? (
                <div className="text-red-500 p-4 text-center">
                  <FaInfoCircle className="inline mr-2" />
                  {error}
                </div>
              ) : logs.length === 0 ? (
                <div className="text-gray-500 p-4 text-center">
                  <FaInfoCircle className="inline mr-2" />
                  No activity logs found for this item
                </div>
              ) : (
                <div className="space-y-4">
                  {logs.map((log) => (
                    <div key={log._id} className="border rounded-lg p-4 bg-gray-50">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <div className="font-semibold text-lg">{log.action}</div>
                          <div className="text-sm text-gray-600">
                            <FaUserClock className="inline mr-1" />
                            By {log.userId?.firstName || ''} {log.userId?.lastName || ''} ({log.userRole})
                          </div>
                        </div>
                        <div className="text-sm text-gray-600">
                          <FaCalendarAlt className="inline mr-1" />
                          {formatDate(log.timestamp)}
                        </div>
                      </div>

                      {log.notes && (
                        <div className="text-sm mb-2 italic">
                          "{log.notes}"
                        </div>
                      )}

                      <div className="grid grid-cols-2 gap-4 mt-3">
                        <div>
                          <div className="font-semibold mb-1 text-red-500">Previous State</div>
                          {formatState(log.previousState)}
                        </div>
                        <div>
                          <div className="font-semibold mb-1 text-green-500">New State</div>
                          {formatState(log.newState)}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <div className="p-4 border-t bg-gray-50 flex justify-end">
              <button
                onClick={() => setIsOpen(false)}
                className="px-4 py-2 bg-gray-300 text-gray-800 rounded hover:bg-gray-400"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ActivityLogViewer;
