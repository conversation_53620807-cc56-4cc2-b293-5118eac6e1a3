const mongoose = require("mongoose");
const gameRequestSchema = require("../Schema/gameRequestSchema");
const GameRequest = mongoose.model("GameRequest", gameRequestSchema);

class GameRequestModel {
  async getNextRequestId() {
    const maxRequest = await GameRequest.findOne({}, { requestId: 1 })
      .sort({ requestId: -1 })
      .limit(1);
    return maxRequest ? maxRequest.requestId + 1 : 1;
  }

  async add(data) {
    try {
      // Get the next request ID
      const nextRequestId = await this.getNextRequestId();

      // Create new game request with the generated ID
      const newGameRequest = new GameRequest({
        ...data,
        requestId: nextRequestId,
        sweepstakesBalance: 0, // Initialize balance to 0
      });

      console.log("newGameRequest===>", newGameRequest);
      const result = await newGameRequest.save();
      console.log("result===>", result);
      return result;
    } catch (err) {
      console.error("Error in GameRequestModel.add:", err);
      throw new Error("Error while saving game request: " + err.message);
    }
  }

  async findAll(requestType, page = 1, limit = 5, filters = {}) {
    try {
      console.log('Received filters:', filters);

      // Build the base query
      let queryConditions = {};

      // Add filter if requestType is provided
      if (requestType) {
        queryConditions.request_type = requestType;
      }

      // Apply additional filters
      if (filters.search) {
        const searchTerm = filters.search.trim();
        console.log('Processing search term:', searchTerm);

        // Check if search term is numeric (for requestId)
        const isNumeric = /^\d+$/.test(searchTerm);

        if (isNumeric) {
          // If it's numeric, search by requestId as number and gameName as string
          const searchNumber = parseInt(searchTerm);
          const searchRegex = new RegExp(searchTerm, 'i');
          queryConditions.$or = [
            { requestId: searchNumber },
            { gameName: searchRegex }
          ];
          console.log('Numeric search - requestId:', searchNumber, 'gameName regex:', searchRegex);
        } else {
          // If it's not numeric, only search by gameName
          const searchRegex = new RegExp(searchTerm, 'i');
          queryConditions.gameName = searchRegex;
          console.log('Text search - gameName regex only:', searchRegex);
        }
      }

      // Only add gameName filter if it's not already added by search
      if (filters.gameName && !queryConditions.gameName && !queryConditions.$or) {
        queryConditions.gameName = new RegExp(filters.gameName, 'i');
        console.log('Added separate gameName filter:', filters.gameName);
      }

      if (filters.status) {
        queryConditions.status = filters.status;
      }

      // Add passwordReset filter for password reset requests
      if (filters.passwordReset) {
        queryConditions.passwordReset = filters.passwordReset;
        console.log('Added passwordReset filter:', filters.passwordReset);
      }

      // Date range filter
      if (filters.startDate || filters.endDate) {
        queryConditions.createdAt = {};
        if (filters.startDate) {
          queryConditions.createdAt.$gte = new Date(filters.startDate);
        }
        if (filters.endDate) {
          // Add one day to endDate to include the entire end date
          const endDate = new Date(filters.endDate);
          endDate.setDate(endDate.getDate() + 1);
          queryConditions.createdAt.$lt = endDate;
        }
      }

      // Handle user filtering BEFORE the main query
      if (filters.userName || filters.userEmail) {
        console.log('Applying user filters:', { userName: filters.userName, userEmail: filters.userEmail });

        try {
          // Build user query conditions
          let userQueryConditions = {};

          if (filters.userName) {
            const nameRegex = new RegExp(filters.userName.trim(), 'i');
            // Search in firstName, lastName, and full name combinations
            // Note: username field doesn't exist in the schema, so we only search firstName and lastName
            userQueryConditions.$or = [
              { firstName: nameRegex },
              { lastName: nameRegex },
              // Also search for full name combinations
              { $expr: {
                $regexMatch: {
                  input: { $concat: ["$firstName", " ", "$lastName"] },
                  regex: filters.userName.trim(),
                  options: "i"
                }
              }},
              { $expr: {
                $regexMatch: {
                  input: { $concat: ["$lastName", " ", "$firstName"] },
                  regex: filters.userName.trim(),
                  options: "i"
                }
              }}
            ];
            console.log('User name search regex:', nameRegex);
          }

          if (filters.userEmail) {
            if (userQueryConditions.$or) {
              // If we already have userName conditions, add email as an AND condition
              userQueryConditions = {
                $and: [
                  { $or: userQueryConditions.$or },
                  { email: new RegExp(filters.userEmail.trim(), 'i') }
                ]
              };
            } else {
              // If only email filter
              userQueryConditions.email = new RegExp(filters.userEmail.trim(), 'i');
            }
          }

          console.log('User query conditions:', JSON.stringify(userQueryConditions, null, 2));

          // Get matching users first
          const { User } = require('./usersModel'); // Correct import path
          const matchingUsers = await User.find(userQueryConditions).select('_id firstName lastName email').lean();
          const matchingUserIds = matchingUsers.map(user => user._id);

          console.log('Found matching users:', matchingUserIds.length);
          console.log('Matching users details:', matchingUsers.map(u => ({
            id: u._id,
            firstName: u.firstName,
            lastName: u.lastName,
            email: u.email
          })));

          if (matchingUserIds.length === 0) {
            // No users match the criteria, return empty result
            console.log('No users found matching the criteria, returning empty result');
            return {
              games: [],
              totalItems: 0,
              totalPages: 0,
            };
          }

          // Add user filter to main query
          queryConditions.userId = { $in: matchingUserIds };
          console.log('Added userId filter to main query:', matchingUserIds);
        } catch (userFilterError) {
          console.error('Error in user filtering:', userFilterError);
          // If user filtering fails, continue without user filter
        }
      }

      console.log('Final Game Request Query Conditions:', JSON.stringify(queryConditions, null, 2));

      const totalItems = await GameRequest.countDocuments(queryConditions);
      const skip = (page - 1) * limit;

      let games = await GameRequest.find(queryConditions)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(parseInt(limit))
        .lean();

      // User filtering is now handled in the main query above, so we just return the results
      return {
        games,
        totalItems,
        totalPages: Math.ceil(totalItems / limit),
      };
    } catch (error) {
      throw new Error(`Error while finding game requests: ${error.message}`);
    }
  }

  async findOne(query) {
    try {
      const gameRequest = await GameRequest.findOne(query).lean();
      // Return null instead of throwing an error when no document is found
      // This allows the caller to handle the case when no document exists
      return gameRequest;
    } catch (err) {
      throw new Error("Error while finding game request...: " + err.message);
    }
  }

  async count(filter) {
    try {
      return await GameRequest.countDocuments(filter);
    } catch (err) {
      throw new Error("Error while counting gameRequests: " + err.message);
    }
  }

  async findById(id) {
    try {
      const gameRequest = await GameRequest.findById(id).lean();
      if (!gameRequest) {
        throw new Error("GameRequest not found");
      }
      return gameRequest;
    } catch (err) {
      throw new Error("Error while finding game request: " + err.message);
    }
  }

  async update(id, data) {
    try {
      if (!mongoose.Types.ObjectId.isValid(id)) {
        throw new Error("Invalid ID format");
      }
      console.log("Id in update--->>", id);
      console.log("Data to update--->>", data);

      const updatedGameRequest = await GameRequest.findByIdAndUpdate(id, data, {
        new: true,
        runValidators: true,
      });

      console.log("updatedGameRequest----->>", updatedGameRequest);
      if (!updatedGameRequest) {
        throw new Error("GameRequest not found");
      }
      return updatedGameRequest;
    } catch (err) {
      throw new Error("Error while updating game request: " + err.message);
    }
  }

  async updateBalance(id, amount, operation = "add") {
    try {
      if (!mongoose.Types.ObjectId.isValid(id)) {
        throw new Error("Invalid ID format");
      }

      const game = await GameRequest.findById(id);
      if (!game) {
        throw new Error("GameRequest not found");
      }

      // Calculate new balance
      const currentBalance = game.sweepstakesBalance || 0;
      const newBalance = operation === "add";
      currentBalance + amount;

      if (newBalance < 0) {
        throw new Error("Insufficient balance for operation");
      }

      // Update the balance
      const updatedGame = await GameRequest.findByIdAndUpdate(
        id,
        { sweepstakesBalance: newBalance },
        { new: true, runValidators: true }
      );

      console.log("Updated game balance--->>", updatedGame.sweepstakesBalance);
      return updatedGame;
    } catch (err) {
      throw new Error("Error while updating game balance: " + err.message);
    }
  }
}
module.exports = new GameRequestModel();

