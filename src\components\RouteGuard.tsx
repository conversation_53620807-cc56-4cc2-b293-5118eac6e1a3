import React from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '../redux/store';
import DocumentTitle from './DocumentTitle';

interface ProtectedRouteProps {
  children: React.ReactNode;
  allowedRoles: string[];
}

// Empty placeholder component that doesn't show a spinner
const LoadingPlaceholder = () => (
  <>
    <DocumentTitle title="LuckShack App" />
    <div className="invisible"></div>
  </>
);

const UnauthorizedMessage = () => (
  <>
    <DocumentTitle title="LuckShack App - Unauthorized" />
    <div
      style={{
        position: 'fixed',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        padding: '20px',
        backgroundColor: '#fff',
        boxShadow: '0 0 10px rgba(0,0,0,0.1)',
        borderRadius: '8px',
        textAlign: 'center',
        color: '#dc3545',
      }}
    >
      <h2>Unauthorized Access</h2>
      <p>You do not have permission to access this page.</p>
      <p>Please contact your administrator if you believe this is an error.</p>
      <button
        onClick={() => (window.location.href = '/')}
        className="mt-3 px-4 py-2 bg-lime-900 text-white rounded-lg"
      >
        Return to Home
      </button>
    </div>
  </>
);

const RouteGuard: React.FC<ProtectedRouteProps> = ({ children, allowedRoles }) => {
  const { user, loading }: any = useSelector((state: RootState) => state.auth);
  const token = localStorage.getItem('Token')
  // Don't show loader for seamless transition, just render children
  if (loading) {
    return <>{children}</>;
  }

  // If user exists but doesn't have the right role
  if (user && !allowedRoles.includes(user.user_type)) {
    return <UnauthorizedMessage />;
  }
  // If no user exists
  if (token === null) {
    window.location.href = '/login';
    return <LoadingPlaceholder />;
  }

  // If user exists and has the right role
  return (
    <>
      <DocumentTitle title="LuckShack App" />
      {children}
    </>
  );
};

export default RouteGuard;
