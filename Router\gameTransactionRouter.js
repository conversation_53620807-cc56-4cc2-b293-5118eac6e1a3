const express = require("express");
const router = express.Router();
const GameTransactionController = require("../Controller/gameTransactionController");
const auth = require('../auth/middleware');

// Purchase game items
router.post("/purchase", auth.verifyToken, GameTransactionController.handleGamePurchase);

// Redeem game rewards
router.post("/redeem", auth.verifyToken, GameTransactionController.handleGameRedeem);

// Get user's game transactions
router.get("/transactions/:userId", auth.verifyToken, GameTransactionController.getTransactions);

// Get all game transactions (for admin)
router.get("/transactions", auth.verifyToken, GameTransactionController.getAllTransactions);

// Get all game transactions (for admin) - original route kept for backward compatibility
router.get("/", auth.verifyToken, GameTransactionController.getAllTransactions);

// Test endpoint
router.get("/test", (req, res) => {
  res.status(200).json({ message: "Game transaction router is working!" });
});
router.get('/game-transactions',auth.verifyToken, GameTransactionController.getAllTransactions);
router.put('/game-transactions/:id',auth.verifyToken, GameTransactionController.updateTransactionStatus);

// Update game transaction status
// router.put("/:id", GameTransactionController.updateTransactionStatus);

module.exports = router;
