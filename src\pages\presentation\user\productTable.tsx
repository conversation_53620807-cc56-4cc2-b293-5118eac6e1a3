import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { toast } from 'react-toastify';

interface Product {
  _id: string;
  name: string;
  imageName: string;
  description: string;
  status: boolean;
}

const ProductTable: React.FC = () => {
  const [productData, setProductData] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [isConfirmModalVisible, setIsConfirmModalVisible] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const userId = localStorage.getItem('userId');

  const fetchProduct = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`${process.env.REACT_APP_API_URL}/products/available/${userId}`, {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          Authorization: `Bearer ${localStorage.getItem('Token')}`,
        },
      });

      // Only show products with status set to true
      const activeProducts = response.data.data.filter((product: Product) => product.status === true);
      setProductData(activeProducts);
    } catch (error: any) {
      console.error('Error fetching products:', error);
      toast.error('Failed to load products');
    } finally {
      setLoading(false);
    }
  };

  const handleRequestSystem = (product: Product) => {
    if (product.status === false) {
      toast.error('This system is currently unavailable');
      return;
    }
    setSelectedProduct(product);
    setIsConfirmModalVisible(true);
  };

  const handleConfirmSubmit = async () => {
    if (!selectedProduct) return;

    const requestData = {
      gameName: selectedProduct.name,
      productId: selectedProduct._id,
      userId: userId,
      date: new Date().toISOString().split('T')[0],
      request_type: 'gameRequest',
      description: selectedProduct.description,
      passwpasswordReset: 'Approve',
    };

    try {
      setIsSubmitting(true);
      const response = await axios.post(
        `${process.env.REACT_APP_API_URL}/game-request/add`,
        requestData,
        {
          headers: {
            Accept: 'application/json',
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            Authorization: `Bearer ${localStorage.getItem('Token')}`,
          },
        }
      );

      if (response.data.data && response.data.data._id) {
        toast.success('The game ID and password will be sent to your registered email shortly!');
        fetchProduct(); // Refresh the product list
      } else {
        toast.warning('Request submitted but no confirmation received');
      }
    } catch (error: any) {
      toast.warn(error.response?.data?.error || 'Game already Requested');
    } finally {
      setIsSubmitting(false);
      setIsConfirmModalVisible(false);
      setSelectedProduct(null);
    }
  };

  const handleCancelSubmit = () => {
    setIsConfirmModalVisible(false);
    setSelectedProduct(null);
  };

  useEffect(() => {
    fetchProduct();
  }, [userId]);

  return (
    <div className="rounded-lg w-full">
      {/* Desktop View - Visible on medium screens and up */}
      <div className="hidden md:!block lg:!block xl:!block 2xl:!block">
        <div className="overflow-auto" style={{ maxHeight: 'calc(100vh - 350px)' }}>
          <div className="w-full inline-block align-middle">
            <div className="border border-gray-200 rounded-lg">
              <table className="w-full divide-y divide-gray-200 border-collapse table-fixed">
                <thead className="bg-gray-50 sticky top-0 z-10">
                  <tr>
                    <th className="px-3 py-3 text-left text-sm sm:text-lg md:text-xl font-semibold text-gray-600 w-[20%]">
                      GAME
                    </th>
                    <th className="px-3 py-3 text-left text-sm sm:text-lg md:text-xl font-semibold text-gray-600 w-[40%]">
                      DESCRIPTION
                    </th>
                    <th className="px-3 py-3 text-left text-sm sm:text-lg md:text-xl font-semibold text-gray-600 w-[15%]">
                      SYSTEM IMAGE
                    </th>
                    <th className="px-3 py-3 text-left text-sm sm:text-lg md:text-xl font-semibold text-gray-600 w-[25%]">
                      REQUEST
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {productData.map((product) => (
                    <tr key={product._id} className={`hover:bg-gray-50 ${product.status === false ? 'opacity-60' : ''} h-20`}>
                      <td className="px-3 py-3 text-sm sm:text-lg md:text-xl text-gray-900 align-middle">
                        <div className="truncate max-w-[150px] md:max-w-[200px]">
                          {product.name}
                          {product.status === false && (
                            <span className="ml-2 px-2 py-1 bg-red-100 text-red-800 text-xs sm:text-sm rounded-full">
                              Unavailable
                            </span>
                          )}
                        </div>
                      </td>
                      <td className="px-3 py-3 text-sm sm:text-lg md:text-xl text-gray-900 align-middle">
                        <div className="line-clamp-2">
                          {product.description ? (
                            product.description
                          ) : 'Request Games Now'}
                        </div>
                      </td>
                      <td className="px-3 py-3 align-middle">
                        {product.imageName ? (
                          <div className="w-16 h-16 sm:w-20 sm:h-20 md:w-24 md:h-24 rounded-md overflow-hidden border border-gray-200">
                            <img
                              src={`https://s3.wasabisys.com/productimage/${product.imageName}`}
                              alt={product.name}
                              className="w-full h-full object-cover"
                            />
                          </div>
                        ) : (
                          <div className="w-16 h-16 sm:w-20 sm:h-20 md:w-24 md:h-24 bg-gray-100 flex items-center justify-center rounded-md border border-gray-200">
                            <span className="text-gray-400 text-xs sm:text-sm">No image</span>
                          </div>
                        )}
                      </td>
                      <td className="px-3 py-3 align-middle">
                        <button
                          onClick={() => handleRequestSystem(product)}
                          className={`${
                            product.status === false
                              ? 'bg-gray-400 cursor-not-allowed'
                              : 'bg-[#4B5E2F] hover:bg-[#3e4d26]'
                          } text-white px-3 py-2 sm:px-4 sm:py-2 rounded text-sm sm:text-base md:text-lg font-medium transition-colors`}
                          disabled={product.status === false}
                        >
                          {product.status === false ? 'Unavailable' : 'Request System'}
                        </button>
                      </td>
                    </tr>
                  ))}
                  {productData.length === 0 && !loading && (
                    <tr>
                      <td colSpan={4} className="px-3 py-6 text-center text-sm sm:text-lg text-gray-500">
                        No systems available
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile View - Only visible on small screens */}
      <div className="md:hidden">
        <div className="overflow-y-auto pb-4" style={{ maxHeight: 'calc(100vh - 350px)' }}>
          {productData.length === 0 && !loading ? (
            <div className="p-4 text-center text-gray-500">
              No systems available
            </div>
          ) : (
            <div className="space-y-4 p-2">
              {productData.map((product) => (
                <div
                  key={product._id}
                  className={`bg-white rounded-lg border border-gray-200 p-4 shadow-sm min-h-[180px] flex flex-col ${product.status === false ? 'opacity-60' : ''}`}
                >
                  <div className="flex justify-between items-start mb-3">
                    <div className="pr-2 flex-1">
                      <h3 className="text-lg font-semibold text-gray-900 truncate">{product.name}</h3>
                      {product.status === false && (
                        <span className="inline-block mt-1 px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">
                          Unavailable
                        </span>
                      )}
                    </div>
                    {product.imageName ? (
                      <div className="w-16 h-16 rounded-md overflow-hidden border border-gray-200 flex-shrink-0 ml-2">
                        <img
                          src={`https://s3.wasabisys.com/productimage/${product.imageName}`}
                          alt={product.name}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    ) : (
                      <div className="w-16 h-16 bg-gray-100 flex items-center justify-center rounded-md border border-gray-200 flex-shrink-0 ml-2">
                        <span className="text-gray-400 text-xs">No image</span>
                      </div>
                    )}
                  </div>

                  <div className="text-sm text-gray-600 mb-4 line-clamp-3">
                    {product.description ? product.description : 'Request Games Now'}
                  </div>

                  <button
                    onClick={() => handleRequestSystem(product)}
                    className={`${
                      product.status === false
                        ? 'bg-gray-400 cursor-not-allowed'
                        : 'bg-[#4B5E2F] hover:bg-[#3e4d26]'
                    } text-white px-4 py-2 rounded text-sm font-medium transition-colors w-full h-10 flex items-center justify-center mt-auto`}
                    disabled={product.status === false}
                  >
                    {product.status === false ? 'Unavailable' : 'Request System'}
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {loading && (
        <div className="flex justify-center items-center p-4 border-t border-gray-200">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          <span className="ml-3 text-sm sm:text-base text-gray-500">Loading systems...</span>
        </div>
      )}

      {isConfirmModalVisible && selectedProduct && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white p-4 sm:p-6 md:p-8 rounded-lg shadow-lg w-full max-w-md sm:max-w-lg md:max-w-xl">
            <h2 className="text-xl sm:text-2xl md:text-3xl font-semibold mb-3 sm:mb-4 md:mb-6">Confirm System Request</h2>
            <p className="text-base sm:text-lg md:text-xl text-gray-600 mb-2 sm:mb-3 md:mb-4">
              Are you sure you want to request the system: <span className="font-medium text-lime-900">{selectedProduct.name}</span>?
            </p>
            <p className="text-sm sm:text-base md:text-lg text-gray-500 mb-4 sm:mb-6 md:mb-8">
              The game ID and password will be sent to your registered email.
            </p>
            <div className="flex flex-col sm:flex-row justify-between gap-3 sm:gap-4">
              <button
                className="bg-gray-300 px-4 py-2 sm:px-5 sm:py-2.5 md:px-6 md:py-3 rounded-md text-base sm:text-lg md:text-xl hover:bg-gray-400 w-full sm:w-1/2 order-2 sm:order-1"
                onClick={handleCancelSubmit}
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <button
                className="bg-[#495e26] text-white px-4 py-2 sm:px-5 sm:py-2.5 md:px-6 md:py-3 rounded-md text-base sm:text-lg md:text-xl hover:bg-[#3e4d26] w-full sm:w-1/2 flex items-center justify-center order-1 sm:order-2"
                onClick={handleConfirmSubmit}
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 sm:h-5 sm:w-5 border-b-2 border-white mr-2 sm:mr-3"></div>
                    <span>Processing...</span>
                  </>
                ) : (
                  'Confirm'
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProductTable;