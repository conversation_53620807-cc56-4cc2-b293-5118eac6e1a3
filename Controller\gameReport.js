const mongoose = require("mongoose");
const gameTransactionSchema = require("../Schema/gametransaction");
const GameTransaction = mongoose.model("GameTransaction", gameTransactionSchema);

class GameReportController {
  async getGameTransactions(req, res) {
    try {
      const {
        page = 1,
        limit = 10,
        startDate,
        endDate,
        gameName,
        transactionType,
        status,
        minAmount,
        maxAmount,
      } = req.query;

      const match = {};

      if (startDate && endDate) {
        match.createdAt = {
          $gte: new Date(startDate),
          $lte: new Date(endDate),
        };
      }

      if (gameName) {
        match.gameName = { $regex: gameName, $options: "i" };
      }

      if (transactionType) {
        match.transactionType = transactionType;
      }

      if (status) {
        match.status = status;
      }

      if (minAmount || maxAmount) {
        match.amount = {};
        if (minAmount) match.amount.$gte = parseFloat(minAmount);
        if (maxAmount) match.amount.$lte = parseFloat(maxAmount);
      }

      const skip = (parseInt(page) - 1) * parseInt(limit);
      const parsedLimit = parseInt(limit);

      const pipeline = [
        { $match: match },
        {
          $facet: {
            data: [
              { $sort: { createdAt: -1 } },
              { $skip: skip },
              { $limit: parsedLimit },
            ],
            totalCount: [{ $count: "count" }],
            totalAmount: [{
              $group: {
                _id: null,
                total: { $sum: "$amount" }
              }
            }]
          }
        }
      ];

      const [result] = await GameTransaction.aggregate(pipeline);

      return res.status(200).send({
        success: true,
        data: result.data || [],
        totalCount: result.totalCount[0]?.count || 0,
        totalAmount: result.totalAmount[0]?.total || 0,
        page: parseInt(page),
        limit: parsedLimit,
      });

    } catch (error) {
      console.error("Error fetching game transactions:", error);
      return res.status(500).send({
        success: false,
        error: "Internal server error",
      });
    }
  }
}

module.exports = new GameReportController();
