const mongoose = require('mongoose');
const moment = require('moment-timezone');
const GameRequestModel = require('../Model/gameRequestModel');
const RedeemRequestModel = require('../Model/redeemRequestModel');
const UserWalletModel = require('../Model/userWalletModal');
const UsersModel = require('../Model/usersModel');
const OrderModel = require('../Model/orderModel');

/**
 * Reports Controller
 * Handles all analytics and reporting functionality
 * Follows industry best practices with proper error handling and validation
 */
class ReportsController {

  /**
   * Get comprehensive dashboard analytics
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getDashboardAnalytics(req, res) {
    try {
      // Validate user role
      if (!this.hasReportsAccess(req.user)) {
        return res.status(403).json({
          success: false,
          message: 'Access denied. Reports access requires Manage or Admin role.',
          error: 'INSUFFICIENT_PERMISSIONS'
        });
      }

      const { startDate, endDate } = this.validateAndParseDateRange(req.query);

      // Get all analytics data in parallel for better performance
      const [
        purchasesData,
        redeemsData,
        totalSweepsBalance
      ] = await Promise.all([
        this.getPurchasesAnalytics(startDate, endDate),
        this.getRedeemsAnalytics(startDate, endDate),
        this.getTotalSweepsBalance()
      ]);

      const response = {
        success: true,
        data: {
          summary: {
            totalPurchases: purchasesData.total,
            totalPurchaseAmount: purchasesData.totalAmount,
            totalRedeems: redeemsData.total,
            totalRedeemAmount: redeemsData.totalAmount,
            totalSweepsBalance: totalSweepsBalance
          },
          purchases: purchasesData,
          redeems: redeemsData,
          dateRange: {
            startDate: startDate ? moment(startDate).format('YYYY-MM-DD') : null,
            endDate: endDate ? moment(endDate).format('YYYY-MM-DD') : null
          }
        },
        timestamp: new Date().toISOString()
      };

      res.status(200).json(response);

    } catch (error) {
      console.error('Error in getDashboardAnalytics:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch dashboard analytics',
        error: process.env.NODE_ENV === 'development' ? error.message : 'INTERNAL_SERVER_ERROR'
      });
    }
  }

  /**
   * Get purchases analytics (card/crypto payments)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getPurchasesReport(req, res) {
    try {
      if (!this.hasReportsAccess(req.user)) {
        return res.status(403).json({
          success: false,
          message: 'Access denied. Reports access requires Manage or Admin role.',
          error: 'INSUFFICIENT_PERMISSIONS'
        });
      }

      const { startDate, endDate } = this.validateAndParseDateRange(req.query);
      const { groupBy = 'date', page = 1, limit = 50 } = req.query;

      const data = await this.getPurchasesAnalytics(startDate, endDate, groupBy, page, limit);

      res.status(200).json({
        success: true,
        data,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          hasMore: data.items && data.items.length === parseInt(limit)
        },
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Error in getPurchasesReport:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch purchases report',
        error: process.env.NODE_ENV === 'development' ? error.message : 'INTERNAL_SERVER_ERROR'
      });
    }
  }

  /**
   * Get redeems analytics
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getRedeemsReport(req, res) {
    try {
      if (!this.hasReportsAccess(req.user)) {
        return res.status(403).json({
          success: false,
          message: 'Access denied. Reports access requires Manage or Admin role.',
          error: 'INSUFFICIENT_PERMISSIONS'
        });
      }

      const { startDate, endDate } = this.validateAndParseDateRange(req.query);
      const { groupBy = 'date', page = 1, limit = 50 } = req.query;

      const data = await this.getRedeemsAnalytics(startDate, endDate, groupBy, page, limit);

      res.status(200).json({
        success: true,
        data,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          hasMore: data.items && data.items.length === parseInt(limit)
        },
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Error in getRedeemsReport:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch redeems report',
        error: process.env.NODE_ENV === 'development' ? error.message : 'INTERNAL_SERVER_ERROR'
      });
    }
  }

  /**
   * Get wallet balances report
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getWalletBalancesReport(req, res) {
    try {
      if (!this.hasReportsAccess(req.user)) {
        return res.status(403).json({
          success: false,
          message: 'Access denied. Reports access requires Manage or Admin role.',
          error: 'INSUFFICIENT_PERMISSIONS'
        });
      }

      const [totalSweepsBalance, detailedBalances] = await Promise.all([
        this.getTotalSweepsBalance(),
        this.getDetailedWalletBalances()
      ]);

      res.status(200).json({
        success: true,
        data: {
          totalSweepsBalance,
          detailedBalances
        },
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Error in getWalletBalancesReport:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch wallet balances report',
        error: process.env.NODE_ENV === 'development' ? error.message : 'INTERNAL_SERVER_ERROR'
      });
    }
  }

  // ==================== HELPER METHODS ====================

  /**
   * Check if user has access to reports
   * @param {Object} user - User object from JWT token
   * @returns {boolean} - Whether user has access
   */
  hasReportsAccess(user) {
    if (!user || !user.role) {
      return false;
    }

    const allowedRoles = ['Admin', 'Manage'];
    return allowedRoles.includes(user.role);
  }

  /**
   * Validate and parse date range from query parameters
   * @param {Object} query - Query parameters
   * @returns {Object} - Parsed start and end dates
   */
  validateAndParseDateRange(query) {
    let { startDate, endDate } = query;

    // If no dates provided, default to last 30 days
    if (!startDate && !endDate) {
      endDate = moment().format('YYYY-MM-DD');
      startDate = moment().subtract(30, 'days').format('YYYY-MM-DD');
    }

    // Validate date formats
    if (startDate && !moment(startDate, 'YYYY-MM-DD', true).isValid()) {
      throw new Error('Invalid startDate format. Use YYYY-MM-DD');
    }

    if (endDate && !moment(endDate, 'YYYY-MM-DD', true).isValid()) {
      throw new Error('Invalid endDate format. Use YYYY-MM-DD');
    }

    // Convert to Date objects for database queries
    const parsedStartDate = startDate ? moment(startDate).startOf('day').toDate() : null;
    const parsedEndDate = endDate ? moment(endDate).endOf('day').toDate() : null;

    return {
      startDate: parsedStartDate,
      endDate: parsedEndDate
    };
  }

  /**
   * Get purchases analytics from Order documents (card/crypto payments)
   * @param {Date} startDate - Start date for filtering
   * @param {Date} endDate - End date for filtering
   * @param {string} groupBy - Grouping option (date, game, user)
   * @param {number} page - Page number for pagination
   * @param {number} limit - Items per page
   * @returns {Object} - Purchases analytics data
   */
  async getPurchasesAnalytics(startDate, endDate, groupBy = 'date', page = 1, limit = 50) {
    try {
      const Order = mongoose.model('Order');

      // Build match conditions for purchases (card or crypto payments)
      const matchConditions = {
        paymentMethod: { $in: ['card', 'crypto'] },
        status: { $in: ['Completed', 'Success', 'Approved'] } // Only successful purchases
      };

      // Add date filter based on completion date (updatedAt when status changed to completed)
      if (startDate || endDate) {
        matchConditions.updatedAt = {};
        if (startDate) matchConditions.updatedAt.$gte = startDate;
        if (endDate) matchConditions.updatedAt.$lte = endDate;
      }

      // Build aggregation pipeline
      let pipeline = [
        { $match: matchConditions }
      ];

      // Add grouping based on groupBy parameter
      if (groupBy === 'date') {
        pipeline.push(
          {
            $group: {
              _id: {
                $dateToString: {
                  format: '%Y-%m-%d',
                  date: '$updatedAt',
                  timezone: 'UTC'
                }
              },
              count: { $sum: 1 },
              totalAmount: {
                $sum: {
                  $toDouble: {
                    $ifNull: ['$price', 0]
                  }
                }
              },
              paymentMethods: { $push: '$paymentMethod' }
            }
          },
          { $sort: { _id: 1 } }
        );
      } else if (groupBy === 'game') {
        pipeline.push(
          {
            $group: {
              _id: '$gameId',
              count: { $sum: 1 },
              totalAmount: {
                $sum: {
                  $toDouble: {
                    $ifNull: ['$price', 0]
                  }
                }
              },
              latestPurchase: { $max: '$updatedAt' }
            }
          },
          { $sort: { totalAmount: -1 } }
        );
      } else if (groupBy === 'user') {
        pipeline.push(
          {
            $group: {
              _id: '$userId',
              count: { $sum: 1 },
              totalAmount: {
                $sum: {
                  $toDouble: {
                    $ifNull: ['$price', 0]
                  }
                }
              },
              latestPurchase: { $max: '$updatedAt' }
            }
          },
          { $sort: { totalAmount: -1 } }
        );
      }

      // Add pagination
      const skip = (page - 1) * limit;
      pipeline.push(
        { $skip: skip },
        { $limit: parseInt(limit) }
      );

      const [results, totalCount, totalAmountResult] = await Promise.all([
        Order.aggregate(pipeline),
        Order.countDocuments(matchConditions),
        Order.aggregate([
          { $match: matchConditions },
          {
            $group: {
              _id: null,
              totalAmount: {
                $sum: {
                  $toDouble: {
                    $ifNull: ['$price', 0]
                  }
                }
              }
            }
          }
        ])
      ]);

      return {
        total: totalCount,
        totalAmount: totalAmountResult.length > 0 ? totalAmountResult[0].totalAmount : 0,
        items: results,
        groupBy
      };

    } catch (error) {
      console.error('Error in getPurchasesAnalytics:', error);
      throw error;
    }
  }

  /**
   * Get redeems analytics from Order documents (push-to-card payments)
   * @param {Date} startDate - Start date for filtering
   * @param {Date} endDate - End date for filtering
   * @param {string} groupBy - Grouping option (date, user, game)
   * @param {number} page - Page number for pagination
   * @param {number} limit - Items per page
   * @returns {Object} - Redeems analytics data
   */
  async getRedeemsAnalytics(startDate, endDate, groupBy = 'date', page = 1, limit = 50) {
    try {
      const Order = mongoose.model('Order');

      // Build match conditions for redeems (push-to-card payments)
      const matchConditions = {
        paymentMethod: 'push-to-card',
        status: { $in: ['Completed', 'Success', 'Approved'] } // Only successful redeems
      };

      // Add date filter based on completion date (updatedAt when status changed to completed)
      if (startDate || endDate) {
        matchConditions.updatedAt = {};
        if (startDate) matchConditions.updatedAt.$gte = startDate;
        if (endDate) matchConditions.updatedAt.$lte = endDate;
      }

      // Build aggregation pipeline
      let pipeline = [
        { $match: matchConditions }
      ];

      // Add grouping based on groupBy parameter
      if (groupBy === 'date') {
        pipeline.push(
          {
            $group: {
              _id: {
                $dateToString: {
                  format: '%Y-%m-%d',
                  date: '$updatedAt',
                  timezone: 'UTC'
                }
              },
              count: { $sum: 1 },
              totalAmount: {
                $sum: {
                  $toDouble: {
                    $ifNull: ['$price', 0]
                  }
                }
              },
              games: { $push: '$gameId' }
            }
          },
          { $sort: { _id: 1 } }
        );
      } else if (groupBy === 'game') {
        pipeline.push(
          {
            $group: {
              _id: '$gameId',
              count: { $sum: 1 },
              totalAmount: {
                $sum: {
                  $toDouble: {
                    $ifNull: ['$price', 0]
                  }
                }
              },
              latestRedeem: { $max: '$updatedAt' }
            }
          },
          { $sort: { totalAmount: -1 } }
        );
      } else if (groupBy === 'user') {
        pipeline.push(
          {
            $group: {
              _id: '$userId',
              count: { $sum: 1 },
              totalAmount: {
                $sum: {
                  $toDouble: {
                    $ifNull: ['$price', 0]
                  }
                }
              },
              latestRedeem: { $max: '$updatedAt' }
            }
          },
          { $sort: { totalAmount: -1 } }
        );
      }

      // Add pagination
      const skip = (page - 1) * limit;
      pipeline.push(
        { $skip: skip },
        { $limit: parseInt(limit) }
      );

      const [results, totalCount, totalAmountResult] = await Promise.all([
        Order.aggregate(pipeline),
        Order.countDocuments(matchConditions),
        Order.aggregate([
          { $match: matchConditions },
          {
            $group: {
              _id: null,
              totalAmount: {
                $sum: {
                  $toDouble: {
                    $ifNull: ['$price', 0]
                  }
                }
              }
            }
          }
        ])
      ]);

      return {
        total: totalCount,
        totalAmount: totalAmountResult.length > 0 ? totalAmountResult[0].totalAmount : 0,
        items: results,
        groupBy
      };

    } catch (error) {
      console.error('Error in getRedeemsAnalytics:', error);
      throw error;
    }
  }

  /**
   * Get total sweeps coin balance across all accounts
   * @returns {number} - Total sweeps coin balance
   */
  async getTotalSweepsBalance() {
    try {
      const Wallet = mongoose.model('Wallet');

      const result = await Wallet.aggregate([
        {
          $group: {
            _id: null,
            totalSwipeCoins: { $sum: '$balance.swipeCoins' },
            totalUnplayedSwipeCoins: { $sum: '$balance.unplayedSwipeCoins' },
            totalRedeemableSwipeCoins: { $sum: '$balance.redeemableSwipeCoins' }
          }
        }
      ]);

      if (result.length === 0) {
        return {
          totalSwipeCoins: 0,
          totalUnplayedSwipeCoins: 0,
          totalRedeemableSwipeCoins: 0,
          grandTotal: 0
        };
      }

      const data = result[0];
      return {
        totalSwipeCoins: data.totalSwipeCoins || 0,
        totalUnplayedSwipeCoins: data.totalUnplayedSwipeCoins || 0,
        totalRedeemableSwipeCoins: data.totalRedeemableSwipeCoins || 0,
        grandTotal: (data.totalSwipeCoins || 0) + (data.totalUnplayedSwipeCoins || 0) + (data.totalRedeemableSwipeCoins || 0)
      };

    } catch (error) {
      console.error('Error in getTotalSweepsBalance:', error);
      throw error;
    }
  }

  /**
   * Get detailed wallet balances breakdown
   * @returns {Object} - Detailed wallet balances
   */
  async getDetailedWalletBalances() {
    try {
      const Wallet = mongoose.model('Wallet');

      const result = await Wallet.aggregate([
        {
          $group: {
            _id: null,
            totalWallets: { $sum: 1 },
            totalGoldCoins: { $sum: '$balance.goldCoins' },
            totalSwipeCoins: { $sum: '$balance.swipeCoins' },
            totalUnplayedSwipeCoins: { $sum: '$balance.unplayedSwipeCoins' },
            totalRedeemableSwipeCoins: { $sum: '$balance.redeemableSwipeCoins' },
            avgGoldCoins: { $avg: '$balance.goldCoins' },
            avgSwipeCoins: { $avg: '$balance.swipeCoins' }
          }
        }
      ]);

      if (result.length === 0) {
        return {
          totalWallets: 0,
          totalGoldCoins: 0,
          totalSwipeCoins: 0,
          totalUnplayedSwipeCoins: 0,
          totalRedeemableSwipeCoins: 0,
          avgGoldCoins: 0,
          avgSwipeCoins: 0
        };
      }

      return result[0];

    } catch (error) {
      console.error('Error in getDetailedWalletBalances:', error);
      throw error;
    }
  }
}

module.exports = new ReportsController();
