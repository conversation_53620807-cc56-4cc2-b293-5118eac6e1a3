const mongoose = require('mongoose');
const moment = require('moment-timezone');
const GameRequestModel = require('../Model/gameRequestModel');
const RedeemRequestModel = require('../Model/redeemRequestModel');
const UserWalletModel = require('../Model/userWalletModal');
const UsersModel = require('../Model/usersModel');

/**
 * Reports Controller
 * Handles all analytics and reporting functionality
 * Follows industry best practices with proper error handling and validation
 */
class ReportsController {

  /**
   * Get comprehensive dashboard analytics
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getDashboardAnalytics(req, res) {
    try {
      // Validate user role
      if (!this.hasReportsAccess(req.user)) {
        return res.status(403).json({
          success: false,
          message: 'Access denied. Reports access requires Manage or Admin role.',
          error: 'INSUFFICIENT_PERMISSIONS'
        });
      }

      const { startDate, endDate } = this.validateAndParseDateRange(req.query);

      // Get all analytics data in parallel for better performance
      const [
        gamePurchasesData,
        redeemsData,
        totalSweepsBalance,
        goldCoinPurchases
      ] = await Promise.all([
        this.getGamePurchasesAnalytics(startDate, endDate),
        this.getRedeemsAnalytics(startDate, endDate),
        this.getTotalSweepsBalance(),
        this.getGoldCoinPurchases(startDate, endDate)
      ]);

      const response = {
        success: true,
        data: {
          summary: {
            totalGamePurchases: gamePurchasesData.total,
            totalRedeems: redeemsData.total,
            totalSweepsBalance: totalSweepsBalance,
            totalGoldCoinPurchases: goldCoinPurchases.total
          },
          gamePurchases: gamePurchasesData,
          redeems: redeemsData,
          goldCoinPurchases: goldCoinPurchases,
          dateRange: {
            startDate: startDate ? moment(startDate).format('YYYY-MM-DD') : null,
            endDate: endDate ? moment(endDate).format('YYYY-MM-DD') : null
          }
        },
        timestamp: new Date().toISOString()
      };

      res.status(200).json(response);

    } catch (error) {
      console.error('Error in getDashboardAnalytics:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch dashboard analytics',
        error: process.env.NODE_ENV === 'development' ? error.message : 'INTERNAL_SERVER_ERROR'
      });
    }
  }

  /**
   * Get game purchases analytics
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getGamePurchasesReport(req, res) {
    try {
      if (!this.hasReportsAccess(req.user)) {
        return res.status(403).json({
          success: false,
          message: 'Access denied. Reports access requires Manage or Admin role.',
          error: 'INSUFFICIENT_PERMISSIONS'
        });
      }

      const { startDate, endDate } = this.validateAndParseDateRange(req.query);
      const { groupBy = 'date', page = 1, limit = 50 } = req.query;

      const data = await this.getGamePurchasesAnalytics(startDate, endDate, groupBy, page, limit);

      res.status(200).json({
        success: true,
        data,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          hasMore: data.items && data.items.length === parseInt(limit)
        },
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Error in getGamePurchasesReport:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch game purchases report',
        error: process.env.NODE_ENV === 'development' ? error.message : 'INTERNAL_SERVER_ERROR'
      });
    }
  }

  /**
   * Get redeems analytics
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getRedeemsReport(req, res) {
    try {
      if (!this.hasReportsAccess(req.user)) {
        return res.status(403).json({
          success: false,
          message: 'Access denied. Reports access requires Manage or Admin role.',
          error: 'INSUFFICIENT_PERMISSIONS'
        });
      }

      const { startDate, endDate } = this.validateAndParseDateRange(req.query);
      const { groupBy = 'date', page = 1, limit = 50 } = req.query;

      const data = await this.getRedeemsAnalytics(startDate, endDate, groupBy, page, limit);

      res.status(200).json({
        success: true,
        data,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          hasMore: data.items && data.items.length === parseInt(limit)
        },
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Error in getRedeemsReport:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch redeems report',
        error: process.env.NODE_ENV === 'development' ? error.message : 'INTERNAL_SERVER_ERROR'
      });
    }
  }

  /**
   * Get wallet balances report
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getWalletBalancesReport(req, res) {
    try {
      if (!this.hasReportsAccess(req.user)) {
        return res.status(403).json({
          success: false,
          message: 'Access denied. Reports access requires Manage or Admin role.',
          error: 'INSUFFICIENT_PERMISSIONS'
        });
      }

      const [totalSweepsBalance, goldCoinData, detailedBalances] = await Promise.all([
        this.getTotalSweepsBalance(),
        this.getGoldCoinPurchases(),
        this.getDetailedWalletBalances()
      ]);

      res.status(200).json({
        success: true,
        data: {
          totalSweepsBalance,
          totalGoldCoins: goldCoinData.total,
          detailedBalances
        },
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Error in getWalletBalancesReport:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch wallet balances report',
        error: process.env.NODE_ENV === 'development' ? error.message : 'INTERNAL_SERVER_ERROR'
      });
    }
  }

  // ==================== HELPER METHODS ====================

  /**
   * Check if user has access to reports
   * @param {Object} user - User object from JWT token
   * @returns {boolean} - Whether user has access
   */
  hasReportsAccess(user) {
    if (!user || !user.role) {
      return false;
    }

    const allowedRoles = ['Admin', 'Manage'];
    return allowedRoles.includes(user.role);
  }

  /**
   * Validate and parse date range from query parameters
   * @param {Object} query - Query parameters
   * @returns {Object} - Parsed start and end dates
   */
  validateAndParseDateRange(query) {
    let { startDate, endDate } = query;

    // If no dates provided, default to last 30 days
    if (!startDate && !endDate) {
      endDate = moment().format('YYYY-MM-DD');
      startDate = moment().subtract(30, 'days').format('YYYY-MM-DD');
    }

    // Validate date formats
    if (startDate && !moment(startDate, 'YYYY-MM-DD', true).isValid()) {
      throw new Error('Invalid startDate format. Use YYYY-MM-DD');
    }

    if (endDate && !moment(endDate, 'YYYY-MM-DD', true).isValid()) {
      throw new Error('Invalid endDate format. Use YYYY-MM-DD');
    }

    // Convert to Date objects for database queries
    const parsedStartDate = startDate ? moment(startDate).startOf('day').toDate() : null;
    const parsedEndDate = endDate ? moment(endDate).endOf('day').toDate() : null;

    return {
      startDate: parsedStartDate,
      endDate: parsedEndDate
    };
  }

  /**
   * Get game purchases analytics
   * @param {Date} startDate - Start date for filtering
   * @param {Date} endDate - End date for filtering
   * @param {string} groupBy - Grouping option (date, game, user)
   * @param {number} page - Page number for pagination
   * @param {number} limit - Items per page
   * @returns {Object} - Game purchases analytics data
   */
  async getGamePurchasesAnalytics(startDate, endDate, groupBy = 'date', page = 1, limit = 50) {
    try {
      const GameRequest = mongoose.model('GameRequest');

      // Build match conditions
      const matchConditions = {
        request_type: 'gameRequest',
        status: 'Approved' // Only approved purchases
      };

      // Add date filter based on approval date (updatedAt when status changed to Approved)
      if (startDate || endDate) {
        matchConditions.updatedAt = {};
        if (startDate) matchConditions.updatedAt.$gte = startDate;
        if (endDate) matchConditions.updatedAt.$lte = endDate;
      }

      // Build aggregation pipeline
      let pipeline = [
        { $match: matchConditions }
      ];

      // Add grouping based on groupBy parameter
      if (groupBy === 'date') {
        pipeline.push(
          {
            $group: {
              _id: {
                $dateToString: {
                  format: '%Y-%m-%d',
                  date: '$updatedAt',
                  timezone: 'UTC'
                }
              },
              count: { $sum: 1 },
              games: { $push: '$gameName' }
            }
          },
          { $sort: { _id: 1 } }
        );
      } else if (groupBy === 'game') {
        pipeline.push(
          {
            $group: {
              _id: '$gameName',
              count: { $sum: 1 },
              latestPurchase: { $max: '$updatedAt' }
            }
          },
          { $sort: { count: -1 } }
        );
      }

      // Add pagination
      const skip = (page - 1) * limit;
      pipeline.push(
        { $skip: skip },
        { $limit: parseInt(limit) }
      );

      const [results, totalCount] = await Promise.all([
        GameRequest.aggregate(pipeline),
        GameRequest.countDocuments(matchConditions)
      ]);

      return {
        total: totalCount,
        items: results,
        groupBy
      };

    } catch (error) {
      console.error('Error in getGamePurchasesAnalytics:', error);
      throw error;
    }
  }

  /**
   * Get redeems analytics
   * @param {Date} startDate - Start date for filtering
   * @param {Date} endDate - End date for filtering
   * @param {string} groupBy - Grouping option (date, user, game)
   * @param {number} page - Page number for pagination
   * @param {number} limit - Items per page
   * @returns {Object} - Redeems analytics data
   */
  async getRedeemsAnalytics(startDate, endDate, groupBy = 'date', page = 1, limit = 50) {
    try {
      const RedeemRequest = mongoose.model('RedeemRequest');

      // Build match conditions
      const matchConditions = {
        status: 'Approved' // Only approved redeems
      };

      // Add date filter based on approval date (updatedAt when status changed to Approved)
      if (startDate || endDate) {
        matchConditions.updatedAt = {};
        if (startDate) matchConditions.updatedAt.$gte = startDate;
        if (endDate) matchConditions.updatedAt.$lte = endDate;
      }

      // Build aggregation pipeline
      let pipeline = [
        { $match: matchConditions }
      ];

      // Add grouping based on groupBy parameter
      if (groupBy === 'date') {
        pipeline.push(
          {
            $group: {
              _id: {
                $dateToString: {
                  format: '%Y-%m-%d',
                  date: '$updatedAt',
                  timezone: 'UTC'
                }
              },
              count: { $sum: 1 },
              totalAmount: {
                $sum: {
                  $toDouble: {
                    $ifNull: ['$payoutAmount', 0]
                  }
                }
              },
              games: { $push: '$gameName' }
            }
          },
          { $sort: { _id: 1 } }
        );
      } else if (groupBy === 'game') {
        pipeline.push(
          {
            $group: {
              _id: '$gameName',
              count: { $sum: 1 },
              totalAmount: {
                $sum: {
                  $toDouble: {
                    $ifNull: ['$payoutAmount', 0]
                  }
                }
              },
              latestRedeem: { $max: '$updatedAt' }
            }
          },
          { $sort: { totalAmount: -1 } }
        );
      }

      // Add pagination
      const skip = (page - 1) * limit;
      pipeline.push(
        { $skip: skip },
        { $limit: parseInt(limit) }
      );

      const [results, totalCount, totalAmountResult] = await Promise.all([
        RedeemRequest.aggregate(pipeline),
        RedeemRequest.countDocuments(matchConditions),
        RedeemRequest.aggregate([
          { $match: matchConditions },
          {
            $group: {
              _id: null,
              totalAmount: {
                $sum: {
                  $toDouble: {
                    $ifNull: ['$payoutAmount', 0]
                  }
                }
              }
            }
          }
        ])
      ]);

      return {
        total: totalCount,
        totalAmount: totalAmountResult.length > 0 ? totalAmountResult[0].totalAmount : 0,
        items: results,
        groupBy
      };

    } catch (error) {
      console.error('Error in getRedeemsAnalytics:', error);
      throw error;
    }
  }

  /**
   * Get total sweeps coin balance across all accounts
   * @returns {number} - Total sweeps coin balance
   */
  async getTotalSweepsBalance() {
    try {
      const Wallet = mongoose.model('Wallet');

      const result = await Wallet.aggregate([
        {
          $group: {
            _id: null,
            totalSwipeCoins: { $sum: '$balance.swipeCoins' },
            totalUnplayedSwipeCoins: { $sum: '$balance.unplayedSwipeCoins' },
            totalRedeemableSwipeCoins: { $sum: '$balance.redeemableSwipeCoins' }
          }
        }
      ]);

      if (result.length === 0) {
        return {
          totalSwipeCoins: 0,
          totalUnplayedSwipeCoins: 0,
          totalRedeemableSwipeCoins: 0,
          grandTotal: 0
        };
      }

      const data = result[0];
      return {
        totalSwipeCoins: data.totalSwipeCoins || 0,
        totalUnplayedSwipeCoins: data.totalUnplayedSwipeCoins || 0,
        totalRedeemableSwipeCoins: data.totalRedeemableSwipeCoins || 0,
        grandTotal: (data.totalSwipeCoins || 0) + (data.totalUnplayedSwipeCoins || 0) + (data.totalRedeemableSwipeCoins || 0)
      };

    } catch (error) {
      console.error('Error in getTotalSweepsBalance:', error);
      throw error;
    }
  }

  /**
   * Get gold coin purchases analytics
   * @param {Date} startDate - Start date for filtering
   * @param {Date} endDate - End date for filtering
   * @returns {Object} - Gold coin purchases data
   */
  async getGoldCoinPurchases(startDate, endDate) {
    try {
      const Wallet = mongoose.model('Wallet');

      // Build match conditions for transactions
      const matchConditions = {
        'transactions.coinType': { $in: ['goldCoin', 'both'] },
        'transactions.type': 'purchase',
        'transactions.status': 'Processed'
      };

      // Add date filter
      if (startDate || endDate) {
        matchConditions['transactions.date'] = {};
        if (startDate) matchConditions['transactions.date'].$gte = startDate;
        if (endDate) matchConditions['transactions.date'].$lte = endDate;
      }

      const pipeline = [
        { $unwind: '$transactions' },
        { $match: matchConditions },
        {
          $group: {
            _id: {
              $dateToString: {
                format: '%Y-%m-%d',
                date: '$transactions.date',
                timezone: 'UTC'
              }
            },
            totalAmount: { $sum: '$transactions.amount' },
            count: { $sum: 1 }
          }
        },
        { $sort: { _id: 1 } }
      ];

      const [results, totalResult] = await Promise.all([
        Wallet.aggregate(pipeline),
        Wallet.aggregate([
          { $unwind: '$transactions' },
          { $match: matchConditions },
          {
            $group: {
              _id: null,
              total: { $sum: '$transactions.amount' },
              count: { $sum: 1 }
            }
          }
        ])
      ]);

      return {
        total: totalResult.length > 0 ? totalResult[0].total : 0,
        totalTransactions: totalResult.length > 0 ? totalResult[0].count : 0,
        dailyBreakdown: results
      };

    } catch (error) {
      console.error('Error in getGoldCoinPurchases:', error);
      throw error;
    }
  }

  /**
   * Get detailed wallet balances breakdown
   * @returns {Object} - Detailed wallet balances
   */
  async getDetailedWalletBalances() {
    try {
      const Wallet = mongoose.model('Wallet');

      const result = await Wallet.aggregate([
        {
          $group: {
            _id: null,
            totalWallets: { $sum: 1 },
            totalGoldCoins: { $sum: '$balance.goldCoins' },
            totalSwipeCoins: { $sum: '$balance.swipeCoins' },
            totalUnplayedSwipeCoins: { $sum: '$balance.unplayedSwipeCoins' },
            totalRedeemableSwipeCoins: { $sum: '$balance.redeemableSwipeCoins' },
            avgGoldCoins: { $avg: '$balance.goldCoins' },
            avgSwipeCoins: { $avg: '$balance.swipeCoins' }
          }
        }
      ]);

      if (result.length === 0) {
        return {
          totalWallets: 0,
          totalGoldCoins: 0,
          totalSwipeCoins: 0,
          totalUnplayedSwipeCoins: 0,
          totalRedeemableSwipeCoins: 0,
          avgGoldCoins: 0,
          avgSwipeCoins: 0
        };
      }

      return result[0];

    } catch (error) {
      console.error('Error in getDetailedWalletBalances:', error);
      throw error;
    }
  }
}

module.exports = new ReportsController();
