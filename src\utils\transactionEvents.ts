import { eventBus, TRANSACTION_EVENTS } from './eventBus';

/**
 * Utility functions to emit transaction events
 * These functions should be called whenever a transaction is completed
 * to notify components that need to refresh their data
 */

/**
 * Emit an event when a purchase transaction is completed
 * @param transactionData Optional transaction data to include with the event
 */
export const notifyPurchaseCompleted = (transactionData?: any) => {
  eventBus.emit(TRANSACTION_EVENTS.PURCHASE_COMPLETED, transactionData);
  // Also emit general transaction updated event
  eventBus.emit(TRANSACTION_EVENTS.TRANSACTION_UPDATED, {
    type: 'purchase',
    data: transactionData
  });
};

/**
 * Emit an event when a redeem transaction is completed
 * @param transactionData Optional transaction data to include with the event
 */
export const notifyRedeemCompleted = (transactionData?: any) => {
  eventBus.emit(TRANSACTION_EVENTS.REDEEM_COMPLETED, transactionData);
  // Also emit general transaction updated event
  eventBus.emit(TRANSACTION_EVENTS.TRANSACTION_UPDATED, {
    type: 'redeem',
    data: transactionData
  });
};

/**
 * Emit a general transaction updated event
 * @param transactionData Optional transaction data to include with the event
 */
export const notifyTransactionUpdated = (transactionData?: any) => {
  eventBus.emit(TRANSACTION_EVENTS.TRANSACTION_UPDATED, transactionData);
};
