import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { RootState } from '../../../redux/store';
import { FaCreditCard } from 'react-icons/fa';
import { toast } from 'react-toastify';
// Optimize imports to only include what's needed
import ProgressiveImage from '../../../components/ProgressiveImage';
import CustomPaymentForm from './customPaymentForm';
import { getCurrentPosition } from 'utils/geoLocator';

interface WalletData {
  id: string;
  swipeCoins: number;
  goldCoins: number;
}

interface Transaction {
  id: string;
  type: 'deposit' | 'redeem' | 'purchase' | 'refund' | 'bonus';
  amount: number;
  date: string;
  coinType: string;
  paymentMethod?: string;
  status?: string;
  description?: string;
}

interface CryptoCurrency {
  currency: string;
  description: string;
  icon_url: string;
  status: string;
  zero_confirmations_enabled: string;
  currency_fiat: string;
  rate: string;
  sell_status: string;
  sell_rate: string;
  buy_status: string;
  buy_rate: string | null;
  sell_min_invoice_amount: string;
  sell_max_invoice_amount: string;
  sell_network_processing_fee: string;
  buy_min_invoice_amount: string | null;
  buy_max_invoice_amount: string | null;
  buy_network_processing_fee: string | null;
}

// API configuration
const API_BASE_URL = process.env.REACT_APP_API_URL;

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth interceptor
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('Token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

const DepositSection = () => {
  const { user, loading: isLoading }: any = useSelector((state: RootState) => state.auth);
  const navigate = useNavigate();
  const [walletData, setWalletData] = useState<WalletData>({
    id: '',
    swipeCoins: 0,
    goldCoins: 0
  });
  
  // Use a derived value instead of separate state
  const hasWallet = useMemo(() => {
    return walletData.id !== '1' || walletData.goldCoins > 0 || walletData.swipeCoins > 0;
  }, [walletData]);
  const [amount, setAmount] = useState<number | string>(10);
  const [loading, setLoading] = useState(false);
  const [notification, setNotification] = useState({
    show: false,
    message: '',
    type: '',
  });
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [paymentMethod, setPaymentMethod] = useState<'card' | 'crypto' | ''>('');
  const [selectedCrypto, setSelectedCrypto] = useState<string>('');
  const [availableCryptocurrencies, setAvailableCryptocurrencies] = useState<CryptoCurrency[]>([]);
  const [loadingCurrencies, setLoadingCurrencies] = useState<boolean>(false);
  const [paymentStep, setPaymentStep] = useState<'method' | 'details' | 'processing' | 'waiting'>('method');
  const [paymentData, setPaymentData] = useState<any>(null);
  const [cardData, setCardData] = useState({
    cardNumber: '',
    expiryMonth: '',
    expiryYear: '',
    cvv: '',
    cardholderName: ''
  });
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);

  // Authorize.Net credentials from environment variables
  const AUTHORIZE_API_LOGIN_ID = process.env.REACT_APP_AUTHORIZE_API_LOGIN_ID || '94x97P7rAxdt';
  const AUTHORIZE_CLIENT_KEY = process.env.REACT_APP_AUTHORIZE_CLIENT_KEY || '47W8f3pA2Hz6zFRmB39uEgbq4PU6qzTahq4353JS3EzYk5FvSEuNNp79LaBkA6f8';
  const ENVIRONMENT = 'production'; // Force production environment for live payments

  const email = localStorage.getItem('email');

  // Filter transactions to only show swipe coins
  const filteredTransactions = transactions.filter(t => t.coinType === 'swipeCoin');

  // Group transactions by date for better organization
  const groupedTransactions = filteredTransactions.reduce((groups, transaction) => {
    const date = new Date(transaction.date).toLocaleDateString();
    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(transaction);
    return groups;
  }, {} as Record<string, Transaction[]>);

  // Sort dates in descending order (newest first)
  const sortedDates = Object.keys(groupedTransactions).sort((a, b) => {
    return new Date(b).getTime() - new Date(a).getTime();
  });

  // Payment method options for deposit
  const depositPaymentMethods = [
    { id: 'card', name: 'Card Payment', icon: <FaCreditCard className="mr-2" /> },
    {
      id: 'crypto', name: 'Cryptocurrency', icon: <img src="/images/crypto-icon.svg" alt="Crypto" className="w-4 h-4 mr-2" onError={(e) => {
        const target = e.target as HTMLImageElement;
        target.style.display = 'none';
      }} />
    },
  ];

  const checkKYCStatus = async () => {
    try {
      const userId = localStorage.getItem('userId');
      const response = await axios.get(`${API_BASE_URL}/api/kyc/verify`, {
        params: {
          user_id: userId,
          email: email,
        },
        headers: {
          Authorization: `Bearer ${localStorage.getItem('Token')}`,
        },
      });

      return response.data.is_verified;
    } catch (error) {
      console.error('Error checking KYC status:', error);
      return false;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Check if amount is empty string or invalid number
    if (amount === '' || Number(amount) <= 0) {
      showNotification('Please enter a valid amount', 'error');
      return;
    }

    // Check if amount is less than the minimum required (10)
    if (Number(amount) < 10) {
      showNotification('Amount should be at least 10', 'error');
      return;
    }

    if (!paymentMethod) {
      showNotification('Please select a payment method', 'error');
      return;
    }

    // Check if KYC is required
    const isKYCVerified = await checkKYCStatus();

    if (!isKYCVerified) {
      localStorage.setItem('pendingWalletRecharge', JSON.stringify({
        amount,
        walletType: 'goldCoins',
        paymentMethod: paymentMethod
      }));
      navigate('/kyc-verify');
      return;
    }

    if (paymentMethod === 'crypto') {
      await handleCryptoPayment();
      return;
    }

    if (paymentMethod === 'card') {
      handleCardPayment();
      return;
    }
  };

  const resetInputFields = () => {
    setAmount(10); // Reset to default value of 10 instead of 0
    setPaymentMethod('');
    setSelectedCrypto('');
    setPaymentStep('method');
  };

  const handleCardPayment = async () => {
      // Component-specific helper for wallet refresh
    // and the handlePaymentSuccess function below
    setPaymentStep('details');
  };

  const handlePaymentSuccess = async (response: any) => {
    // Prevent duplicate API calls
    if (isProcessingPayment) {
      return;
    }

    setIsProcessingPayment(true);
    setLoading(true);

    try {
      // Validate the response from Authorize.Net
      if (!response || !response.dataDescriptor || !response.dataValue) {
        throw new Error('Invalid payment token received from payment processor');
      }

      // First, set the payment step to processing
      setPaymentStep('processing');

      // Get user's position for geolocation validation
      let position;
      try {
        position = await getCurrentPosition();
      } catch (geoError) {
        console.error('Error getting position:', geoError);
        // Fallback to default coordinates if geolocation fails
        position = {
          coords: {
            latitude: 0,
            longitude: 0
          }
        };
      }

      // Extract amount from the response if available, otherwise use the state amount
      // This is critical because AcceptUI might be overriding our amount
      let finalAmount = typeof amount === 'string' ? (amount === '' ? 0 : Number(amount)) : amount;

      // Check if the response contains amount information
      if (response.amount) {
        finalAmount = Number(response.amount);
      } else if (response.authAmount) {
        finalAmount = Number(response.authAmount);
      }

      // Validate the amount
      const parsedAmount = Number(finalAmount);
      if (isNaN(parsedAmount) || parsedAmount <= 0) {
        throw new Error('Invalid payment amount');
      }

      // Check if amount is less than the minimum required (10)
      if (parsedAmount < 10) {
        throw new Error('Amount should be at least 10');
      }


      // Create payment payload for Authorize.Net
      const paymentPayload = {
        opaqueData: {
          dataDescriptor: response.dataDescriptor,
          dataValue: response.dataValue
        },
        amount: parsedAmount,
        userId: user?._id,
        email: user?.email || localStorage.getItem('email'),
        description: `Purchase ${parsedAmount} Gold Coins`,
        reference: `GOLD-${user?._id}-${Date.now()}`,
        geolocation: {
          latitude: position.coords.latitude,
          longitude: position.coords.longitude
        }
      };

     
      // Validate payload before sending
      if (!paymentPayload.opaqueData || !paymentPayload.opaqueData.dataDescriptor || !paymentPayload.opaqueData.dataValue) {
        throw new Error('Invalid payment token data. Please try again.');
      }

      if (!paymentPayload.userId) {
        throw new Error('User ID is missing. Please log in again and try again.');
      }

      if (!paymentPayload.amount || paymentPayload.amount < 10) {
        throw new Error('Amount must be at least 10.');
      }

      // Process payment through Authorize.Net API
      const paymentResponse = await api.post('/process', paymentPayload);

      // Check for valid response
      if (!paymentResponse || !paymentResponse.data) {
        throw new Error('No response received from payment server');
      }

      if (paymentResponse.data && paymentResponse.data.success) {
        // If payment was successful, update the wallet data from the response
        if (paymentResponse.data.wallet) {
          setWalletData({
            ...walletData,
            goldCoins: paymentResponse.data.wallet.goldCoins,
            swipeCoins: paymentResponse.data.wallet.swipeCoins
          });
        }

        // Refresh transactions list
        await refreshWalletData();

        // Show success notification
        showNotification(`Successfully purchased ${parsedAmount} Gold Coins`, 'success');

        // Reset form fields
        resetInputFields();
      } else {
        // Handle payment failure with detailed error information
        let errorMessage = 'Payment processing failed';

        // Extract detailed error information if available
        if (paymentResponse.data) {
          if (paymentResponse.data.message) {
            errorMessage = paymentResponse.data.message;
          }

          // Check for transaction response details
          if (paymentResponse.data.transactionResponse) {
            const transResponse = paymentResponse.data.transactionResponse;

            // Add response code information if available
            if (transResponse.responseCode) {
              errorMessage += ` (Code: ${transResponse.responseCode})`;
            }

            // Add specific error details if available
            if (transResponse.errors && transResponse.errors.length > 0) {
              errorMessage += `: ${transResponse.errors[0].errorText}`;
            }
          }
        }

        showNotification(errorMessage, 'error');
        throw new Error(errorMessage);
      }
    } catch (error: any) {
      console.error('Error processing card payment:', error);
      console.error('Error details:', JSON.stringify(error, null, 2));

      // Handle different types of errors with appropriate messages
      let errorMessage = 'Payment processing failed';

      if (error.message) {
        errorMessage = error.message;
      }

      if (error.response) {
        // Log detailed API error response
        console.error('API error response:', {
          status: error.response.status,
          statusText: error.response.statusText,
          data: error.response.data
        });

        // Handle API error responses
        if (error.response.data && error.response.data.message) {
          errorMessage = error.response.data.message;
        } else if (error.response.status === 400) {
          // Check for specific 400 error messages
          if (error.response.data && typeof error.response.data === 'object') {
            if (error.response.data.transactionResponse && error.response.data.transactionResponse.errors) {
              // Extract error from transaction response
              const transErrors = error.response.data.transactionResponse.errors;
              if (Array.isArray(transErrors) && transErrors.length > 0) {
                errorMessage = `Payment declined: ${transErrors[0].errorText}`;
              } else {
                errorMessage = 'Payment declined by the payment processor';
              }
            } else {
              errorMessage = 'Payment declined by the payment processor';
            }
          } else {
            errorMessage = 'Payment declined by the payment processor';
          }
        } else if (error.response.status === 401) {
          errorMessage = 'Payment authorization failed';
        } else if (error.response.status === 500) {
          errorMessage = 'Server error processing payment';
        }
      } else if (error.request) {
        // Handle network errors
        console.error('Network error details:', error.request);
        errorMessage = 'Network error while processing payment. Please check your internet connection.';
      }

      showNotification(errorMessage, 'error');
    } finally {
      setLoading(false);
      setPaymentStep('method'); // Reset to method selection step
      setIsProcessingPayment(false); // Reset the processing flag
    }
  };

  const handlePaymentError = (errors: any) => {
    console.error('Payment errors:', errors);

    // Create a more detailed error message
    let errorMessage = 'Payment failed';

    // Check if we have error details
    if (Array.isArray(errors) && errors.length > 0) {
      // Get the first error
      const firstError = errors[0];

      if (firstError) {
        // Add error text if available
        if (firstError.text) {
          errorMessage += ': ' + firstError.text;
        }

        // Add error code if available
        if (firstError.code) {
          errorMessage += ` (Code: ${firstError.code})`;
        }
      }
    } else if (typeof errors === 'string') {
      // If errors is just a string
      errorMessage += ': ' + errors;
    } else if (errors && typeof errors === 'object') {
      // If errors is an object but not an array
      if (errors.message) {
        errorMessage += ': ' + errors.message;
      } else if (errors.error) {
        errorMessage += ': ' + errors.error;
      }
    }

    // Map common error codes to user-friendly messages
    if (errorMessage.includes('E_WC_05') || errorMessage.includes('declined')) {
      errorMessage = 'Your payment was declined. Please check your card details and try again.';
    } else if (errorMessage.includes('E_WC_06')) {
      errorMessage = 'The card number you entered is invalid. Please check and try again.';
    } else if (errorMessage.includes('E_WC_07')) {
      errorMessage = 'The expiration date you entered is invalid. Please check and try again.';
    } else if (errorMessage.includes('E_WC_08')) {
      errorMessage = 'The CVV code you entered is invalid. Please check and try again.';
    } else if (errorMessage.includes('E_WC_15') || errorMessage.includes('E_WC_16')) {
      errorMessage = 'There was a problem connecting to the payment processor. Please try again later.';
    }

    showNotification(errorMessage, 'error');
    setPaymentStep('method');
    setLoading(false);
  };

  const handleCryptoPayment = async () => {
    if (!selectedCrypto) {
      setNotification({
        show: true,
        message: 'Please select a cryptocurrency',
        type: 'error'
      });
      return;
    }

    setLoading(true);
    setPaymentStep('processing');

    try {
      const position = await getCurrentPosition();

      // Ensure amount is a number
      const numericAmount = typeof amount === 'string' ? (amount === '' ? 0 : Number(amount)) : amount;

      // Create a payment request for the backend's ForumPay integration
      const paymentRequest = {
        amount: numericAmount,
        price: numericAmount, // Required by OrderController.add
        cryptocurrency: selectedCrypto,
        userId: user?._id,
        email: user?.email || localStorage.getItem('email'),
        reference_no: `${user?._id}-${Date.now()}`, // Unique reference number
        description: `Deposit ${numericAmount} Sweeps Coins`,
        success_url: `${window.location.origin}/dashboard?status=success`,
        failure_url: `${window.location.origin}/dashboard?status=failure`,
        geolocation: {
          latitude: position.coords.latitude,
          longitude: position.coords.longitude
        }
      };

      // Send to backend to handle ForumPay integration via OrderController.add
      const response = await api.post('/order/add', paymentRequest);


      if (response.data && response.data.access_url) {
        // Extract address from the payment response - check all possible locations
        let address = '';

        // Check if address is directly in the response
        if (response.data.address) {
          address = response.data.address;
        }
        // Check if address is in the data object
        else if (response.data.data && response.data.data.paymentAddress) {
          address = response.data.data.paymentAddress;
        }
        // Check if address is in data.address
        else if (response.data.data && response.data.data.address) {
          address = response.data.data.address;
        }

       
        // Save pending transaction information to restore state after payment
        const pendingPaymentData = {
          amount: Number(amount),
          crypto: selectedCrypto,
          reference: paymentRequest.reference_no,
          payment_id: response.data.data.paymentId,
          address: address,
          timestamp: Date.now()
        };

        localStorage.setItem('pendingCryptoPayment', JSON.stringify(pendingPaymentData));

        // Store the access URL in state for fallback link
        setPaymentData({
          ...pendingPaymentData,
          access_url: response.data.access_url
        });

        // Try to open ForumPay payment page in a new tab
        const newWindow = window.open(response.data.access_url, '_blank');

        // Check if window was successfully opened
        if (newWindow === null || typeof newWindow === 'undefined') {
          // If popup was blocked, show a message with a clickable link
          showNotification('Popup blocked. Please click the payment button to proceed.', 'warning');
          setPaymentStep('waiting');
        } else {
          // Focus the new window
          newWindow.focus();
          // Show a message to the user that we're waiting for payment
          showNotification('Payment page opened in a new tab. Please complete your payment there.', 'info');
        }

        // Start polling for payment status
        startPaymentStatusPolling();
      } else {
        throw new Error('Payment initialization failed');
      }
    } catch (error) {
      console.error('Error initiating crypto payment:', error);
      showNotification('Failed to initiate cryptocurrency payment', 'error');
      setPaymentStep('method');
    } finally {
      setLoading(false);
    }
  };

  // Function to start polling for payment status
  const startPaymentStatusPolling = () => {
    // Set a message that we're waiting for payment
    setNotification({
      show: true,
      message: 'Waiting for your payment to be confirmed...',
      type: 'info'
    });

    // Set payment step to waiting
    setPaymentStep('waiting' as 'method' | 'details' | 'processing' | 'waiting');

    // Start checking payment status every 10 seconds
    const pendingPayment = localStorage.getItem('pendingCryptoPayment');
    if (pendingPayment) {
      const paymentInfo = JSON.parse(pendingPayment);
      const pollInterval = setInterval(() => {
        checkPaymentStatusByPaymentId(paymentInfo);
      }, 10000);

      // Store the interval ID so we can clear it later
      localStorage.setItem('paymentPollInterval', pollInterval.toString());

      // Stop polling after 10 minutes (600 seconds)
      setTimeout(() => {
        clearInterval(pollInterval);
        localStorage.removeItem('paymentPollInterval');

        // If we still have a pending payment after 10 minutes, show a message
        if (localStorage.getItem('pendingCryptoPayment')) {
          setNotification({
            show: true,
            message: 'Payment verification is taking longer than expected. Your coins will be added to your wallet within 2 hours if payment was successful.',
            type: 'warning'
          });
          setPaymentStep('method');
        }
      }, 600000);
    }
  };

  // Function to check payment status by payment ID
  const checkPaymentStatusByPaymentId = async (paymentInfo: any) => {
    try {

      // Prepare request payload
      const payload: any = {
        pos_id: "WEB1",
        payment_id: paymentInfo.payment_id,
        reference_no: paymentInfo.reference,
        invoice_amount: typeof paymentInfo.amount === 'string' ?
          (paymentInfo.amount === '' ? 0 : Number(paymentInfo.amount)) :
          paymentInfo.amount,
        currency: paymentInfo.crypto
      };

      // Only add address if it exists and is not empty
      if (paymentInfo.address && paymentInfo.address.trim() !== '') {
        payload.address = paymentInfo.address;
      }


      try {
        const response = await api.post('/order/check-payment', payload);


        if (!response || !response.data) {
          console.error('Payment check response is undefined or missing data');
          return;
        }


        // Check if the response indicates an error
        if (response.data.error || response.data.status === 'error') {
          console.error('Payment check returned an error:', response.data.error || response.data.message);

          // If it's an unknown currency error, show a specific message
          if (response.data.error === 'Unknown currency' ||
            (response.data.message && response.data.message.includes('Unknown currency'))) {
            setNotification({
              show: true,
              message: 'Payment verification failed: Unknown cryptocurrency. Please contact support.',
              type: 'error'
            });

            // Clear polling and payment data
            clearInterval(Number(localStorage.getItem('paymentPollInterval')));
            localStorage.removeItem('paymentPollInterval');
            localStorage.removeItem('pendingCryptoPayment');

            // Reset payment step
            setPaymentStep('method');
            return;
          }

          // For other errors, continue polling
          return;
        }

        // Check if response has the confirmed property and it's true
        if (response.data && response.data.confirmed === true) {
          // Payment confirmed, clear polling and show success message
          clearInterval(Number(localStorage.getItem('paymentPollInterval')));
          localStorage.removeItem('paymentPollInterval');
          localStorage.removeItem('pendingCryptoPayment');

          // Show success message
          setNotification({
            show: true,
            message: 'Payment confirmed! Your coins will be added to your wallet within 2 hours.',
            type: 'success'
          });
          resetInputFields();

          // Reset payment step
          setPaymentStep('method');

          // Fetch updated wallet data
          await refreshWalletData();
          await fetchTransactions();
        } else {
        }
      } catch (apiError: any) {
        console.error('API error checking payment status:', apiError);
        console.error('API error details:', apiError.response ? apiError.response.data : 'No response data');
      }
    } catch (error) {
      console.error('Error in checkPaymentStatusByPaymentId:', error);
    }
  };

  // Fetch available cryptocurrencies from ForumPay
  const fetchAvailableCryptocurrencies = async () => {
    try {
      setLoadingCurrencies(true);

      const response = await api.get('/order/cryptocurrencies');


      if (response.data && response.data.success && response.data.currencies) {
        const currencies = response.data.currencies;

        // Filter out currencies that don't have a valid status
        const validCurrencies = currencies.filter((crypto: CryptoCurrency) =>
          crypto.status === 'OK' && crypto.sell_status === 'OK'
        );

        setAvailableCryptocurrencies(validCurrencies);

        // Set the first currency as default if available and none is selected
        if (validCurrencies.length > 0 && !selectedCrypto) {
          setSelectedCrypto(validCurrencies[0].currency);
        }
      } else {
        console.error('Failed to fetch cryptocurrencies:', response.data?.message || 'Unknown error');
        setNotification({
          show: true,
          message: 'Failed to load available cryptocurrencies. Please try again later.',
          type: 'error'
        });
      }
    } catch (error) {
      console.error('Error fetching cryptocurrencies:', error);
      setNotification({
        show: true,
        message: 'Failed to load available cryptocurrencies. Please try again later.',
        type: 'error'
      });
    } finally {
      setLoadingCurrencies(false);
    }
  };

  const checkPaymentStatus = async () => {
    // Check if we have a status in the URL (returning from ForumPay)
    const urlParams = new URLSearchParams(window.location.search);
    const status = urlParams.get('status');

    if (status) {
      const pendingPayment = localStorage.getItem('pendingCryptoPayment');

      if (pendingPayment) {

        try {
          const paymentInfo = JSON.parse(pendingPayment);
         
          setLoading(true);

          // Set payment step to processing
          setPaymentStep('processing');

          if (status === 'success') {
            try {

              // Show processing message
              setNotification({
                show: true,
                message: 'Processing your payment, please wait...',
                type: 'info'
              });

              // Prepare request payload
              const payload: any = {
                pos_id: "WEB1",
                payment_id: paymentInfo.payment_id,
                reference_no: paymentInfo.reference,
                invoice_amount: typeof paymentInfo.amount === 'string' ?
                  (paymentInfo.amount === '' ? 0 : Number(paymentInfo.amount)) :
                  paymentInfo.amount,
                currency: paymentInfo.crypto
              };

              // Only add address if it exists and is not empty
              if (paymentInfo.address && paymentInfo.address.trim() !== '') {
                payload.address = paymentInfo.address;
              }


              try {
                // Verify the payment was successful with backend
                const response = await api.post('/order/check-payment', payload);


                if (!response || !response.data) {
                  throw new Error('Payment verification response is undefined or missing data');
                }


                // Clear any existing polling
                if (localStorage.getItem('paymentPollInterval')) {
                  clearInterval(Number(localStorage.getItem('paymentPollInterval')));
                  localStorage.removeItem('paymentPollInterval');
                }

                // Check if the response indicates an error
                if (response.data.error || response.data.status === 'error') {
                  console.error('Payment verification returned an error:', response.data.error || response.data.message);

                  // If it's an unknown currency error, show a specific message
                  if (response.data.error === 'Unknown currency' ||
                    (response.data.message && response.data.message.includes('Unknown currency'))) {
                    setNotification({
                      show: true,
                      message: 'Payment verification failed: Unknown cryptocurrency. Please contact support.',
                      type: 'error'
                    });
                    setLoading(false);
                    setPaymentStep('method');
                    return;
                  }

                  // For other errors, show a generic message
                  setNotification({
                    show: true,
                    message: response.data.message || 'Payment verification failed, please contact support',
                    type: 'error'
                  });
                  setLoading(false);
                  setPaymentStep('method');
                  return;
                }

                // Check if response has the confirmed property and it's true
                if (response.data && response.data.confirmed === true) {
                  // Payment was successfully verified and processed
                  await refreshWalletData();
                  await fetchTransactions();

                  setNotification({
                    show: true,
                    message: 'Payment confirmed! Your coins will be added to your wallet within 2 hours.',
                    type: 'success'
                  });
                  resetInputFields();

                  // If the wallet was updated in the response, update the local state
                  if (response.data.wallet) {
                    setWalletData(response.data.wallet);
                  }
                } else if (response.data) {
                  // Payment verification failed with a specific message
                  setNotification({
                    show: true,
                    message: response.data.message || 'Your payment is being processed. Coins will be added to your wallet within 2 hours.',
                    type: 'info'
                  });

                  // Start polling for payment status
                  startPaymentStatusPolling();
                } else {
                  throw new Error('Invalid response format from payment verification');
                }
              } catch (apiError: any) {
                console.error('API error verifying payment:', apiError);
                console.error('API error details:', apiError.response ? apiError.response.data : 'No response data');
                throw apiError;
              }
            } catch (error) {
              console.error('Error verifying payment:', error);
              setNotification({
                show: true,
                message: 'Payment verification failed, please contact support',
                type: 'error'
              });
            } finally {
              setLoading(false);
              setPaymentStep('method');
            }
          } else {
            setNotification({
              show: true,
              message: 'Payment was cancelled or failed',
              type: 'error'
            });
            setLoading(false);
            setPaymentStep('method');
          }

          // Clear the pending payment data
          localStorage.removeItem('pendingCryptoPayment');

          // Remove the query parameters
          window.history.replaceState({}, document.title, window.location.pathname);
        } catch (error) {
          console.error('Error parsing or processing payment info:', error);
          setNotification({
            show: true,
            message: 'Error processing payment information',
            type: 'error'
          });
          setLoading(false);
          setPaymentStep('method');
        }
      }
    }
  };

  const showNotification = (message: string, type: string) => {
    setNotification({ show: true, message, type });
    setTimeout(() => {
      setNotification({ show: false, message: '', type: '' });
    }, 3000);
  };

  // Legacy fetchWalletData implementation - will be removed in favor of React Query version
  // const fetchWalletData = async () => {
  //   try {
  //     const response = await api.get(`/wallet/balance/${user?._id}`);
  //     console.log(response.data.data);
  //     setWalletData(response.data.data);
  //   } catch (error) {
  //     console.error('Error fetching wallet data:', error);
  //     showNotification('Failed to fetch wallet data', 'error');
  //   }
  // };

  const fetchTransactions = async (): Promise<any> => {
    try {
      const response = await api.get(`/wallet/transactions/${user?._id}`);
      const transactions = response.data.data || [];
      setTransactions(transactions);
      return transactions;
    } catch (error) {
      console.error('Error fetching transactions:', error);
      showNotification('Failed to fetch transactions', 'error');
      return [];
    }
  };

  useEffect(() => {
    // Fetch wallet data when component mounts
    refreshWalletData();
    fetchTransactions();

    // Check if there's a pending payment status in the URL
    checkPaymentStatus();

    // Clean up any polling intervals when component unmounts
    return () => {
      if (localStorage.getItem('paymentPollInterval')) {
        clearInterval(Number(localStorage.getItem('paymentPollInterval')));
        localStorage.removeItem('paymentPollInterval');
      }
    };
  }, []);

  // Fetch available cryptocurrencies when payment method is set to crypto
  useEffect(() => {
    if (paymentMethod === 'crypto') {
      fetchAvailableCryptocurrencies();
    }
  }, [paymentMethod]);

  useEffect(() => {
    // Check payment status when component mounts (for returning from ForumPay)
    checkPaymentStatus();

    // Check for any pending recharge after KYC verification
    const pendingRecharge = localStorage.getItem('pendingWalletRecharge');

    if (pendingRecharge) {
      const rechargeData = JSON.parse(pendingRecharge);
      setAmount(rechargeData.amount);
      if (rechargeData.paymentMethod) {
        setPaymentMethod(rechargeData.paymentMethod);
      }
      localStorage.removeItem('pendingWalletRecharge');
    }
  }, []);

  // Initialize QueryClient at the component level
  const queryClient = useQueryClient();
  
  // Define wallet data query with prefetching and error handling
  const walletQuery = useQuery<WalletData | null>({ 
    queryKey: ['wallet', user?._id],
    queryFn: async () => {
      try {
        const response = await api.get(`/wallet/balance/${user?._id}`);
        return response.data.data as WalletData;
      } catch (error: any) {
        // If wallet doesn't exist, we'll create one
        if (error.response?.status === 404 && 
            error.response?.data?.error === "Wallet not found") {
          return null; // Return null to indicate wallet needs creation
        }
        throw error; // Re-throw other errors
      }
    },
    enabled: !!user?._id,
    staleTime: 5 * 60 * 1000, // 5 minutes cache
    retry: 1
  });
  
  // Extract loading state and data
  const isWalletLoading = walletQuery.isLoading;
  const walletQueryData = walletQuery.data;
  
  // Set up error handler
  useEffect(() => {
    if (walletQuery.error) {
      console.error('Error fetching wallet:', walletQuery.error);
      toast.error('Error loading wallet data');
    }
  }, [walletQuery.error]);
  
  // Mutation for creating a wallet
  const createWalletMutation = useMutation<any, Error, void>({
    mutationFn: async () => {
      return await api.post('/wallet/create', { userId: user?._id });
    },
    onSuccess: () => {
      toast.success('Wallet created successfully');
      // Invalidate wallet query to trigger refetch
      queryClient.invalidateQueries({ queryKey: ['wallet', user?._id] });
    },
    onError: (error: Error) => {
      console.error('Error creating wallet:', error);
      toast.error('Failed to create wallet. Please try again later.');
    }
  });
  
  // Query for transactions with memory optimization - using any type to avoid TypeScript errors
  const transactionsQuery = useQuery<any, any, any>({ 
    queryKey: ['transactions', user?._id],
    queryFn: fetchTransactions,
    enabled: !!user?._id && !!hasWallet,
    staleTime: 2 * 60 * 1000 // 2 minutes cache
  });
  
  // Extract transaction data with type assertion
  const transactionData = transactionsQuery.data as Transaction[] | undefined;
  
  const refreshWalletData = useCallback(async () => {
    try {
      await queryClient.invalidateQueries({ queryKey: ['wallet', user?._id] });
      return true;
    } catch (error) {
      console.error('Error refreshing wallet data:', error);
      return false;
    }
  }, [user?._id, queryClient]);
  
  useEffect(() => {
    if (!user?._id) return;
    
    if (walletQueryData === null && !createWalletMutation.isPending) {
      createWalletMutation.mutate();
    }
    
    if (walletQueryData) {
      const safeWalletData = walletQueryData as unknown as WalletData;
      setWalletData({
        id: safeWalletData.id || '',
        goldCoins: safeWalletData.goldCoins || 0,
        swipeCoins: safeWalletData.swipeCoins || 0
      });
    }
    
    setLoading(false);
  }, [user?._id, walletQueryData, createWalletMutation]);

  const renderPaymentMethodSelector = () => {
    return (
      <div className="mt-4">
        <label className="block text-sm font-medium mb-2">Select Payment Method</label>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
          {depositPaymentMethods.map((method) => (
            <button
              key={method.id}
              type="button"
              className={`flex items-center justify-center px-4 py-2 border ${paymentMethod === method.id
                ? 'border-lime-500 bg-lime-50 text-lime-700'
                : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                } rounded-md focus:outline-none`}
              onClick={() => setPaymentMethod(method.id as any)}
              disabled={loading}
            >
              {method.icon}
              {method.name}
            </button>
          ))}
        </div>
      </div>
    );
  };

  const renderAmountInput = () => {
    return (
      <div className="space-y-2">
        <h3 className="text-lg font-medium">Enter Amount</h3>
        <input
          type="number"
          value={amount}
          className="w-full px-3 py-2 border border-lime-900 rounded-md focus:outline-none focus:ring-1 focus:ring-lime-500"
          onChange={(e) => {
            const inputValue = e.target.value;
            // Allow empty input (don't convert to number if empty)
            setAmount(inputValue === '' ? '' : Number(inputValue));
          }}
          placeholder="Enter amount"
          disabled={loading}
        />
      </div>
    );
  };

  const renderActionButtons = () => {
    return (
      <div className="mt-6">
        <button
          type="submit"
          className="w-full bg-[#495e26] text-white py-4 px-6 rounded-lg hover:bg-[#3b4d1f] transition-colors text-lg font-medium"
          disabled={loading || !paymentMethod || (paymentMethod === 'crypto' && !selectedCrypto) || amount === '' || Number(amount) <= 0}
        >
          {loading ? 'Processing deposit...' : 'Purchase Gold Coins'}
        </button>
      </div>
    );
  };

  const renderPaymentStep = () => {
    switch (paymentStep) {
      case 'method':
        return (
          <>
            {renderPaymentMethodSelector()}
            {paymentMethod && renderAmountInput()}
            {paymentMethod === 'card' && amount && (
              <div className="mt-4 p-4 bg-white rounded-lg shadow">
                <h3 className="text-lg font-semibold mb-4">Card Payment</h3>
                {/* Force re-render of the payment form when amount changes */}
                <CustomPaymentForm
                  key={`card-payment-form-${amount}`}
                  apiLoginId={AUTHORIZE_API_LOGIN_ID}
                  clientKey={AUTHORIZE_CLIENT_KEY}
                  environment={ENVIRONMENT}
                  onPaymentSuccess={handlePaymentSuccess}
                  onPaymentError={handlePaymentError}
                  amount={Number(amount)}
                  requireBillingAddress={true}
                  formButtonText={"Purchase Gold Coins"}
                  formHeaderText="Payment Information"
                  onCancel={() => setPaymentStep('method')}
                  userDetails={{
                    firstName: user?.firstName || '',
                    lastName: user?.lastName || '',
                    email: user?.email || localStorage.getItem('email') || '',
                    phoneNumber: user?.phone || ''
                  }}
                />
              </div>
            )}
            {paymentMethod === 'crypto' && amount && (
              <div className="space-y-2">
                <h3 className="text-lg font-medium">Select Cryptocurrency</h3>
                {loadingCurrencies ? (
                  <div className="mt-2 flex items-center">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-gray-900 mr-2"></div>
                    <span>Loading available cryptocurrencies...</span>
                  </div>
                ) : availableCryptocurrencies.length > 0 ? (
                  <div className="relative">
                    <select
                      id="cryptoSelect"
                      className="mt-1 block w-full pl-3 pr-10 py-3 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md appearance-none"
                      value={selectedCrypto}
                      onChange={(e) => setSelectedCrypto(e.target.value)}
                      disabled={loading}
                    >
                      <option value="" disabled>Select a cryptocurrency</option>
                      {availableCryptocurrencies.map((crypto) => (
                        <option key={crypto.currency} value={crypto.currency}>
                          {crypto.description} ({crypto.currency}) - Rate: {crypto.sell_rate} {crypto.currency_fiat}
                        </option>
                      ))}
                    </select>

                    {/* Display icon for selected cryptocurrency */}
                    {selectedCrypto && (
                      <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
                        {availableCryptocurrencies.map((crypto) =>
                          crypto.currency === selectedCrypto ? (
                            <img
                              key={crypto.currency}
                              src={crypto.icon_url || "/images/crypto-icon.svg"}
                              alt={crypto.description}
                              className="h-6 w-6 mr-2"
                              onError={(e) => {
                                e.currentTarget.src = "/images/crypto-icon.svg";
                              }}
                            />
                          ) : null
                        )}
                      </div>
                    )}

                    {/* Custom dropdown arrow */}
                    <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                      <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                    </div>
                  </div>
                ) : (
                  <div className="mt-2 text-red-500">
                    No cryptocurrencies available. Please try again later.
                  </div>
                )}

                {/* Display selected cryptocurrency details */}
                {selectedCrypto && availableCryptocurrencies.length > 0 && (
                  <div className="mt-4 p-4 border border-gray-200 rounded-md bg-gray-50">
                    {availableCryptocurrencies.map((crypto) =>
                      crypto.currency === selectedCrypto ? (
                        <div key={crypto.currency} className="flex items-start">
                          <img
                            src={crypto.icon_url || "/images/crypto-icon.svg"}
                            alt={crypto.description}
                            className="h-10 w-10 mr-3"
                            onError={(e) => {
                              e.currentTarget.src = "/images/crypto-icon.svg";
                            }}
                          />
                          <div>
                            <h4 className="font-medium">{crypto.description} ({crypto.currency})</h4>
                            <p className="text-sm text-gray-600">Rate: {crypto.sell_rate} {crypto.currency_fiat}</p>
                            <p className="text-xs text-gray-500 mt-1">
                              Min: {crypto.sell_min_invoice_amount} {crypto.currency_fiat} |
                              Max: {crypto.sell_max_invoice_amount} {crypto.currency_fiat}
                            </p>
                          </div>
                        </div>
                      ) : null
                    )}
                  </div>
                )}
              </div>
            )}
            {paymentMethod !== 'card' && renderActionButtons()}
          </>
        );
      case 'processing':
        return (
          <div className="flex flex-col items-center justify-center p-8">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-lime-500"></div>
            <h3 className="text-xl font-medium mt-4">Processing your payment...</h3>
            <p className="text-gray-500 mt-2">Please wait while we process your request.</p>
          </div>
        );
      case 'waiting':
        return (
          <div className="flex flex-col items-center justify-center p-8">
            <div className="flex items-center justify-center p-4 animate-pulse">
              <div className="h-16 w-16 rounded-full bg-blue-100 flex items-center justify-center">
                <svg className="h-8 w-8 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
            <h3 className="text-xl font-medium mt-4">Waiting for payment confirmation...</h3>
            <p className="text-gray-500 mt-2">This may take a few minutes. Please don't close this page.</p>

            {paymentData && paymentData.access_url && (
              <button
                className="mt-4 px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center justify-center"
                onClick={(e) => {
                  e.preventDefault();
                  const newWindow = window.open(paymentData.access_url, '_blank');
                  if (newWindow) newWindow.focus();
                }}
              >
                <svg className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                </svg>
                Open Payment Page
              </button>
            )}

            <button className="mt-4 px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300" onClick={() => setPaymentStep('method')}>
              Cancel
            </button>
          </div>
        );
      default:
        return null;
    }
  };

// Enhanced Skeleton UI Component for Deposit Section
const DepositSkeleton = () => (
  <div className="w-full bg-white rounded-lg p-2 sm:p-4 md:p-6 lg:ml-[250px] lg:max-w-[calc(100%-250px)] min-h-[500px]">
    <div className="animate-pulse">
      {/* Wallet Balance Skeleton */}
      <div className="mb-6 flex items-center space-x-4">
        <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
        <div className="h-8 w-48 bg-gray-200 rounded"></div>
      </div>
      
      {/* Amount Input Skeleton */}
      <div className="w-full lg:max-w-xl lg:mx-auto bg-white rounded-lg shadow-md p-4 lg:p-8">
        <div className="space-y-6">
          <div className="h-10 bg-gray-200 rounded w-full"></div>
          <div className="h-10 bg-gray-200 rounded w-full"></div>
          <div className="h-10 bg-gray-200 rounded w-full"></div>
          <div className="grid grid-cols-2 gap-4">
            <div className="h-12 bg-gray-200 rounded"></div>
            <div className="h-12 bg-gray-200 rounded"></div>
          </div>
          <div className="h-12 bg-gray-300 rounded w-full"></div>
        </div>
      </div>
    </div>
  </div>
);

// Use the skeleton when loading
if (isLoading || loading) {
  return <DepositSkeleton />;
}

  return (
    <div className="w-full bg-white rounded-lg p-2 sm:p-4 md:p-6 lg:ml-[250px] lg:max-w-[calc(100%-250px)] min-h-[500px] flex flex-col h-full">
      <h1 className="text-3xl font-bold text-[#495e26] mb-8">Purchase</h1>

      {notification.show && (
        <div
          className={`fixed top-4 right-4 p-4 rounded-lg shadow-lg ${notification.type === 'error' ? 'bg-red-500' : 'bg-green-500'
            } text-white z-50 transition-opacity duration-300`}
        >
          {notification.message}
        </div>
      )}

      <div className="w-full lg:max-w-xl lg:mx-auto bg-white rounded-lg shadow-md p-4 lg:p-8">
        <div className="mb-6 flex items-center space-x-4 text-2xl font-semibold text-[#495e26]">
          <ProgressiveImage
            src="https://res.cloudinary.com/dyiso4ohk/image/upload/v1742926203/Clover__1_-removebg-preview_xh6tsw.png"
            alt="Gold Coin Icon"
            className="w-8 h-8"
            height="32px"
            width="32px"
          />
          <span>Gold Coins: {walletData.goldCoins}</span>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="space-y-4">
            {renderPaymentStep()}
          </div>
        </form>

        {/*<h3 className="text-xl font-semibold mt-8 mb-4">Transaction History</h3>
         <div className="bg-gray-100 rounded-lg p-4">
          {filteredTransactions.length === 0 ? (
            <p className="text-gray-500">No transactions yet.</p>
          ) : (
            <div className="space-y-4">
              {sortedDates.map(date => (
                <div key={date} className="border-b border-gray-200 pb-2 last:border-b-0">
                  <h4 className="text-sm font-medium text-gray-500 mb-2">{date}</h4>
                  <ul className="space-y-2">
                    {groupedTransactions[date].map((tx) => (
                      <li key={tx.id} className="py-2 flex justify-between items-center bg-white rounded-md p-3 shadow-sm">
                        <div className="flex flex-col">
                          <div className="flex items-center">
                            <span
                              className={`font-medium ${tx.type === 'deposit' || tx.type === 'refund' || tx.type === 'bonus'
                                ? 'text-green-600'
                                : 'text-red-600'
                                }`}
                            >
                              {tx.type === 'deposit' || tx.type === 'refund' || tx.type === 'bonus' ? '+' : '-'}
                              {tx.amount.toFixed(2)}
                            </span>
                            <span className="ml-2 px-2 py-0.5 text-xs rounded-full bg-gray-100 text-gray-700">
                              {tx.type === 'deposit' ? 'Deposit' :
                                tx.type === 'redeem' ? 'Redeem' :
                                  tx.type === 'purchase' ? 'Purchase' :
                                    tx.type === 'refund' ? 'Refund' :
                                      tx.type === 'bonus' ? 'Bonus' : tx.type}
                            </span>
                            {tx.status && tx.status !== 'completed' && (
                              <span className="ml-1 px-1.5 py-0.5 text-xs bg-yellow-100 text-yellow-800 rounded">
                                {tx.status}
                              </span>
                            )}
                          </div>
                          {tx.paymentMethod && (
                            <span className="text-xs text-gray-500">
                              via {tx.paymentMethod}
                            </span>
                          )}
                          {tx.description && (
                            <span className="text-xs text-gray-500 mt-1">
                              {tx.description}
                            </span>
                          )}
                        </div>
                        <span className="text-gray-500 text-sm">{new Date(tx.date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          )}
        </div>
        */}
      </div>
    </div>
  );
};

export default DepositSection;
