import { AlertTriangle } from 'lucide-react';

export default function WarningBanner() {
  return (
    <div className="max-w-md mx-auto">
      <div className="flex items-center gap-4 bg-gradient-to-r from-amber-50 to-yellow-50 border-l-4 border-amber-400 px-5 py-4 rounded-r-lg shadow-md">
        <div className="bg-amber-100 p-2 rounded-full">
          <AlertTriangle className="w-6 h-6 text-amber-600" />
        </div>
        <div className="flex-1">
          <h3 className="font-semibold text-amber-900 text-sm mb-1">
            Payment Method Restricted
          </h3>
          <p className="text-red-700 text-sm leading-relaxed font-medium">
            Apple Cash and Chime cards aren't accepted. Please try a different payment method.
          </p>
        </div>
      </div>
    </div>
  );
}