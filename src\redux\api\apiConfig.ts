import { 
  retry,
  skipToken,
  createApi, 
  fetchBaseQuery,
  type BaseQueryFn,
  type FetchArgs,
  type FetchBaseQueryError
} from '@reduxjs/toolkit/query/react';
import { Mutex } from 'async-mutex';

export const CACHE_CONFIG = {
  DEFAULT_CACHE_LIFETIME: 5 * 60,
  
  TAG_CACHE_LIFETIMES: {
    Orders: 2 * 60,
    CashApp: 5 * 60,
    Analytics: 10 * 60,
    User: 30 * 60
  },
  
  MAX_RETRIES: 3,
  
  normalizeQueryArgs: (args: any) => JSON.stringify(args),
};

const mutex = new Mutex();

const baseQueryWithReauth: BaseQueryFn<
  string | FetchArgs,
  unknown,
  FetchBaseQueryError
> = async (args, api, extraOptions) => {
  const baseQuery = fetchBaseQuery({
    baseUrl: process.env.REACT_APP_API_URL,
    prepareHeaders: (headers) => {
      const token = localStorage.getItem('Token');
      if (token) {
        headers.set('Authorization', `Bearer ${token}`);
      }
      headers.set('Content-Type', 'application/json');
      
      headers.set('Cache-Control', 'no-cache');
      headers.set('Pragma', 'no-cache');
      
      return headers;
    },
    timeout: 30000,
  });

  let result = await baseQuery(args, api, extraOptions);
  
  if (result.error && result.error.status === 401) {
    if (!mutex.isLocked()) {
      const release = await mutex.acquire();
      
      try {
        const refreshToken = localStorage.getItem('RefreshToken');
        
        if (refreshToken) {
          const refreshResult = await baseQuery(
            { 
              url: '/auth/refresh-token', 
              method: 'POST', 
              body: { refreshToken } 
            },
            api,
            extraOptions
          );
          
          if (refreshResult.data) {
            const { token, refreshToken: newRefreshToken } = refreshResult.data as any;
            
            localStorage.setItem('Token', token);
            localStorage.setItem('RefreshToken', newRefreshToken);
            
            result = await baseQuery(args, api, extraOptions);
          }
        }
      } finally {
        release();
      }
    } else {
      await mutex.waitForUnlock();
      result = await baseQuery(args, api, extraOptions);
    }
  }
  
  return result;
};

const enhancedBaseQuery = retry(baseQueryWithReauth, {
  maxRetries: CACHE_CONFIG.MAX_RETRIES,
  backoff: ((attempt: any) => Math.min(1000 * (2 ** attempt), 30000)) as any,
});

export { skipToken };

export const TAG_TYPES = {
  Orders: 'Orders',
  CashApp: 'CashApp',
  Analytics: 'Analytics', 
  User: 'User'
} as const;

export type TagType = typeof TAG_TYPES[keyof typeof TAG_TYPES];
export type IdTagType<T extends TagType> = { type: T; id: string | number };

export type AnyObject = Record<string, any>;

export const providesList = <R extends { id: string | number }[], T extends TagType>(
  tagType: T,
  data?: R
) => {
  return data
    ? [
        { type: tagType, id: 'LIST' },
        ...data.map(({ id }) => ({ type: tagType, id })),
      ]
    : [{ type: tagType, id: 'LIST' }];
};

export const cacheEntryBuilder = (builder: any) => ({
  cacheEntryConfig: (time = CACHE_CONFIG.DEFAULT_CACHE_LIFETIME) => ({
    keepUnusedDataFor: time,
  }),
  
  trackRequestStatus: (endpoint: string) => ({
    onQueryStarted: async (arg: any, { dispatch, queryFulfilled, getState }: any) => {
      try {
        await queryFulfilled;
      } catch (error) {
      }
    },
  }),
});

export default enhancedBaseQuery;
