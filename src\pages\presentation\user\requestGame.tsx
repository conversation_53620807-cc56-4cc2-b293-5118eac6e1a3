import React, { useState, FormEvent, useEffect } from 'react';
import gamesImg from '../../../assets/gamesAnimated.svg';
import axios from 'axios';
import { toast } from 'react-toastify';

interface ImagePreview {
  url: string;
  alt: string;
}
interface Product {
  _id: string;
  name: string;
  imageName: string;
  description: string;
}

const RequestGame: React.FC = () => {
  const userId = localStorage.getItem('userId');

  const [selectedGame, setSelectedGame] = useState<Product | null>(null);
  const [imagePreview, setImagePreview] = useState<ImagePreview | null>(null);
  const [productData, setProductData] = useState<Product[]>([]);
  const [isConfirmModalVisible, setIsConfirmModalVisible] = useState(false);
  const [requestedProductIds, setRequestedProductIds] = useState<string[]>([]);

  const handleSubmit = async () => {
    const requestData = {
      gameName: selectedGame?.name,
      productId: selectedGame?._id,
      userId: userId,
      date: new Date().toISOString().split('T')[0],
      request_type: 'gameRequest',
      description: selectedGame?.description,
      passwpasswordReset: 'Approve',
    };
    try {
      // Await the axios POST call to resolve the promise
      const response = await axios.post(
        `${process.env.REACT_APP_API_URL}/game-request/add`,
        requestData,
        {
          headers: {
            Accept: 'application/json',
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            Authorization: `Bearer ${localStorage.getItem('Token')}`,
          },
        }
      );

      if (response.data.data && response.data.data._id) {
        /*   setRequestedProductIds((prev) => [
             ...prev,
             selectedGame?._id || "",
           ]);  */
        setSelectedGame(null);
        setImagePreview(null);

        toast.success('The game ID and password will be sent to your registered email shortly.!');
        fetchProduct();
      } else {
      }
    } catch (error: any) {
      console.error('Error fetching product:', error.message);
    }
  };
  const fetchProduct = async () => {
    try {
      const response = await axios.get(`${process.env.REACT_APP_API_URL}/products/user/${userId}`, {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          Authorization: `Bearer ${localStorage.getItem('Token')}`,
        },
      });
      setProductData(response.data.data);
    } catch (error: any) {
      console.error('Error fetching product :', error);
    }
  };
  const handleGameSelect = (event: any) => {
    const selectedGameId = event.target.value;

    const selectedGameObj = productData.find((game) => game._id === selectedGameId || null);
    setSelectedGame(selectedGameObj ? selectedGameObj : null);

    if (selectedGameObj) {
      setImagePreview({
        url: selectedGameObj.imageName,
        alt: selectedGameObj.name,
      });
    } else {
      setImagePreview(null);
    }
  };

  const handleFormSubmit = (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    if (selectedGame) {
      setIsConfirmModalVisible(true);
    }
  };

  const handleConfirmSubmit = () => {
    setIsConfirmModalVisible(false);
    handleSubmit();
  };

  const handleCancelSubmit = () => {
    setIsConfirmModalVisible(false);
  };

  useEffect(() => {
    fetchProduct();
    // fetchRequestedProducts()
  }, [userId]);
  // const cleanedRequestedIds = requestedProductIds.filter((id) => id.trim() !== "");
  return (
    <div className="w-full bg-white rounded-lg  p-2 px-4 sm:p-4 md:p-6  p-4 lg:ml-[250px] lg:max-w-[calc(100%-250px)] min-h-[500px] flex flex-col h-full">
      <h1 className="text-xl sm:text-2xl md:text-3xl font-bold text-start mb-8 md:mb-16">
        Request System
      </h1>

      <div className="flex flex-col md:flex-row gap-4 md:gap-8 items-center justify-between">
        <div className="w-full md:w-1/2">
          <img src={gamesImg} alt="Gaming Controller" className="w-full h-auto object-contain" />
        </div>

        <div className="w-full md:w-1/2 space-y-4 md:space-y-6 border border-gray-300 rounded-lg p-4 md:p-8">
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 md:p-8 text-center bg-gray-50">
            {imagePreview ? (
              <img
                src={`https://s3.wasabisys.com/productimage/${imagePreview.url}`}
                alt={imagePreview.alt}
                className="w-full h-[200px] sm:h-[300px] md:h-[400px] object-contain px-2 md:px-4 py-2"
              />
            ) : (
              <p className="text-gray-500 text-sm md:text-base">
                The image of the game you selected will appear here.
              </p>
            )}
          </div>

          <form onSubmit={handleFormSubmit} className="space-y-4 md:space-y-6">
            <div className="space-y-1 md:space-y-2">
              <label
                htmlFor="gameSelect"
                className="block text-sm md:text-base font-medium text-gray-700"
              >
                GAME NAME
              </label>
              <select
                id="gameSelect"
                value={selectedGame?._id || ''}
                onChange={handleGameSelect}
                className="w-full px-2 md:px-4 py-2 border border-gray-300 rounded-md text-sm md:text-base"
              >
                <option value="">Select a system</option>
                {productData && productData.length > 0 ? (
                  productData
                    //   .filter((game) => !requestedProductIds.includes(game.name))
                    .map((game) => (
                      <option key={game._id} value={game._id}>
                        {game.name}
                      </option>
                    ))
                ) : (
                  <option>No products available</option>
                )}
              </select>
            </div>

            <button
              type="submit"
              className="w-full bg-[#495e26] text-white py-2 md:py-3 px-4 md:px-6 rounded-md hover:bg-gray-900 transition-colors duration-200"
            >
              Submit
            </button>
          </form>
        </div>
      </div>

      {/* {isNotificationVisible && (
        <NotificationModal
          severity="success"
          title="Notification"
          description={notificationMessage}
          onClose={() => setIsNotificationVisible(false)}
        />
      )} */}

      {isConfirmModalVisible && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-8 rounded-lg shadow-lg w-[95%] max-w-[500px] text-center">
            <h2 className="text-2xl font-semibold mb-6">Confirm Game Request</h2>
            <p className="text-xl text-gray-600 mb-8">
              Are you sure you want to request this game?
            </p>
            <div className="flex justify-between">
              <button
                className="bg-gray-300 px-4 py-3 rounded-md text-xl hover:bg-gray-400 w-[48%]"
                onClick={handleCancelSubmit}
              >
                Cancel
              </button>
              <button
                className="bg-[#495e26] text-white px-4 py-3 rounded-md text-xl hover:bg-gray-900 w-[48%]"
                onClick={handleConfirmSubmit}
              >
                OK
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default RequestGame;
