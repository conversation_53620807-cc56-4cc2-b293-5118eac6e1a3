import React, { useState } from 'react';
import { withErrorHandling, useAsyncErrorHandler } from '../../utils/errorHandling';
import EnhancedErrorBoundary from '../ErrorBoundary/EnhancedErrorBoundary';

/**
 * Component that demonstrates proper async error handling in React
 */
const AsyncErrorHandlingDemo: React.FC = () => {
  const [result, setResult] = useState<string | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  
  // Example 1: Using withErrorHandling utility
  const simulateAsyncError = async () => {
    // Simulate an API call that fails
    await new Promise(resolve => setTimeout(resolve, 1000));
    throw new Error('Simulated async error in API call');
  };
  
  const handleAsyncErrorWithUtility = withErrorHandling(
    simulateAsyncError,
    (error) => {
      // Custom error handling
      setErrorMessage(`Caught with utility: ${error.message}`);
    }
  );
  
  // Example 2: Using useAsyncErrorHandler hook
  const simulateAsyncSuccess = async () => {
    // Simulate a successful API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    return 'Operation completed successfully!';
  };
  
  const [
    handleAsyncWithHook, 
    isError, 
    hookError
  ] = useAsyncErrorHandler(
    simulateAsyncSuccess,
    (error) => {
      console.log('Error handled by hook:', error);
    }
  );
  
  // Example 3: Traditional try/catch in event handler
  const handleTraditionalTryCatch = async () => {
    try {
      setErrorMessage(null);
      setResult(null);
      
      // Simulate an API call that fails
      await new Promise(resolve => setTimeout(resolve, 1000));
      throw new Error('Simulated error in traditional try/catch');
    } catch (error) {
      if (error instanceof Error) {
        setErrorMessage(`Caught with try/catch: ${error.message}`);
      } else {
        setErrorMessage('An unknown error occurred');
      }
    }
  };
  
  // Example 4: Successful async operation
  const handleSuccessfulOperation = async () => {
    try {
      setErrorMessage(null);
      setResult(null);
      
      // Simulate a successful API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setResult('Operation completed successfully!');
    } catch (error) {
      if (error instanceof Error) {
        setErrorMessage(`Error: ${error.message}`);
      }
    }
  };
  
  return (
    <div className="p-6 border rounded-lg bg-white">
      <h2 className="text-xl font-bold mb-4">Async Error Handling Demo</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div className="p-4 border rounded-lg">
          <h3 className="font-semibold mb-3">Example 1: withErrorHandling Utility</h3>
          <p className="text-sm text-gray-600 mb-4">
            This example uses a utility function to wrap async operations and handle errors.
          </p>
          <button
            onClick={handleAsyncErrorWithUtility}
            className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
          >
            Trigger Async Error (Utility)
          </button>
        </div>
        
        <div className="p-4 border rounded-lg">
          <h3 className="font-semibold mb-3">Example 2: useAsyncErrorHandler Hook</h3>
          <p className="text-sm text-gray-600 mb-4">
            This example uses a custom hook to handle async operations and errors.
          </p>
          <button
            onClick={async () => {
              const result = await handleAsyncWithHook();
              if (result) {
                setResult(result);
              }
            }}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          >
            Trigger Async Success (Hook)
          </button>
          {hookError && (
            <p className="mt-2 text-red-500">Hook Error: {hookError.message}</p>
          )}
        </div>
        
        <div className="p-4 border rounded-lg">
          <h3 className="font-semibold mb-3">Example 3: Traditional try/catch</h3>
          <p className="text-sm text-gray-600 mb-4">
            This example uses traditional try/catch in an event handler.
          </p>
          <button
            onClick={handleTraditionalTryCatch}
            className="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600"
          >
            Trigger Error (try/catch)
          </button>
        </div>
        
        <div className="p-4 border rounded-lg">
          <h3 className="font-semibold mb-3">Example 4: Successful Operation</h3>
          <p className="text-sm text-gray-600 mb-4">
            This example demonstrates a successful async operation.
          </p>
          <button
            onClick={handleSuccessfulOperation}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Trigger Success
          </button>
        </div>
      </div>
      
      {/* Results display */}
      <div className="mt-6">
        {errorMessage && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg mb-4">
            <h3 className="font-semibold text-red-700 mb-2">Error:</h3>
            <p className="text-red-600">{errorMessage}</p>
          </div>
        )}
        
        {result && (
          <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
            <h3 className="font-semibold text-green-700 mb-2">Result:</h3>
            <p className="text-green-600">{result}</p>
          </div>
        )}
      </div>
      
      <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h3 className="font-semibold text-blue-700 mb-2">Important Notes:</h3>
        <ul className="list-disc list-inside text-blue-600 space-y-1">
          <li>React error boundaries <strong>do not catch</strong> errors in event handlers</li>
          <li>React error boundaries <strong>do not catch</strong> errors in async code (promises, setTimeout, etc.)</li>
          <li>Always use try/catch for async operations and event handlers</li>
          <li>Consider using utility functions or hooks to standardize error handling</li>
          <li>In production, log errors to a monitoring service but show user-friendly messages</li>
        </ul>
      </div>
    </div>
  );
};

/**
 * Wrapper component that adds error boundary
 */
const AsyncErrorHandlingDemoWithBoundary: React.FC = () => {
  return (
    <EnhancedErrorBoundary componentName="AsyncErrorHandlingDemo">
      <AsyncErrorHandlingDemo />
    </EnhancedErrorBoundary>
  );
};

export default AsyncErrorHandlingDemoWithBoundary;
