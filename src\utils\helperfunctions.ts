import axios from "axios";

export const formatDateTime = (date: string) => {
  const options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    hour12: true
  };
  return new Date(date).toLocaleString(undefined, options);
};

export const fetchGameFields = async (userId: string, gameId: string) => {
    try {
      const response = await axios.get(`${process.env.REACT_APP_API_URL}/game-fields/by-user-id/${userId}/${gameId}`, {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          Authorization: `Bearer ${localStorage.getItem('Token')}`,
        },
      });
      const gameFields = response.data.data;
      return gameFields;
    } catch (error) {
      console.error('Error fetching game fields:', error);
      return null;
    }
  };