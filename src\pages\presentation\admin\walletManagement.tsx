import React, { useState, useEffect, useRef } from 'react';
import { toast } from 'react-toastify';
import axios from 'axios';
import { useSelector } from 'react-redux';
import { RootState } from '../../../redux/store';
import { SearchOutlined, UserOutlined, PlusCircleOutlined } from '@ant-design/icons';
import { FaHistory, FaEnvelope } from 'react-icons/fa';
import Loader from '../../../components/loader';

interface User {
  _id: string;
  name: string;
  email: string;
}

// Reusable Tooltip Component
const Tooltip: React.FC<{
  children: React.ReactNode;
  content: string;
  position?: 'top' | 'bottom' | 'left' | 'right';
  delay?: number;
}> = ({ children, content, position = 'top', delay = 200 }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout>();
  const tooltipId = useRef(`tooltip-${Math.random().toString(36).substring(2, 11)}`);

  const handleMouseEnter = () => {
    clearTimeout(timeoutRef.current);
    timeoutRef.current = setTimeout(() => {
      setIsVisible(true);
      setShowTooltip(true);
    }, delay);
  };

  const handleMouseLeave = () => {
    clearTimeout(timeoutRef.current);
    setIsVisible(false);
    setTimeout(() => setShowTooltip(false), 150);
  };

  const handleFocus = () => {
    setIsVisible(true);
    setShowTooltip(true);
  };

  const handleBlur = () => {
    setIsVisible(false);
    setTimeout(() => setShowTooltip(false), 150);
  };

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const getPositionClasses = () => {
    switch (position) {
      case 'bottom':
        return 'top-full left-1/2 transform -translate-x-1/2 mt-2';
      case 'left':
        return 'right-full top-1/2 transform -translate-y-1/2 mr-2';
      case 'right':
        return 'left-full top-1/2 transform -translate-y-1/2 ml-2';
      default: // top
        return 'bottom-full left-1/2 transform -translate-x-1/2 mb-2';
    }
  };

  const getArrowClasses = () => {
    switch (position) {
      case 'bottom':
        return 'bottom-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-gray-900';
      case 'left':
        return 'left-full top-1/2 transform -translate-y-1/2 w-0 h-0 border-t-4 border-b-4 border-l-4 border-transparent border-l-gray-900';
      case 'right':
        return 'right-full top-1/2 transform -translate-y-1/2 w-0 h-0 border-t-4 border-b-4 border-r-4 border-transparent border-r-gray-900';
      default: // top
        return 'top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900';
    }
  };

  return (
    <div className="relative inline-block">
      <div
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onFocus={handleFocus}
        onBlur={handleBlur}
        aria-describedby={isVisible ? tooltipId.current : undefined}
        tabIndex={0}
        className="focus:outline-none focus:ring-2 focus:ring-lime-500 focus:ring-opacity-50 rounded"
      >
        {children}
      </div>

      {showTooltip && (
        <div
          id={tooltipId.current}
          role="tooltip"
          className={`absolute ${getPositionClasses()} px-3 py-2 bg-gray-900 text-white text-sm rounded-lg shadow-lg border border-gray-700 z-[100] transition-opacity duration-200 ${
            isVisible ? 'opacity-100' : 'opacity-0'
          }`}
          style={{
            whiteSpace: 'nowrap',
            pointerEvents: 'none'
          }}
        >
          {content}
          <div className={`absolute ${getArrowClasses()}`}></div>
        </div>
      )}
    </div>
  );
};

const WalletManagement = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [amount, setAmount] = useState<string>('');
  const [reason, setReason] = useState<string>('');
  // Admin will only add gold coins
  const coinType = 'goldCoins';
  const [loading, setLoading] = useState<boolean>(false);
  const [searchResults, setSearchResults] = useState<User[]>([]);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [showResults, setShowResults] = useState<boolean>(false);
  
  const { user: adminUser } = useSelector((state: RootState) => state.auth);
  
  const API_BASE_URL = process.env.REACT_APP_API_URL;
  
  const api = axios.create({
    baseURL: API_BASE_URL,
    headers: {
      'Content-Type': 'application/json',
    },
  });
  
  api.interceptors.request.use((config) => {
    const token = localStorage.getItem('Token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  });

  useEffect(() => {
    fetchUsers();
  }, []);

  useEffect(() => {
    if (searchTerm.trim() === '') {
      setSearchResults([]);
      setShowResults(false);
    } else {
      const filtered = users.filter(user => 
        user.email.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setSearchResults(filtered);
      setShowResults(true);
    }
  }, [searchTerm, users]);

  const fetchUsers = async () => {
    setLoading(true);
    try {
      const response = await api.get('/users');
      setUsers(response.data.data);
    } catch (error) {
      console.error('Error fetching users:', error);
      toast.error('Failed to fetch users');
    } finally {
      setLoading(false);
    }
  };
  
  // Transaction history display removed as requested

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedUser) {
      toast.error('Please select a user');
      return;
    }
    
    if (!amount || isNaN(Number(amount)) || Number(amount) <= 0) {
      toast.error('Please enter a valid amount');
      return;
    }
    
    if (!reason.trim()) {
      toast.error('Please provide a reason');
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      // Use the add-gold-coins endpoint for Gold Coins
      const response = await api.post('/wallet/add-gold-coins', {
        userId: selectedUser._id,
        amount: Number(amount),
        description: reason,
        paymentMethod: 'Manual',
        status: 'Completed'
      });
      
      // Create activity log entry
      const logData = {
        action: 'add_coins',
        entityId: selectedUser._id,
        entityType: 'wallet',
        details: {
          userId: selectedUser._id,
          userName: selectedUser.name || 'Unknown User',
          amount: Number(amount),
          coinType: 'Gold Coins',
          reason,
          adminId: adminUser?._id,
          adminName: adminUser?.name || 'Unknown Admin'
        }
      };
      
      try {
        await api.post('/activity-logs', logData);
      } catch (logError) {
        console.error('Error creating activity log:', logError);
        // Don't show error toast for activity log failure
      }
      
      toast.success(`Successfully added ${amount} Gold Coins to user's wallet`);
      
      // Transaction history display removed as requested
      
      // Reset form
      setSelectedUser(null);
      setSearchTerm('');
      setAmount('');
      setReason('');
      setSearchResults([]);
      setShowResults(false);
      
    } catch (error) {
      console.error('Error adding coins:', error);
      toast.error('Failed to add coins to user wallet');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="w-full bg-white rounded-lg p-4 sm:p-4 md:p-6 lg:fixed lg:right-0 lg:w-[calc(100%-250px)] lg:h-screen lg:overflow-y-auto">
      <div className="lg:flex lg:flex-col lg:h-full">
        {/* Header with title */}
        <div className="flex justify-between items-center mb-4 sm:mb-6 mx-4">
          <h2 className="text-2xl font-bold text-lime-900">Wallet Management</h2>
        </div>
        
        {/* Add Coins Section */}
        <div className="bg-white rounded-lg shadow-md mb-6">
          <div className="p-4 sm:p-6 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-800 flex items-center">
              <PlusCircleOutlined className="mr-2" /> Add Coins to User Wallet
            </h2>
          </div>
        
          <div className="p-4 sm:p-6">
            {loading ? (
              <div className="flex justify-center items-center py-10">
                <Loader size="medium" color="#495e26" />
                <span className="ml-3 text-lime-900 font-medium">Loading users...</span>
              </div>
            ) : (
              <>
                {/* Search functionality removed as requested */}
                
                <form onSubmit={handleSubmit}>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-gray-700 font-medium mb-2">Search User by Email</label>
                      <div className="relative">
                        <input
                          type="text"
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-lime-500 focus:border-lime-500"
                          placeholder="Enter user email"
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                          required={!selectedUser}
                        />
                        {selectedUser && (
                          <div className="mt-2 p-3 bg-green-50 border border-green-200 rounded-lg">
                            <div className="flex justify-between items-center">
                              <div>
                                <p className="font-medium text-gray-800">{selectedUser.name}</p>
                                <p className="text-sm text-gray-600 flex items-center">
                                  <FaEnvelope className="mr-1 text-xs text-blue-600" />
                                  {selectedUser.email}
                                </p>
                              </div>
                              <button 
                                type="button"
                                onClick={() => {
                                  setSelectedUser(null);
                                  setSearchTerm('');
                                }}
                                className="text-gray-500 hover:text-gray-700"
                              >
                                ✕
                              </button>
                            </div>
                          </div>
                        )}
                        {showResults && searchResults.length > 0 && !selectedUser && (
                          <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                            {searchResults.map(user => (
                              <div 
                                key={user._id} 
                                className="p-2 hover:bg-gray-100 cursor-pointer border-b border-gray-100 flex flex-col"
                                onClick={() => {
                                  setSelectedUser(user);
                                  setSearchTerm('');
                                  setShowResults(false);
                                }}
                              >
                                <span className="font-medium">{user.name}</span>
                                <span className="text-sm text-gray-600">{user.email}</span>
                              </div>
                            ))}
                          </div>
                        )}
                        {showResults && searchResults.length === 0 && searchTerm.trim() !== '' && (
                          <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg p-3 text-center">
                            No users found with that email
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <div>
                      <label className="block text-gray-700 font-medium mb-2">Coin Type</label>
                      <input
                        type="text"
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg bg-gray-100 cursor-not-allowed"
                        value="Gold Coins"
                        disabled
                      />
                    </div>
                    
                    <div>
                      <label className="block text-gray-700 font-medium mb-2">Amount</label>
                      <input
                        type="number"
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-lime-500 focus:border-lime-500"
                        placeholder="Enter amount"
                        value={amount}
                        onChange={(e) => setAmount(e.target.value)}
                        min="1"
                        required
                      />
                    </div>
                    
                    <div>
                      <label className="block text-gray-700 font-medium mb-2">Reason</label>
                      <input
                        type="text"
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-lime-500 focus:border-lime-500"
                        placeholder="Enter reason for adding coins"
                        value={reason}
                        onChange={(e) => setReason(e.target.value)}
                        required
                      />
                    </div>
                  </div>
                  
                  <div className="mt-6">
                    <button
                      type="submit"
                      className="bg-lime-900 text-white px-6 py-2 rounded-lg hover:bg-lime-800 transition-colors duration-300 flex items-center"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? (
                        <>
                          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                          Processing...
                        </>
                      ) : (
                        <>
                          <PlusCircleOutlined className="mr-2" />
                          Add Coins
                        </>
                      )}
                    </button>
                  </div>
                </form>
              </>
            )}
          </div>
        </div>
        
        {/* Transaction history and filters removed as requested */}
      </div>
    </div>
  );
};

const WalletManagementComponent = WalletManagement;
export default WalletManagementComponent;
