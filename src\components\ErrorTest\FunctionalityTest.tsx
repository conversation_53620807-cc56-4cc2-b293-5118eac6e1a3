import React, { useState, useEffect } from 'react';

/**
 * A simple component to verify that our error handling doesn't break normal functionality
 */
const FunctionalityTest: React.FC = () => {
  const [count, setCount] = useState(0);
  const [message, setMessage] = useState('');
  
  // This useEffect should work normally
  useEffect(() => {
    // This is a normal effect that shouldn't be affected by our error handling
    setMessage(`Count is ${count}`);
    
    // Log to verify console.log still works
    console.log('Normal useEffect running, count:', count);
    
    // This is a legitimate warning that should still show in development
    if (count > 5) {
      console.warn('Count is getting high!');
    }
  }, [count]);
  
  // Function to handle button click
  const handleClick = () => {
    setCount(count + 1);
  };
  
  // Function to log an error
  const logNormalError = () => {
    try {
      // This is a normal error that should still show in development
      throw new Error('Normal error - this should show in development');
    } catch (error) {
      console.error('Caught error:', error);
    }
  };
  
  return (
    <div className="p-4 border rounded-md">
      <h2 className="text-xl font-semibold mb-4">Functionality Test</h2>
      <p className="mb-4">
        This component tests that normal functionality still works with our error handling.
      </p>
      
      <div className="mb-4">
        <p>Count: {count}</p>
        <p>Message: {message}</p>
      </div>
      
      <div className="flex space-x-4">
        <button
          onClick={handleClick}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Increment Count
        </button>
        
        <button
          onClick={logNormalError}
          className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
        >
          Log Normal Error
        </button>
      </div>
    </div>
  );
};

export default FunctionalityTest;
