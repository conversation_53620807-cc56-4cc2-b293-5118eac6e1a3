# LuckShack eCommerce Platform

![LuckShack Logo](public/logo.png)

## Enterprise Documentation

This document provides comprehensive information for enterprise usage, development, deployment, and governance of the LuckShack eCommerce platform.

[![Build Status](https://img.shields.io/badge/build-passing-brightgreen)]() 
[![Code Coverage](https://img.shields.io/badge/coverage-85%25-green)]() 
[![License](https://img.shields.io/badge/license-Proprietary-blue)]() 
[![Version](https://img.shields.io/badge/version-4.4.1-informational)]() 

## Table of Contents

- [Overview](#overview)
- [Architecture](#architecture)
- [Getting Started](#getting-started)
- [Development Workflow](#development-workflow)
- [Build and Deployment](#build-and-deployment)
- [Quality Assurance](#quality-assurance)
- [Production Configuration](#production-configuration)
- [Operations and Monitoring](#operations-and-monitoring)
- [Security](#security)
- [Support and Maintenance](#support-and-maintenance)
- [Team and Governance](#team-and-governance)
- [License](#license)

## Overview

LuckShack is an enterprise-grade eCommerce platform built on React with modern architectural patterns. The platform provides comprehensive management of products, orders, customers, and payments with a focus on performance, security, and scalability.

### Key Features

- **Progressive Web App (PWA)**: Offline capabilities and mobile-first experience
- **Robust Admin Dashboard**: Comprehensive management tools for operators
- **Advanced Error Tracking**: Integration with Sentry for real-time error monitoring
- **Optimized Performance**: Cache invalidation and request deduplication
- **Enterprise Security**: Secure authentication and authorization flows
- **Analytics Integration**: Comprehensive business intelligence capabilities

## Architecture

The LuckShack platform follows a modern frontend architecture with the following key components:

- **Frontend Framework**: React with TypeScript
- **State Management**: Redux with RTK Query for API integration
- **Styling**: SCSS with modular architecture
- **Routing**: React Router for navigation
- **Performance Monitoring**: Sentry for error tracking and performance metrics
- **Build System**: Webpack (via Create React App) with custom configurations
- **API Integration**: RESTful API consumption with intelligent caching

### System Architecture Diagram

```
┌─────────────────────┐       ┌──────────────────┐
│                     │       │                  │
│  User Interface     │◄─────►│  State Management│
│  (React Components) │       │  (Redux/RTK)     │
│                     │       │                  │
└─────────────────────┘       └──────────────────┘
          ▲                             ▲
          │                             │
          ▼                             ▼
┌─────────────────────┐       ┌──────────────────┐
│                     │       │                  │
│  Routing            │◄─────►│  Services/API    │
│  (React Router)     │       │  (RTK Query)     │
│                     │       │                  │
└─────────────────────┘       └──────────────────┘
                                       ▲
                                       │
                                       ▼
                              ┌──────────────────┐
                              │                  │
                              │  Backend API     │
                              │                  │
                              └──────────────────┘
```

## Getting Started

### Prerequisites

- Node.js 16.x or higher
- npm 7.x or higher (or yarn 1.22.x)
- Git

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/aarishcs2/luckshack-ecommerce.git
   cd luckshack-ecommerce
   ```

2. Install dependencies:
   ```bash
   npm install
   # or using the Makefile
   make install
   ```

3. Set up environment variables:
   ```bash
   cp .env.example .env.local
   ```
   Edit `.env.local` with your specific configuration values.

4. Start the development server:
   ```bash
   npm start
   # or using the Makefile
   make start
   ```

## Development Workflow

### Branch Strategy

We follow a trunk-based development workflow:

- `main` - Production-ready code
- `develop` - Integration branch for features
- `feature/*` - Feature branches
- `hotfix/*` - Urgent fixes for production
- `release/*` - Release candidate branches

### Commit Guidelines

We follow [Conventional Commits](https://www.conventionalcommits.org/) specification:

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

Example: `feat(order): implement optimistic updates for order status`

### Code Style

The codebase follows strict coding standards enforced through ESLint, Prettier, and Stylelint. To format your code:

```bash
# Format all files
npm run format
# or using the Makefile
make format

# Run linters
make lint
```

## Build and Deployment

### Building for Production

```bash
# Build the application
npm run build
# or using the Makefile
make build
```

The build output will be in the `build/` directory.

### Deployment Options

#### Docker Deployment

Build and push a Docker image:

```bash
# Build Docker image
make docker-build

# Push to registry
make docker-push
```

#### CI/CD Pipeline

Our CI/CD pipeline is configured through Coolify and automatically:

1. Runs tests and linting
2. Builds the application
3. Deploys to the appropriate environment based on the branch

## Quality Assurance

### Testing Strategy

- **Unit Testing**: Jest for component and utility tests
- **Integration Testing**: Testing interactions between components
- **End-to-End Testing**: Cypress for full user journey tests

Run tests with:

```bash
npm test
# or using the Makefile
make test
```

### Performance Monitoring

- Sentry for error tracking and performance monitoring
- Google Analytics for user behavior tracking
- Custom performance metrics for critical user paths

## Production Configuration

### Environment Variables

Key environment variables for production deployment:

| Variable | Description | Example |
|----------|-------------|----------|
| `REACT_APP_API_URL` | Backend API URL | `https://api.luckshack.com` |
| `REACT_APP_VERSION` | Application version | `4.4.1` |
| `SENTRY_AUTH_TOKEN` | Sentry authentication token | `abc123xyz` |
| `NODE_ENV` | Environment mode | `production` |

### Security Considerations

- All API requests use HTTPS
- Authentication tokens are stored securely
- Regular security audits and dependency updates
- CSP headers configured to prevent XSS attacks

## Operations and Monitoring

### Health Checks

The application includes built-in health checks accessible at `/health`.

### Logging

- Application logs are sent to Sentry
- Critical errors trigger alerts to the operations team
- Structured logging format for easier analysis

### Backup and Recovery

- Automatic backup of user data
- Disaster recovery plan documented in `docs/disaster-recovery.md`

## Security

### Authentication and Authorization

- JWT-based authentication
- Role-based access control
- Session management with refresh token rotation

### Data Protection

- PII is handled according to GDPR requirements
- Data is encrypted at rest and in transit
- Regular security audits and penetration testing

## Support and Maintenance

### Issue Reporting

For internal issues, use the JIRA project. For external contributors, use GitHub Issues.

### Release Schedule

- Major releases: Quarterly
- Minor releases: Monthly
- Hotfixes: As needed

### Long-term Support

Each major version is supported for 12 months after release.

## Team and Governance

Refer to [GOVERNANCE.md](GOVERNANCE.md) for detailed information about:

- Team structure and roles
- Decision-making processes
- Contribution guidelines
- Code review requirements

## License

This project is licensed under the proprietary SC Craft license - see [LICENSE](LICENSE) for details.

---

© 2025 SC Craft. All rights reserved.

### Yarn

`yarn install`

Inside the newly created project, you can run some built-in commands:

### `npm start` or `yarn start`

Runs the app in development mode.<br>
Open [http://localhost:3000](http://localhost:3000) to view it in the browser.

### Detailed information on how to use it is given at [facit.omtanke.studio](https://facit.omtanke.studio/) or [facit-story.omtanke.studio](https://facit-story.omtanke.studio/). If you have questions, you can get support from us 24/7.
