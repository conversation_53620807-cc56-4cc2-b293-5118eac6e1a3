import React from 'react';
import Next from '../../../icons/next.svg';

const ReviewsSection: React.FC = () => {
  return (
    <section className="mb-12 w-full px-4">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-2xl">Reviews</h2>
        <a
          href="#all-reviews"
          className="text-grey-900 hover:text-grey-700 flex items-center space-x-2"
        >
          <span>Read All Reviews</span>
          <img src={Next} alt="Next icon" className="w-4 h-4" />
        </a>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {[1, 2, 3].map((index) => (
          <div key={index} className="border rounded p-4 shadow-md">
            <p className="mb-4 text-gray-600">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit.
            </p>
            <div className="flex items-center">
              <img
                src={`/avatar-${index}.jpg`}
                alt="Reviewer"
                className="w-10 h-10 rounded-full mr-4"
              />
              <div>
                <p>Reviewer Name</p>
                <p className="text-sm text-gray-500">Reviewer Title</p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </section>
  );
};

export default ReviewsSection;
