import React, { useState, ChangeEvent, FormEvent } from 'react';

interface FormData {
  name: string;
  email: string;
  message: string;
}

const ContactPage: React.FC = () => {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    message: '',
  });

  const handleChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };

  const handleSubmit = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setFormData({ name: '', email: '', message: '' });
  };

  return (
    <div className="min-h-auto bg-white flex flex-col items-center">
      <div className="w-full max-w-4xl px-4 py-8 sm:px-6 md:px-8">
        <div className="max-w-2xl mx-auto text-center">
          <h1 className="text-2xl sm:text-3xl font-bold text-black mb-4">Contact Us</h1>
          <p className="text-sm sm:text-base text-gray-600 mb-8 font-normal">
            Get a question or need some help? Get in touch. we'd love to hear from you.
            <br className="hidden sm:block" />
            Email - <EMAIL> | Phone +****************
          </p>
          
          <div className="p-3 border border-[#495e26] rounded-md inline-block mb-8 bg-[#f8f9f6]">
            <h2 className="text-base sm:text-lg font-bold text-[#495e26] mb-2">Contact Business Address</h2>
            <p className="text-sm sm:text-base font-medium text-[#495e26]">
              <span className="font-bold">Newby Entertainment LLC</span>
              <br />
              810 N W S Young Dr STE 105, Killeen TX 76543
            </p>
          </div>
        </div>

        {/* Form container - centered with responsive width */}
        <div className="max-w-md mx-auto w-full">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-[#495e26]">
                Name
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                required
                className="mt-1 block w-full border border-[#E8E8E8] rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-[#495e26] focus:border-[#495e26] text-sm"
              />
            </div>
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-[#495e26]">
                Email
              </label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                required
                className="mt-1 block w-full border border-[#E8E8E8] rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-[#495e26] focus:border-[#495e26] text-sm"
              />
            </div>
            <div>
              <label htmlFor="message" className="block text-sm font-medium text-[#495e26]">
                Message
              </label>
              <textarea
                id="message"
                name="message"
                rows={4}
                value={formData.message}
                onChange={handleChange}
                required
                className="mt-1 block w-full border border-[#E8E8E8] rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-[#495e26] focus:border-[#495e26] text-sm"
              ></textarea>
            </div>
            <div>
              <button
                type="submit"
                className="w-full bg-[#495e26] text-white py-3 px-4 rounded-md hover:bg-[#3a4c1e] transition duration-300 text-sm font-medium"
              >
                Send Message
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ContactPage;