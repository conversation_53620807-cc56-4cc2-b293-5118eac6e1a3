const moment = require('moment');

/**
 * Reports Validation Middleware
 * Provides validation functions for reports endpoints
 */

/**
 * Validate date range query parameters
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const validateDateRange = (req, res, next) => {
  try {
    const { startDate, endDate } = req.query;

    // Validate startDate format if provided
    if (startDate && !moment(startDate, 'YYYY-MM-DD', true).isValid()) {
      return res.status(400).json({
        success: false,
        message: 'Invalid startDate format. Use YYYY-MM-DD format.',
        error: 'INVALID_DATE_FORMAT'
      });
    }

    // Validate endDate format if provided
    if (endDate && !moment(endDate, 'YYYY-MM-DD', true).isValid()) {
      return res.status(400).json({
        success: false,
        message: 'Invalid endDate format. Use YYYY-MM-DD format.',
        error: 'INVALID_DATE_FORMAT'
      });
    }

    // Validate date range logic
    if (startDate && endDate) {
      const start = moment(startDate);
      const end = moment(endDate);
      
      if (start.isAfter(end)) {
        return res.status(400).json({
          success: false,
          message: 'Start date cannot be after end date.',
          error: 'INVALID_DATE_RANGE'
        });
      }

      // Check if date range is too large (more than 1 year)
      if (end.diff(start, 'days') > 365) {
        return res.status(400).json({
          success: false,
          message: 'Date range cannot exceed 365 days.',
          error: 'DATE_RANGE_TOO_LARGE'
        });
      }
    }

    next();
  } catch (error) {
    console.error('Date validation error:', error);
    res.status(400).json({
      success: false,
      message: 'Date validation failed.',
      error: 'DATE_VALIDATION_ERROR'
    });
  }
};

/**
 * Validate pagination parameters
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const validatePagination = (req, res, next) => {
  try {
    let { page, limit } = req.query;

    // Set defaults
    page = parseInt(page) || 1;
    limit = parseInt(limit) || 50;

    // Validate page number
    if (page < 1) {
      return res.status(400).json({
        success: false,
        message: 'Page number must be greater than 0.',
        error: 'INVALID_PAGE_NUMBER'
      });
    }

    // Validate limit
    if (limit < 1 || limit > 100) {
      return res.status(400).json({
        success: false,
        message: 'Limit must be between 1 and 100.',
        error: 'INVALID_LIMIT'
      });
    }

    // Add validated values back to query
    req.query.page = page;
    req.query.limit = limit;

    next();
  } catch (error) {
    console.error('Pagination validation error:', error);
    res.status(400).json({
      success: false,
      message: 'Pagination validation failed.',
      error: 'PAGINATION_VALIDATION_ERROR'
    });
  }
};

/**
 * Validate groupBy parameter
 * @param {Array} allowedValues - Array of allowed groupBy values
 * @returns {Function} - Express middleware function
 */
const validateGroupBy = (allowedValues = ['date', 'game', 'user']) => {
  return (req, res, next) => {
    try {
      const { groupBy } = req.query;

      if (groupBy && !allowedValues.includes(groupBy)) {
        return res.status(400).json({
          success: false,
          message: `Invalid groupBy value. Allowed values: ${allowedValues.join(', ')}`,
          error: 'INVALID_GROUP_BY'
        });
      }

      next();
    } catch (error) {
      console.error('GroupBy validation error:', error);
      res.status(400).json({
        success: false,
        message: 'GroupBy validation failed.',
        error: 'GROUP_BY_VALIDATION_ERROR'
      });
    }
  };
};

/**
 * Validate user role for reports access
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const validateReportsAccess = (req, res, next) => {
  try {
    // const user = req.user;

    // if (!user || !user.role) {
    //   return res.status(401).json({
    //     success: false,
    //     message: 'Authentication required.',
    //     error: 'AUTHENTICATION_REQUIRED'
    //   });
    // }

    // const allowedRoles = ['Admin', 'Manage'];
    // if (!allowedRoles.includes(user.role)) {
    //   return res.status(403).json({
    //     success: false,
    //     message: 'Access denied. Reports access requires Manage or Admin role.',
    //     error: 'INSUFFICIENT_PERMISSIONS'
    //   });
    // }

    next();
  } catch (error) {
    console.error('Reports access validation error:', error);
    res.status(403).json({
      success: false,
      message: 'Access validation failed.',
      error: 'ACCESS_VALIDATION_ERROR'
    });
  }
};

/**
 * Combined validation middleware for reports endpoints
 * @param {Object} options - Validation options
 * @param {Array} options.allowedGroupBy - Allowed groupBy values
 * @param {boolean} options.requirePagination - Whether pagination is required
 * @returns {Array} - Array of middleware functions
 */
const validateReportsRequest = (options = {}) => {
  const {
    allowedGroupBy = ['date', 'game', 'user'],
    requirePagination = true
  } = options;

  const middlewares = [
    validateReportsAccess,
    validateDateRange
  ];

  if (requirePagination) {
    middlewares.push(validatePagination);
  }

  middlewares.push(validateGroupBy(allowedGroupBy));

  return middlewares;
};

module.exports = {
  validateDateRange,
  validatePagination,
  validateGroupBy,
  validateReportsAccess,
  validateReportsRequest
};
